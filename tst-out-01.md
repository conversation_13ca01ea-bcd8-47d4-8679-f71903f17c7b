 FAIL  server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts (55.92 s, 58 MB heap size)
  GovernanceTrackingBridge Unit Tests
    Service Lifecycle Management
      ✓ should create bridge service instance successfully (6 ms)
      ✓ should initialize bridge service successfully (4 ms)
      ✓ should shutdown bridge service successfully (6 ms)
      ✓ should handle double initialization gracefully (3 ms)
    Bridge Initialization
      ✓ should initialize bridge with valid configuration (4 ms)
      ✓ should reject initialization with invalid configuration (132 ms)
      ✓ should handle bridge initialization timeout gracefully (3 ms)
    Governance Rules Synchronization
      ✓ should synchronize governance rules successfully (3 ms)
      ✓ should handle empty rules array (4 ms)
      ✓ should handle invalid rules gracefully (3 ms)
      ✓ should handle partial synchronization failures (3 ms)
    Tracking Data Forwarding
      ✓ should forward tracking data successfully (8 ms)
      ✓ should validate tracking data before forwarding (4 ms)
      ✓ should handle tracking data transformation (3 ms)
    Cross-System Compliance Validation
      ✓ should validate cross-system compliance successfully (3 ms)
      ✓ should handle validation scope with no systems (2 ms)
    Event Handling
      ✓ should handle governance events successfully (3 ms)
      ✓ should handle tracking events successfully (3 ms)
      ✓ should handle invalid events gracefully (3 ms)
    Health Monitoring
      ✓ should perform health check successfully (3 ms)
      ✓ should get bridge metrics successfully (8 ms)
      ✓ should detect unhealthy systems (4 ms)
    Bridge Diagnostics
      ✓ should perform comprehensive diagnostics (5 ms)
      ✓ should identify system check results (3 ms)
      ✓ should provide actionable recommendations (10 ms)
    Performance Validation
      ✓ should meet <5ms operation requirements (3 ms)
      ✓ should handle concurrent operations efficiently (3 ms)
      ✓ should maintain performance under load (3 ms)
    Resilient Timing Integration
      ✓ should use resilient timing for all operations (2 ms)
      ✓ should collect performance metrics (2 ms)
      ✓ should handle timing failures gracefully (2 ms)
    Memory Safety
      ✓ should not leak memory during repeated operations (4 ms)
      ✓ should clean up resources on shutdown (1 ms)
      ✓ should handle resource exhaustion gracefully (5 ms)
    Error Handling and Recovery
      ✓ should handle network failures gracefully (4 ms)
      ✓ should recover from temporary failures (3 ms)
      ✓ should maintain service health after errors (3 ms)
      ✓ should handle concurrent error scenarios (4 ms)
    Authority Validation
      ✓ should validate authority data in tracking data (2 ms)
      ✓ should handle missing authority data (2 ms)
      ✓ should validate governance rule authority (2 ms)
    Integration Readiness
      ✓ should be ready for integration after initialization (2 ms)
      ✓ should support BaseTrackingService interface (2 ms)
      ✓ should handle service lifecycle correctly (2 ms)
      ✓ should handle bridge validation errors with detailed messages (3 ms)
      ✓ should handle compliance validation when no validators are enabled (8 ms)
      ✓ should handle authority validation with edge cases (2 ms)
      ✓ should handle performance health check errors (2 ms)
      ✓ should handle diagnostic generation errors gracefully (2 ms)
      ✓ should process event queue with batch size limits (2 ms)
      ✓ should handle bridge connection timeout scenarios (1 ms)
      ✓ should handle bridge state transitions correctly (2 ms)
      ✓ should handle complex validation scenarios with multiple rule types (2 ms)
      ✓ should handle specific error types in event processing (2 ms)
      ✓ should collect metrics with various data states (2 ms)
      ✓ should generate comprehensive diagnostics with detailed analysis (4 ms)
      ✓ should handle event queue overflow gracefully (3 ms)
      ✓ should handle missing tracking event handler in production mode (2 ms)
      ✓ should handle tracking event processing errors with detailed error logging (2 ms)
      ✓ should reset governance and tracking connections when configured (2 ms)
      ✓ should handle comprehensive bridge optimization with all areas (2 ms)
      ✓ should manage bridge status with comprehensive state tracking (2 ms)
      ✓ should handle complex event processing error scenarios (2 ms)
      ✓ should calculate metrics with comprehensive edge case handling (2 ms)
      ✓ should hit lines 1933,1938,1943,1948: Configuration validation catch blocks (5 ms)
      ✓ should hit lines 1961-2165: Connection initialization error handling (5 ms)
      ✓ should hit lines 2263-2293: Compliance validator execution paths (2 ms)
      ✓ should hit lines 1966-1990,2011-2165: Constructor failure catch blocks (2 ms)
      ✓ should hit line 2270: Compliance validator violation detection (1 ms)
      ✓ should hit lines 325-424: Early initialization setup error handling (1 ms)
      ✓ should hit lines 480,496-520: Validation method error paths (1 ms)
      ✓ should hit lines 625-711: Processing pipeline failure scenarios (6 ms)
      ✓ should hit lines 984-999,1011: Runtime condition evaluations (2 ms)
      ✓ should hit lines 1246,1330-1331: State transition edge cases (2 ms)
      ✓ should hit lines 311-312: Advanced timing infrastructure failure patterns (9 ms)
      ✓ should hit lines 1680-1887: Deep integration cross-system data flow patterns (3 ms)
      ✓ should hit lines 1966-1990,2016-2040: Connection establishment under realistic network conditions (2 ms)
      ✓ should hit lines 2061-2165: Advanced connection validation and error recovery patterns (3 ms)
      ✓ should hit lines 1330-1331: Comprehensive state machine transition patterns (2 ms)
      ✓ should hit lines 2568,2620,2710,2718,2876: Runtime error injection patterns (2 ms)
      ✓ should achieve comprehensive private method coverage through strategic access patterns (3 ms)
      ✓ should hit lines 1687-1721,1765-1887: Ultra-deep integration data processing patterns (2 ms)
      ✓ should hit lines 1011,1246: Ultra-precise runtime condition targeting (2 ms)
      ✓ should hit lines 2061-2165: Ultra-advanced connection management patterns (4 ms)
      ✓ should hit lines 325-424: Ultra-precision early initialization error handling (2 ms)
      ✓ should hit lines 480,496-520: Ultra-precision validation method error paths (2 ms)
      ✓ should hit lines 625-711: Ultra-precision processing pipeline failure scenarios (3 ms)
      ✓ should hit lines 984-999,1011: Ultra-precision runtime condition evaluations (3 ms)
      ✓ should hit lines 1246,1330-1331: Ultra-precision state transition edge cases (3 ms)
      ✓ should hit lines 1687-1721,1765-1887: Ultra-precision deep integration processing (2 ms)
      ✓ should hit lines 2061-2165: Ultra-precision advanced connection management (4 ms)
      ✓ should hit lines 2568,2620,2710,2718,2876,2902: Ultra-precision runtime error injection (5 ms)
    Surgical Precision Coverage Enhancement
      ✓ should cover lines 348-351: doTrack method error handling in forwardTrackingData (3 ms)
      ✕ should cover line 480: test environment detection in _testSafeDelay (10005 ms)
      ✓ should cover lines 497,503,509,515: createSafeInterval calls in non-test environment (2 ms)
      ✓ should cover lines 625-711: initializeBridge error handling (2 ms)
      ✕ should cover line 1246: governance event handler not found in production mode (10007 ms)
      ✓ should cover lines 1330-1331: timer end and return in handleTrackingEvent validation failure (20 ms)
      ✕ should cover line 2620: event processing error logging in _processEventQueue (10008 ms)
      ✕ should cover line 2876: bridge health determination error in getBridgeHealth (10008 ms)
      ✕ should achieve comprehensive coverage through integration scenarios (15010 ms)

  ● GovernanceTrackingBridge Unit Tests › Surgical Precision Coverage Enhancement › should cover line 480: test environment detection in _testSafeDelay

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      3720 |     // TARGET: Line 480 - test environment detection
      3721 |     // ========================================================================
    > 3722 |     test('should cover line 480: test environment detection in _testSafeDelay', async () => {
           |     ^
      3723 |       // Use fake timers to prevent hanging
      3724 |       jest.useFakeTimers();
      3725 |

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3722:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Surgical Precision Coverage Enhancement › should cover line 1246: governance event handler not found in production mode

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      3782 |       expect(result.success).toBe(false);
      3783 |       expect(result.errors).toHaveLength(1);
    > 3784 |       expect(result.errors[0].message).toBe('Validation failed');
           |     ^
      3785 |
      3786 |       (surgicalBridge as any)._validateBridgeConfig = originalValidate;
      3787 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3784:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Surgical Precision Coverage Enhancement › should cover line 2620: event processing error logging in _processEventQueue

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      3840 |
      3841 |       expect(result.success).toBe(false);
    > 3842 |       expect(result.eventId).toBe('unknown');
           |     ^
      3843 |       expect(result.handlerId).toBe('validation-failed');
      3844 |       expect(result.errors).toHaveLength(1);
      3845 |     });

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3842:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Surgical Precision Coverage Enhancement › should cover line 2876: bridge health determination error in getBridgeHealth

    thrown: "Exceeded timeout of 10000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      3877 |
      3878 |         expect(surgicalBridge.handleGovernanceEvent).toHaveBeenCalledWith(governanceEvent);
    > 3879 |       } finally {
           |     ^
      3880 |         surgicalBridge.handleGovernanceEvent = originalHandleGovernance;
      3881 |       }
      3882 |     }, 10000); // 10 second timeout

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3879:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  ● GovernanceTrackingBridge Unit Tests › Surgical Precision Coverage Enhancement › should achieve comprehensive coverage through integration scenarios

    thrown: "Exceeded timeout of 15000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      3915 |         expect(health.overall).toBeDefined();
      3916 |         expect(health.lastCheck).toBeDefined();
    > 3917 |       } finally {
           |     ^
      3918 |         (surgicalBridge as any)._performSystemChecks = originalSystemChecks;
      3919 |       }
      3920 |     }, 10000); // 10 second timeout

      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3917:5
      at server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:3685:3
      at Object.<anonymous> (server/src/platform/integration/core-bridge/__tests__/GovernanceTrackingBridge.test.ts:402:1)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)
      