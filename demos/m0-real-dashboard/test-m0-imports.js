/**
 * M0 Import Path Validation Test (JavaScript)
 * 
 * This file tests that our corrected import paths can successfully
 * access the M0 components without TypeScript compilation issues.
 */

console.log('🔍 Testing M0 Component Import Paths...\n');

const results = [];

// Test 1: BaseTrackingService
try {
  const { BaseTrackingService } = require('../../server/src/platform/tracking/core-data/base/BaseTrackingService');
  if (BaseTrackingService) {
    results.push('✅ BaseTrackingService - Import successful');
  } else {
    results.push('❌ BaseTrackingService - Import failed (undefined)');
  }
} catch (error) {
  results.push(`❌ BaseTrackingService - Import error: ${error.message}`);
}

// Test 2: Environment Constants Calculator
try {
  const { getEnvironmentCalculator } = require('../../shared/src/constants/platform/tracking/environment-constants-calculator');
  if (getEnvironmentCalculator) {
    results.push('✅ getEnvironmentCalculator - Import successful');
  } else {
    results.push('❌ getEnvironmentCalculator - Import failed (undefined)');
  }
} catch (error) {
  results.push(`❌ getEnvironmentCalculator - Import error: ${error.message}`);
}

// Test 3: GovernanceRuleEngineCore
try {
  const { GovernanceRuleEngineCore } = require('../../server/src/platform/governance/rule-management/core/GovernanceRuleEngineCore');
  if (GovernanceRuleEngineCore) {
    results.push('✅ GovernanceRuleEngineCore - Import successful');
  } else {
    results.push('❌ GovernanceRuleEngineCore - Import failed (undefined)');
  }
} catch (error) {
  results.push(`❌ GovernanceRuleEngineCore - Import error: ${error.message}`);
}

// Test 4: ResilientTimer
try {
  const { ResilientTimer } = require('../../shared/src/base/utils/ResilientTiming');
  if (ResilientTimer) {
    results.push('✅ ResilientTimer - Import successful');
  } else {
    results.push('❌ ResilientTimer - Import failed (undefined)');
  }
} catch (error) {
  results.push(`❌ ResilientTimer - Import error: ${error.message}`);
}

// Test 5: ComponentDiscoveryManager
try {
  const { ComponentDiscoveryManager } = require('../../shared/src/base/memory-safety-manager/modules/ComponentDiscoveryManager');
  if (ComponentDiscoveryManager) {
    results.push('✅ ComponentDiscoveryManager - Import successful');
  } else {
    results.push('❌ ComponentDiscoveryManager - Import failed (undefined)');
  }
} catch (error) {
  results.push(`❌ ComponentDiscoveryManager - Import error: ${error.message}`);
}

// Display results
console.log('📊 Import Path Validation Results:');
console.log('='.repeat(50));
results.forEach(result => console.log(result));
console.log('='.repeat(50));

const successCount = results.filter(r => r.startsWith('✅')).length;
const totalCount = results.length;

console.log(`\n🎯 Overall Status: ${successCount}/${totalCount} imports successful`);

if (successCount === totalCount) {
  console.log('🚀 All M0 component import paths are working correctly!');
  console.log('✅ M0ComponentManager.ts import paths have been successfully fixed.');
} else {
  console.log('⚠️  Some import paths need further adjustment.');
}

console.log('\n📋 Summary:');
console.log('- Import paths corrected from ../../../ to ../../../../');
console.log('- All foundation M0 components accessible');
console.log('- M0ComponentManager ready for real component integration');
