/**
 * ============================================================================
 * Next.js Configuration - M0 Real Dashboard
 * ============================================================================
 * 
 * Configures Next.js for genuine M0 component integration by:
 * - Enabling external directory imports from OA Framework
 * - Setting up webpack aliases for clean import paths
 * - Configuring module resolution for server-side M0 components
 * 
 * Author: AI Assistant (Phase 1 Day 2 - Real M0 Integration)
 * Created: 2025-09-05
 * ============================================================================
 */

const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for external directory imports
  experimental: {
    externalDir: true, // Allow imports from outside the project directory
    serverComponentsExternalPackages: [], // Add packages that should be external if needed
  },

  // Configure webpack for M0 component integration
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // Add webpack aliases for clean OA Framework imports
    config.resolve.alias = {
      ...config.resolve.alias,
      // OA Framework Server Components
      '@oa-server': path.resolve(__dirname, '../../server/src'),
      // OA Framework Shared Components  
      '@oa-shared': path.resolve(__dirname, '../../shared/src'),
      // OA Framework Client Components (if needed)
      '@oa-client': path.resolve(__dirname, '../../client/src'),
    };

    // Configure module resolution for better compatibility
    config.resolve.modules = [
      ...config.resolve.modules,
      path.resolve(__dirname, '../../'), // OA Framework root
      path.resolve(__dirname, '../../server/src'),
      path.resolve(__dirname, '../../shared/src'),
    ];

    // Handle potential Node.js module compatibility issues
    if (isServer) {
      // Server-side configuration for M0 components
      config.externals = config.externals || [];
      
      // Allow Node.js built-in modules that M0 components might use
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false, // Disable fs polyfill (use Node.js fs)
        path: false, // Disable path polyfill (use Node.js path)
        crypto: false, // Disable crypto polyfill (use Node.js crypto)
        stream: false, // Disable stream polyfill (use Node.js stream)
        util: false, // Disable util polyfill (use Node.js util)
        os: false, // Disable os polyfill (use Node.js os)
      };
    }

    // Add custom webpack plugins if needed
    config.plugins = config.plugins || [];

    // Enable source maps for better debugging of M0 components
    if (dev) {
      config.devtool = 'source-map';
    }

    return config;
  },

  // Configure TypeScript for better M0 component integration
  typescript: {
    // Allow TypeScript compilation to continue even with some errors during development
    ignoreBuildErrors: false, // Set to true only if needed for development
  },

  // Configure ESLint
  eslint: {
    // Directories to run ESLint on during build
    dirs: ['src'],
    // Don't fail build on ESLint errors during development
    ignoreDuringBuilds: false,
  },

  // Environment variables for M0 integration
  env: {
    M0_INTEGRATION_MODE: 'real', // Flag to indicate real M0 integration
    M0_DASHBOARD_VERSION: '1.1.0', // Version with real integration
  },

  // Configure headers for API routes
  async headers() {
    return [
      {
        source: '/api/m0-components',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'X-M0-Integration',
            value: 'real',
          },
        ],
      },
    ];
  },

  // Configure redirects if needed
  async redirects() {
    return [];
  },

  // Configure rewrites if needed
  async rewrites() {
    return [];
  },

  // Output configuration
  output: 'standalone', // For better deployment compatibility

  // Image optimization (if using Next.js Image component)
  images: {
    domains: [], // Add domains if loading external images
  },

  // Compression
  compress: true,

  // Power by header
  poweredByHeader: false,

  // React strict mode
  reactStrictMode: true,

  // SWC minification
  swcMinify: true,
};

module.exports = nextConfig;
