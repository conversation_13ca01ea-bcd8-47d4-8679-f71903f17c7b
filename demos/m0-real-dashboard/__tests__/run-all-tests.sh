#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - Master Test Runner
# ============================================================================
# 
# This script runs all test suites for the M0 dashboard:
# - API Endpoint Tests
# - Component Integration Tests  
# - Health Check Tests
# - Refresh Functionality Tests
# - Performance Tests
# - Error Handling Tests
#
# Usage:
#   ./run-all-tests.sh                    # Run all tests
#   ./run-all-tests.sh --quick            # Run essential tests only
#   ./run-all-tests.sh --performance      # Run performance tests only
#   ./run-all-tests.sh --help             # Show help
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
TEST_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Test suite tracking
TOTAL_SUITES=0
PASSED_SUITES=0
FAILED_SUITES=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_header() {
    echo -e "${CYAN}${BOLD}$1${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

show_help() {
    echo "M0 Real Component Integration Dashboard - Test Suite"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --quick         Run essential tests only (API, Components, Health)"
    echo "  --performance   Run performance tests only"
    echo "  --api          Run API endpoint tests only"
    echo "  --components   Run component integration tests only"
    echo "  --health       Run health check tests only"
    echo "  --refresh      Run refresh functionality tests only"
    echo "  --errors       Run error handling tests only"
    echo "  --help         Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all test suites"
    echo "  $0 --quick           # Run essential tests"
    echo "  $0 --performance     # Run performance tests"
    echo "  $0 --api --health    # Run specific test suites"
    echo ""
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if server is running
    if curl -s --max-time 5 "$BASE_URL" > /dev/null 2>&1; then
        log_success "M0 dashboard server is running at $BASE_URL"
    else
        log_error "M0 dashboard server is not running at $BASE_URL"
        log_info "Please start the server with: cd demos/m0-real-dashboard && npm run dev"
        exit 1
    fi
    
    # Check required tools
    local required_tools=("curl" "jq")
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_success "Required tool available: $tool"
        else
            log_error "Required tool missing: $tool"
            log_info "Please install $tool to run the tests"
            exit 1
        fi
    done
    
    # Check test scripts exist
    local test_scripts=(
        "api-endpoint-tests.sh"
        "component-integration-tests.sh"
        "health-check-tests.sh"
        "refresh-functionality-tests.sh"
        "performance-tests.sh"
        "error-handling-tests.sh"
    )
    
    for script in "${test_scripts[@]}"; do
        if [[ -x "$TEST_DIR/$script" ]]; then
            log_success "Test script available: $script"
        else
            log_error "Test script missing or not executable: $script"
            exit 1
        fi
    done
}

run_test_suite() {
    local suite_name="$1"
    local script_name="$2"
    local description="$3"
    
    ((TOTAL_SUITES++))
    
    echo ""
    log_header "============================================================================"
    log_header "RUNNING: $suite_name"
    log_header "Description: $description"
    log_header "============================================================================"
    
    local start_time
    start_time=$(date +%s)
    
    if "$TEST_DIR/$script_name"; then
        local end_time
        end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "✅ $suite_name completed successfully in ${duration}s"
        ((PASSED_SUITES++))
        return 0
    else
        local end_time
        end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_error "❌ $suite_name failed after ${duration}s"
        ((FAILED_SUITES++))
        return 1
    fi
}

run_essential_tests() {
    log_header "Running Essential Test Suites (Quick Mode)"
    
    run_test_suite "API Endpoint Tests" "api-endpoint-tests.sh" \
        "Tests GET/POST endpoints, response format, and basic functionality"
    
    run_test_suite "Component Integration Tests" "component-integration-tests.sh" \
        "Tests integration of all 21 M0 components across four categories"
    
    run_test_suite "Health Check Tests" "health-check-tests.sh" \
        "Tests health checking functionality and EnvironmentConstantsCalculator fix"
}

run_all_tests() {
    log_header "Running Complete Test Suite"
    
    run_test_suite "API Endpoint Tests" "api-endpoint-tests.sh" \
        "Tests GET/POST endpoints, response format, and basic functionality"
    
    run_test_suite "Component Integration Tests" "component-integration-tests.sh" \
        "Tests integration of all 21 M0 components across four categories"
    
    run_test_suite "Health Check Tests" "health-check-tests.sh" \
        "Tests health checking functionality and EnvironmentConstantsCalculator fix"
    
    run_test_suite "Refresh Functionality Tests" "refresh-functionality-tests.sh" \
        "Tests manual refresh functionality that was recently fixed"
    
    run_test_suite "Performance Tests" "performance-tests.sh" \
        "Tests API response times, load handling, and performance metrics"
    
    run_test_suite "Error Handling Tests" "error-handling-tests.sh" \
        "Tests error scenarios, graceful degradation, and recovery mechanisms"
}

print_final_summary() {
    echo ""
    log_header "============================================================================"
    log_header "FINAL TEST SUMMARY"
    log_header "============================================================================"
    
    echo "Test Suites Run: $TOTAL_SUITES"
    echo "Test Suites Passed: $PASSED_SUITES"
    echo "Test Suites Failed: $FAILED_SUITES"
    
    if [[ $FAILED_SUITES -eq 0 ]]; then
        echo ""
        log_success "🎉 ALL TEST SUITES PASSED! 🎉"
        log_success "The M0 Real Component Integration Dashboard is working perfectly!"
        echo ""
        log_info "Dashboard Status:"
        log_info "✅ 21 M0 Components Integrated (5x expansion)"
        log_info "✅ 100% Health Score (all components healthy)"
        log_info "✅ Refresh Functionality Working"
        log_info "✅ Performance Within Thresholds"
        log_info "✅ Error Handling Robust"
        log_info "✅ Ready for Production Use"
        echo ""
        return 0
    else
        echo ""
        log_error "❌ SOME TEST SUITES FAILED"
        log_error "Please review the failed tests and fix any issues before production deployment."
        echo ""
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    local run_mode="all"
    local specific_tests=()
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --quick)
                run_mode="quick"
                shift
                ;;
            --performance)
                run_mode="performance"
                shift
                ;;
            --api)
                specific_tests+=("api")
                shift
                ;;
            --components)
                specific_tests+=("components")
                shift
                ;;
            --health)
                specific_tests+=("health")
                shift
                ;;
            --refresh)
                specific_tests+=("refresh")
                shift
                ;;
            --errors)
                specific_tests+=("errors")
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Print header
    echo ""
    log_header "============================================================================"
    log_header "M0 REAL COMPONENT INTEGRATION DASHBOARD - TEST SUITE"
    log_header "============================================================================"
    log_header "Comprehensive testing of the expanded M0 dashboard with 21 components"
    log_header "Testing recent fixes: Refresh functionality & EnvironmentConstantsCalculator"
    log_header "============================================================================"
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Run tests based on mode
    if [[ ${#specific_tests[@]} -gt 0 ]]; then
        log_header "Running Specific Test Suites"
        
        for test in "${specific_tests[@]}"; do
            case $test in
                "api")
                    run_test_suite "API Endpoint Tests" "api-endpoint-tests.sh" \
                        "Tests GET/POST endpoints, response format, and basic functionality"
                    ;;
                "components")
                    run_test_suite "Component Integration Tests" "component-integration-tests.sh" \
                        "Tests integration of all 21 M0 components across four categories"
                    ;;
                "health")
                    run_test_suite "Health Check Tests" "health-check-tests.sh" \
                        "Tests health checking functionality and EnvironmentConstantsCalculator fix"
                    ;;
                "refresh")
                    run_test_suite "Refresh Functionality Tests" "refresh-functionality-tests.sh" \
                        "Tests manual refresh functionality that was recently fixed"
                    ;;
                "errors")
                    run_test_suite "Error Handling Tests" "error-handling-tests.sh" \
                        "Tests error scenarios, graceful degradation, and recovery mechanisms"
                    ;;
            esac
        done
        
    elif [[ "$run_mode" == "quick" ]]; then
        run_essential_tests
        
    elif [[ "$run_mode" == "performance" ]]; then
        run_test_suite "Performance Tests" "performance-tests.sh" \
            "Tests API response times, load handling, and performance metrics"
            
    else
        run_all_tests
    fi
    
    # Print final summary and exit with appropriate code
    if print_final_summary; then
        exit 0
    else
        exit 1
    fi
}

# Run main function with all arguments
main "$@"
