#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - Error Handling Tests
# ============================================================================
# 
# This script tests error scenarios and graceful degradation:
# - Server unavailable scenarios
# - Malformed requests and responses
# - Component failure simulation
# - Network timeout handling
# - Invalid endpoint access
# - Error response format validation
# - Recovery mechanisms
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"
INVALID_ENDPOINT="/api/invalid-endpoint"
TIMEOUT=10  # Shorter timeout for error tests

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

run_test() {
    local test_name="$1"
    ((TESTS_RUN++))
    log_info "Running test: $test_name"
}

# Make request with error handling
make_request_with_error_handling() {
    local method="$1"
    local url="$2"
    local data="$3"
    local expected_code="$4"
    
    local response
    local http_code
    
    if [[ "$method" == "GET" ]]; then
        response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT "$url" 2>/dev/null || echo -e "\nERROR")
    elif [[ "$method" == "POST" ]]; then
        response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url" 2>/dev/null || echo -e "\nERROR")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "$http_code:$response_body"
}

# ============================================================================
# ERROR HANDLING TESTS
# ============================================================================

test_invalid_endpoint() {
    run_test "Invalid Endpoint Access"
    
    local result
    result=$(make_request_with_error_handling "GET" "$BASE_URL$INVALID_ENDPOINT" "" "404")
    
    local http_code
    http_code=$(echo "$result" | cut -d: -f1)
    
    if [[ "$http_code" == "404" ]]; then
        log_success "Invalid endpoint returned HTTP 404"
    else
        log_error "Invalid endpoint returned HTTP $http_code, expected 404"
        return 1
    fi
}

test_invalid_http_method() {
    run_test "Invalid HTTP Method"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X DELETE \
        "$BASE_URL$API_ENDPOINT" 2>/dev/null || echo -e "\n405")
    
    http_code=$(echo "$response" | tail -n1)
    
    if [[ "$http_code" == "405" ]] || [[ "$http_code" == "404" ]]; then
        log_success "Invalid HTTP method returned appropriate error: HTTP $http_code"
    else
        log_error "Invalid HTTP method returned HTTP $http_code, expected 405 or 404"
        return 1
    fi
}

test_malformed_json_request() {
    run_test "Malformed JSON Request"
    
    local result
    result=$(make_request_with_error_handling "POST" "$BASE_URL$API_ENDPOINT" '{"action":invalid}' "400")
    
    local http_code
    http_code=$(echo "$result" | cut -d: -f1)
    
    if [[ "$http_code" == "400" ]] || [[ "$http_code" == "500" ]]; then
        log_success "Malformed JSON returned appropriate error: HTTP $http_code"
    else
        log_error "Malformed JSON returned HTTP $http_code, expected 400 or 500"
        return 1
    fi
}

test_empty_request_body() {
    run_test "Empty Request Body"
    
    local result
    result=$(make_request_with_error_handling "POST" "$BASE_URL$API_ENDPOINT" "" "400")
    
    local http_code
    http_code=$(echo "$result" | cut -d: -f1)
    
    if [[ "$http_code" == "400" ]] || [[ "$http_code" == "500" ]]; then
        log_success "Empty request body returned appropriate error: HTTP $http_code"
    else
        log_error "Empty request body returned HTTP $http_code, expected 400 or 500"
        return 1
    fi
}

test_oversized_request() {
    run_test "Oversized Request Body"
    
    # Create a large JSON payload (1MB of data)
    local large_data='{"action":"refresh","data":"'
    for i in {1..10000}; do
        large_data+="abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefghijklmnopqrstuvwxyz1234567890"
    done
    large_data+='"}'
    
    local result
    result=$(make_request_with_error_handling "POST" "$BASE_URL$API_ENDPOINT" "$large_data" "413")
    
    local http_code
    http_code=$(echo "$result" | cut -d: -f1)
    
    if [[ "$http_code" == "413" ]] || [[ "$http_code" == "400" ]] || [[ "$http_code" == "500" ]]; then
        log_success "Oversized request handled appropriately: HTTP $http_code"
    else
        log_warning "Oversized request returned HTTP $http_code (server may not have size limits)"
    fi
}

test_invalid_content_type() {
    run_test "Invalid Content-Type Header"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: text/plain" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT" 2>/dev/null || echo -e "\n400")
    
    http_code=$(echo "$response" | tail -n1)
    
    if [[ "$http_code" == "400" ]] || [[ "$http_code" == "415" ]] || [[ "$http_code" == "200" ]]; then
        log_success "Invalid Content-Type handled appropriately: HTTP $http_code"
    else
        log_error "Invalid Content-Type returned unexpected HTTP $http_code"
        return 1
    fi
}

test_network_timeout_simulation() {
    run_test "Network Timeout Simulation"
    
    # Use a very short timeout to simulate network issues
    local response
    local exit_code
    
    response=$(curl -s --max-time 0.001 "$BASE_URL$API_ENDPOINT" 2>/dev/null)
    exit_code=$?
    
    if [[ $exit_code -ne 0 ]]; then
        log_success "Network timeout handled gracefully (curl exit code: $exit_code)"
    else
        log_warning "Network timeout test may not have triggered (response received)"
    fi
}

test_server_unavailable() {
    run_test "Server Unavailable Scenario"
    
    # Try to connect to a non-existent port
    local invalid_url="http://localhost:9999$API_ENDPOINT"
    
    local response
    local exit_code
    
    response=$(curl -s --max-time 2 "$invalid_url" 2>/dev/null)
    exit_code=$?
    
    if [[ $exit_code -ne 0 ]]; then
        log_success "Server unavailable scenario handled gracefully (curl exit code: $exit_code)"
    else
        log_error "Server unavailable test failed - unexpected response received"
        return 1
    fi
}

test_error_response_format() {
    run_test "Error Response Format Validation"
    
    # Make a request that should return an error
    local response
    response=$(curl -s --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"invalid"}' \
        "$BASE_URL$API_ENDPOINT" 2>/dev/null || echo '{"error":"request_failed"}')
    
    # Check if response is valid JSON
    if echo "$response" | jq . > /dev/null 2>&1; then
        log_success "Error response is valid JSON"
    else
        log_error "Error response is not valid JSON: $response"
        return 1
    fi
    
    # Check for error indicators
    if echo "$response" | jq -e '.success == false' > /dev/null 2>&1; then
        log_success "Error response contains success: false"
    elif echo "$response" | jq -e '.error' > /dev/null 2>&1; then
        log_success "Error response contains error field"
    else
        log_warning "Error response format may not follow expected pattern"
    fi
}

test_concurrent_error_requests() {
    run_test "Concurrent Error Requests"
    
    local pids=()
    local temp_files=()
    
    # Launch multiple invalid requests concurrently
    for i in {1..5}; do
        local temp_file="/tmp/error_test_$i.out"
        temp_files+=("$temp_file")
        
        (
            curl -s --max-time $TIMEOUT \
                -X POST \
                -H "Content-Type: application/json" \
                -d '{"action":"invalid"}' \
                "$BASE_URL$API_ENDPOINT" > "$temp_file" 2>&1
        ) &
        pids+=($!)
    done
    
    # Wait for all requests to complete
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # Check that all requests received responses
    local response_count=0
    for temp_file in "${temp_files[@]}"; do
        if [[ -s "$temp_file" ]]; then
            ((response_count++))
        fi
        rm -f "$temp_file"
    done
    
    if [[ $response_count -eq 5 ]]; then
        log_success "All concurrent error requests received responses"
    else
        log_error "Only $response_count out of 5 concurrent error requests received responses"
        return 1
    fi
}

test_recovery_after_errors() {
    run_test "Recovery After Error Requests"
    
    # Make several error requests
    for i in {1..3}; do
        curl -s --max-time $TIMEOUT \
            -X POST \
            -H "Content-Type: application/json" \
            -d '{"action":"invalid"}' \
            "$BASE_URL$API_ENDPOINT" > /dev/null 2>&1
    done
    
    # Now make a valid request
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    http_code=$(echo "$response" | tail -n1)
    
    if [[ "$http_code" == "200" ]]; then
        log_success "Server recovered successfully after error requests"
    else
        log_error "Server did not recover properly after error requests: HTTP $http_code"
        return 1
    fi
    
    # Verify the response is still valid
    local response_body
    response_body=$(echo "$response" | head -n -1)
    
    if echo "$response_body" | jq -e '.success == true' > /dev/null 2>&1; then
        log_success "Valid response received after recovery"
    else
        log_error "Invalid response received after recovery"
        return 1
    fi
}

test_error_logging_and_monitoring() {
    run_test "Error Logging and Monitoring"
    
    # Make an error request
    curl -s --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"invalid"}' \
        "$BASE_URL$API_ENDPOINT" > /dev/null 2>&1
    
    # Check if the system still reports correct metrics
    local response
    response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    if echo "$response" | jq -e '.data.systemMetrics' > /dev/null 2>&1; then
        log_success "System metrics still available after error"
    else
        log_error "System metrics unavailable after error"
        return 1
    fi
    
    # Check if error rate is being tracked
    local error_rate
    error_rate=$(echo "$response" | jq -r '.data.systemMetrics.errorRate')
    
    if [[ "$error_rate" =~ ^[0-9]+\.?[0-9]*$ ]]; then
        log_success "Error rate is being tracked: ${error_rate}%"
    else
        log_warning "Error rate tracking may not be working: $error_rate"
    fi
}

test_graceful_degradation() {
    run_test "Graceful Degradation"
    
    # Test that the system continues to function even with some errors
    local valid_requests=0
    local total_requests=10
    
    for i in $(seq 1 $total_requests); do
        local response
        local http_code
        
        if [[ $((i % 3)) -eq 0 ]]; then
            # Every third request is invalid
            response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
                -X POST \
                -H "Content-Type: application/json" \
                -d '{"action":"invalid"}' \
                "$BASE_URL$API_ENDPOINT" 2>/dev/null || echo -e "\n400")
        else
            # Valid request
            response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT" 2>/dev/null || echo -e "\n500")
            http_code=$(echo "$response" | tail -n1)
            
            if [[ "$http_code" == "200" ]]; then
                ((valid_requests++))
            fi
        fi
    done
    
    local success_rate=$((valid_requests * 100 / (total_requests * 2 / 3)))  # Only count valid requests
    
    if [[ $success_rate -ge 90 ]]; then
        log_success "Graceful degradation: ${success_rate}% success rate with mixed requests"
    else
        log_error "Poor graceful degradation: ${success_rate}% success rate (should be >= 90%)"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    echo "============================================================================"
    echo "M0 Real Component Integration Dashboard - Error Handling Tests"
    echo "============================================================================"
    echo ""
    
    # Note: We don't check if server is running for error tests
    # Some tests specifically test server unavailable scenarios
    
    # Run all tests
    test_invalid_endpoint || true
    test_invalid_http_method || true
    test_malformed_json_request || true
    test_empty_request_body || true
    test_oversized_request || true
    test_invalid_content_type || true
    test_network_timeout_simulation || true
    test_server_unavailable || true
    test_error_response_format || true
    test_concurrent_error_requests || true
    test_recovery_after_errors || true
    test_error_logging_and_monitoring || true
    test_graceful_degradation || true
    
    # Print summary
    echo ""
    echo "============================================================================"
    echo "TEST SUMMARY"
    echo "============================================================================"
    echo "Tests Run: $TESTS_RUN"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All error handling tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some error handling tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
