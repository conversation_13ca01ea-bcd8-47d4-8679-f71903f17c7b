#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - API Endpoint Tests
# ============================================================================
# 
# This script tests all API endpoints for the M0 dashboard including:
# - GET /api/m0-components (fetch component data)
# - POST /api/m0-components (refresh functionality)
# - Response format validation
# - Error handling scenarios
# - Performance validation
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"
TIMEOUT=30
EXPECTED_COMPONENTS=21

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

run_test() {
    local test_name="$1"
    ((TESTS_RUN++))
    log_info "Running test: $test_name"
}

# Check if server is running
check_server() {
    log_info "Checking if M0 dashboard server is running..."
    if curl -s --max-time 5 "$BASE_URL" > /dev/null 2>&1; then
        log_success "Server is running at $BASE_URL"
        return 0
    else
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: cd demos/m0-real-dashboard && npm run dev"
        exit 1
    fi
}

# ============================================================================
# API ENDPOINT TESTS
# ============================================================================

test_get_endpoint_success() {
    run_test "GET /api/m0-components - Success Case"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "200" ]]; then
        log_success "GET endpoint returned HTTP 200"
    else
        log_error "GET endpoint returned HTTP $http_code, expected 200"
        return 1
    fi
    
    # Validate JSON structure
    if echo "$response_body" | jq -e '.success' > /dev/null 2>&1; then
        log_success "Response contains 'success' field"
    else
        log_error "Response missing 'success' field"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.data' > /dev/null 2>&1; then
        log_success "Response contains 'data' field"
    else
        log_error "Response missing 'data' field"
        return 1
    fi
    
    # Validate component count
    local component_count
    component_count=$(echo "$response_body" | jq -r '.data.totalComponents')
    
    if [[ "$component_count" == "$EXPECTED_COMPONENTS" ]]; then
        log_success "Component count is correct: $component_count"
    else
        log_error "Component count is $component_count, expected $EXPECTED_COMPONENTS"
        return 1
    fi
}

test_post_endpoint_success() {
    run_test "POST /api/m0-components - Refresh Success Case"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "200" ]]; then
        log_success "POST refresh endpoint returned HTTP 200"
    else
        log_error "POST refresh endpoint returned HTTP $http_code, expected 200"
        echo "Response: $response_body"
        return 1
    fi
    
    # Validate refresh response structure
    if echo "$response_body" | jq -e '.success == true' > /dev/null 2>&1; then
        log_success "Refresh response indicates success"
    else
        log_error "Refresh response does not indicate success"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.data.totalComponents' > /dev/null 2>&1; then
        log_success "Refresh response contains component data"
    else
        log_error "Refresh response missing component data"
        return 1
    fi
}

test_post_endpoint_invalid_action() {
    run_test "POST /api/m0-components - Invalid Action"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"invalid"}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "400" ]]; then
        log_success "POST with invalid action returned HTTP 400"
    else
        log_error "POST with invalid action returned HTTP $http_code, expected 400"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.success == false' > /dev/null 2>&1; then
        log_success "Invalid action response indicates failure"
    else
        log_error "Invalid action response should indicate failure"
        return 1
    fi
}

test_post_endpoint_malformed_json() {
    run_test "POST /api/m0-components - Malformed JSON"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    
    if [[ "$http_code" == "400" ]] || [[ "$http_code" == "500" ]]; then
        log_success "POST with malformed JSON returned appropriate error code: $http_code"
    else
        log_error "POST with malformed JSON returned HTTP $http_code, expected 400 or 500"
        return 1
    fi
}

test_response_format_validation() {
    run_test "Response Format Validation"
    
    local response
    response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    # Test required fields
    local required_fields=("success" "data" "timestamp")
    for field in "${required_fields[@]}"; do
        if echo "$response" | jq -e ".$field" > /dev/null 2>&1; then
            log_success "Response contains required field: $field"
        else
            log_error "Response missing required field: $field"
            return 1
        fi
    done
    
    # Test data structure
    local data_fields=("totalComponents" "healthyComponents" "categories" "systemMetrics")
    for field in "${data_fields[@]}"; do
        if echo "$response" | jq -e ".data.$field" > /dev/null 2>&1; then
            log_success "Data contains required field: $field"
        else
            log_error "Data missing required field: $field"
            return 1
        fi
    done
    
    # Test categories structure
    local categories=("governance" "tracking" "memorySafety" "integration")
    for category in "${categories[@]}"; do
        if echo "$response" | jq -e ".data.categories.$category" > /dev/null 2>&1; then
            log_success "Categories contains: $category"
        else
            log_error "Categories missing: $category"
            return 1
        fi
    done
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    echo "============================================================================"
    echo "M0 Real Component Integration Dashboard - API Endpoint Tests"
    echo "============================================================================"
    echo ""
    
    # Check prerequisites
    check_server
    
    # Run all tests
    test_get_endpoint_success || true
    test_post_endpoint_success || true
    test_post_endpoint_invalid_action || true
    test_post_endpoint_malformed_json || true
    test_response_format_validation || true
    
    # Print summary
    echo ""
    echo "============================================================================"
    echo "TEST SUMMARY"
    echo "============================================================================"
    echo "Tests Run: $TESTS_RUN"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
