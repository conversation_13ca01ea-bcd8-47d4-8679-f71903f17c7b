#!/bin/bash

# Simple test to verify the framework works
set -e

BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"

echo "Testing API endpoint..."

# Test GET request
response=$(curl -s --max-time 10 "$BASE_URL$API_ENDPOINT")
echo "Response received"

# Test JSON parsing
total_components=$(echo "$response" | jq -r '.data.totalComponents')
echo "Total components: $total_components"

if [[ "$total_components" == "21" ]]; then
    echo "✅ Test passed: Found 21 components"
    exit 0
else
    echo "❌ Test failed: Expected 21 components, got $total_components"
    exit 1
fi
