#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - Refresh Functionality Tests
# ============================================================================
# 
# This script tests the manual refresh functionality that was recently fixed:
# - POST /api/m0-components with {"action":"refresh"} payload
# - Response format validation
# - Data consistency after refresh
# - Error handling for invalid refresh requests
# - Performance of refresh operations
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"
TIMEOUT=30
MAX_REFRESH_TIME=5000  # 5 seconds max for refresh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

run_test() {
    local test_name="$1"
    ((TESTS_RUN++))
    log_info "Running test: $test_name"
}

# Get current timestamp in milliseconds
get_timestamp_ms() {
    date +%s%3N
}

# Perform refresh request and measure time
refresh_with_timing() {
    local start_time
    start_time=$(get_timestamp_ms)
    
    local response
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    local end_time
    end_time=$(get_timestamp_ms)
    
    local duration=$((end_time - start_time))
    
    echo "$response"
    echo "TIMING:$duration"
}

# ============================================================================
# REFRESH FUNCTIONALITY TESTS
# ============================================================================

test_basic_refresh_functionality() {
    run_test "Basic Refresh Functionality"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "200" ]]; then
        log_success "Refresh request returned HTTP 200"
    else
        log_error "Refresh request returned HTTP $http_code, expected 200"
        echo "Response: $response_body"
        return 1
    fi
    
    # Validate response structure
    if echo "$response_body" | jq -e '.success == true' > /dev/null 2>&1; then
        log_success "Refresh response indicates success"
    else
        log_error "Refresh response does not indicate success"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.data' > /dev/null 2>&1; then
        log_success "Refresh response contains data"
    else
        log_error "Refresh response missing data"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.timestamp' > /dev/null 2>&1; then
        log_success "Refresh response contains timestamp"
    else
        log_error "Refresh response missing timestamp"
        return 1
    fi
}

test_refresh_data_consistency() {
    run_test "Refresh Data Consistency"
    
    # Get initial data via GET
    local initial_data
    initial_data=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT" | jq '.data')
    
    local initial_total
    initial_total=$(echo "$initial_data" | jq -r '.totalComponents')
    
    # Perform refresh
    local refresh_response
    refresh_response=$(curl -s --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    local refresh_data
    refresh_data=$(echo "$refresh_response" | jq '.data')
    
    local refresh_total
    refresh_total=$(echo "$refresh_data" | jq -r '.totalComponents')
    
    if [[ "$refresh_total" == "$initial_total" ]]; then
        log_success "Component count consistent after refresh: $refresh_total"
    else
        log_error "Component count changed after refresh: $initial_total -> $refresh_total"
        return 1
    fi
    
    # Test that all categories are present
    local categories=("governance" "tracking" "memorySafety" "integration")
    for category in "${categories[@]}"; do
        if echo "$refresh_data" | jq -e ".categories.$category" > /dev/null 2>&1; then
            log_success "Category $category present after refresh"
        else
            log_error "Category $category missing after refresh"
            return 1
        fi
    done
}

test_refresh_performance() {
    run_test "Refresh Performance"
    
    local response_with_timing
    response_with_timing=$(refresh_with_timing)
    
    local timing_line
    timing_line=$(echo "$response_with_timing" | grep "TIMING:")
    
    local duration
    duration=$(echo "$timing_line" | cut -d: -f2)
    
    if [[ "$duration" =~ ^[0-9]+$ ]] && [[ "$duration" -lt "$MAX_REFRESH_TIME" ]]; then
        log_success "Refresh completed in ${duration}ms (< ${MAX_REFRESH_TIME}ms)"
    else
        log_error "Refresh took ${duration}ms (should be < ${MAX_REFRESH_TIME}ms)"
        return 1
    fi
    
    # Verify response is still valid despite timing measurement
    local response_body
    response_body=$(echo "$response_with_timing" | head -n -2)  # Remove timing and HTTP code
    
    if echo "$response_body" | jq -e '.success == true' > /dev/null 2>&1; then
        log_success "Refresh response valid despite performance measurement"
    else
        log_error "Refresh response corrupted during performance measurement"
        return 1
    fi
}

test_refresh_invalid_action() {
    run_test "Refresh with Invalid Action"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"invalid"}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ "$http_code" == "400" ]]; then
        log_success "Invalid action returned HTTP 400"
    else
        log_error "Invalid action returned HTTP $http_code, expected 400"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.success == false' > /dev/null 2>&1; then
        log_success "Invalid action response indicates failure"
    else
        log_error "Invalid action response should indicate failure"
        return 1
    fi
    
    if echo "$response_body" | jq -e '.error' > /dev/null 2>&1; then
        log_success "Invalid action response contains error message"
    else
        log_error "Invalid action response missing error message"
        return 1
    fi
}

test_refresh_missing_action() {
    run_test "Refresh with Missing Action"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    
    if [[ "$http_code" == "400" ]]; then
        log_success "Missing action returned HTTP 400"
    else
        log_error "Missing action returned HTTP $http_code, expected 400"
        return 1
    fi
}

test_refresh_malformed_json() {
    run_test "Refresh with Malformed JSON"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    
    if [[ "$http_code" == "400" ]] || [[ "$http_code" == "500" ]]; then
        log_success "Malformed JSON returned appropriate error code: $http_code"
    else
        log_error "Malformed JSON returned HTTP $http_code, expected 400 or 500"
        return 1
    fi
}

test_refresh_without_content_type() {
    run_test "Refresh without Content-Type Header"
    
    local response
    local http_code
    
    response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
        -X POST \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    http_code=$(echo "$response" | tail -n1)
    
    # Should still work or return appropriate error
    if [[ "$http_code" == "200" ]] || [[ "$http_code" == "400" ]] || [[ "$http_code" == "415" ]]; then
        log_success "Request without Content-Type handled appropriately: HTTP $http_code"
    else
        log_error "Request without Content-Type returned unexpected HTTP $http_code"
        return 1
    fi
}

test_multiple_refresh_requests() {
    run_test "Multiple Consecutive Refresh Requests"
    
    local success_count=0
    local total_requests=3
    
    for i in $(seq 1 $total_requests); do
        local response
        local http_code
        
        response=$(curl -s -w "\n%{http_code}" --max-time $TIMEOUT \
            -X POST \
            -H "Content-Type: application/json" \
            -d '{"action":"refresh"}' \
            "$BASE_URL$API_ENDPOINT")
        
        http_code=$(echo "$response" | tail -n1)
        
        if [[ "$http_code" == "200" ]]; then
            ((success_count++))
        fi
        
        # Small delay between requests
        sleep 0.1
    done
    
    if [[ "$success_count" == "$total_requests" ]]; then
        log_success "All $total_requests consecutive refresh requests succeeded"
    else
        log_error "Only $success_count out of $total_requests refresh requests succeeded"
        return 1
    fi
}

test_refresh_response_format() {
    run_test "Refresh Response Format Validation"
    
    local response
    response=$(curl -s --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    # Test required top-level fields
    local required_fields=("success" "data" "timestamp")
    for field in "${required_fields[@]}"; do
        if echo "$response" | jq -e ".$field" > /dev/null 2>&1; then
            log_success "Refresh response contains required field: $field"
        else
            log_error "Refresh response missing required field: $field"
            return 1
        fi
    done
    
    # Test data structure matches GET response format
    local data_fields=("totalComponents" "healthyComponents" "categories" "systemMetrics")
    for field in "${data_fields[@]}"; do
        if echo "$response" | jq -e ".data.$field" > /dev/null 2>&1; then
            log_success "Refresh data contains required field: $field"
        else
            log_error "Refresh data missing required field: $field"
            return 1
        fi
    done
    
    # Test that refresh response includes success message
    if echo "$response" | jq -e '.message' > /dev/null 2>&1; then
        local message
        message=$(echo "$response" | jq -r '.message')
        log_success "Refresh response includes message: $message"
    else
        log_error "Refresh response missing success message"
        return 1
    fi
}

test_refresh_timestamp_update() {
    run_test "Refresh Timestamp Update"
    
    # Get initial timestamp
    local initial_response
    initial_response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    local initial_timestamp
    initial_timestamp=$(echo "$initial_response" | jq -r '.timestamp')
    
    # Wait a moment to ensure timestamp difference
    sleep 1
    
    # Perform refresh
    local refresh_response
    refresh_response=$(curl -s --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT")
    
    local refresh_timestamp
    refresh_timestamp=$(echo "$refresh_response" | jq -r '.timestamp')
    
    if [[ "$refresh_timestamp" != "$initial_timestamp" ]]; then
        log_success "Refresh updated timestamp: $initial_timestamp -> $refresh_timestamp"
    else
        log_error "Refresh did not update timestamp: $refresh_timestamp"
        return 1
    fi
    
    # Verify timestamp format (ISO 8601)
    if [[ "$refresh_timestamp" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}Z$ ]]; then
        log_success "Refresh timestamp has valid ISO 8601 format"
    else
        log_error "Refresh timestamp has invalid format: $refresh_timestamp"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    echo "============================================================================"
    echo "M0 Real Component Integration Dashboard - Refresh Functionality Tests"
    echo "============================================================================"
    echo ""
    
    # Check if server is running
    if ! curl -s --max-time 5 "$BASE_URL" > /dev/null 2>&1; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: cd demos/m0-real-dashboard && npm run dev"
        exit 1
    fi
    
    # Run all tests
    test_basic_refresh_functionality || true
    test_refresh_data_consistency || true
    test_refresh_performance || true
    test_refresh_invalid_action || true
    test_refresh_missing_action || true
    test_refresh_malformed_json || true
    test_refresh_without_content_type || true
    test_multiple_refresh_requests || true
    test_refresh_response_format || true
    test_refresh_timestamp_update || true
    
    # Print summary
    echo ""
    echo "============================================================================"
    echo "TEST SUMMARY"
    echo "============================================================================"
    echo "Tests Run: $TESTS_RUN"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All refresh functionality tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some refresh functionality tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
