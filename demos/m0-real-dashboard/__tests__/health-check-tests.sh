#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - Health Check Tests
# ============================================================================
# 
# This script tests the health checking functionality for all components:
# - EnvironmentConstantsCalculator health status format fix
# - Health score calculations
# - Overall system health metrics
# - Component status transitions
# - Health check method compatibility
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"
TIMEOUT=30

# Expected health metrics
EXPECTED_TOTAL_COMPONENTS=21
EXPECTED_HEALTHY_COMPONENTS=21
EXPECTED_ERROR_COMPONENTS=0
EXPECTED_HEALTH_SCORE=100

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

run_test() {
    local test_name="$1"
    ((TESTS_RUN++))
    log_info "Running test: $test_name"
}

# Get fresh component data
get_component_data() {
    curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT" | jq '.data'
}

# Refresh component data
refresh_component_data() {
    curl -s --max-time $TIMEOUT \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{"action":"refresh"}' \
        "$BASE_URL$API_ENDPOINT" | jq '.data'
}

# ============================================================================
# HEALTH CHECK TESTS
# ============================================================================

test_overall_health_metrics() {
    run_test "Overall Health Metrics Validation"
    
    local data
    data=$(get_component_data)
    
    # Test total components
    local total_components
    total_components=$(echo "$data" | jq -r '.totalComponents')
    
    if [[ "$total_components" == "$EXPECTED_TOTAL_COMPONENTS" ]]; then
        log_success "Total components: $total_components"
    else
        log_error "Total components: $total_components, expected: $EXPECTED_TOTAL_COMPONENTS"
        return 1
    fi
    
    # Test healthy components
    local healthy_components
    healthy_components=$(echo "$data" | jq -r '.healthyComponents')
    
    if [[ "$healthy_components" == "$EXPECTED_HEALTHY_COMPONENTS" ]]; then
        log_success "Healthy components: $healthy_components"
    else
        log_error "Healthy components: $healthy_components, expected: $EXPECTED_HEALTHY_COMPONENTS"
        return 1
    fi
    
    # Test error components
    local error_components
    error_components=$(echo "$data" | jq -r '.errorComponents')
    
    if [[ "$error_components" == "$EXPECTED_ERROR_COMPONENTS" ]]; then
        log_success "Error components: $error_components"
    else
        log_error "Error components: $error_components, expected: $EXPECTED_ERROR_COMPONENTS"
        return 1
    fi
    
    # Test overall health score
    local health_score
    health_score=$(echo "$data" | jq -r '.overallHealthScore')
    
    if [[ "$health_score" == "$EXPECTED_HEALTH_SCORE" ]]; then
        log_success "Overall health score: $health_score%"
    else
        log_error "Overall health score: $health_score%, expected: $EXPECTED_HEALTH_SCORE%"
        return 1
    fi
}

test_environment_constants_calculator_health() {
    run_test "EnvironmentConstantsCalculator Health Status (Fixed Issue)"
    
    local data
    data=$(get_component_data)
    
    # Find EnvironmentConstantsCalculator specifically
    local env_calc_status
    env_calc_status=$(echo "$data" | jq -r '.categories.memorySafety[] | select(.id == "environment-constants-calculator") | .status')
    
    if [[ "$env_calc_status" == "healthy" ]]; then
        log_success "EnvironmentConstantsCalculator status: $env_calc_status (issue fixed!)"
    else
        log_error "EnvironmentConstantsCalculator status: $env_calc_status, expected: healthy"
        return 1
    fi
    
    # Test health score
    local env_calc_health_score
    env_calc_health_score=$(echo "$data" | jq -r '.categories.memorySafety[] | select(.id == "environment-constants-calculator") | .healthScore')
    
    if [[ "$env_calc_health_score" == "100" ]]; then
        log_success "EnvironmentConstantsCalculator health score: $env_calc_health_score"
    else
        log_error "EnvironmentConstantsCalculator health score: $env_calc_health_score, expected: 100"
        return 1
    fi
    
    # Test that it has valid metrics
    local response_time
    response_time=$(echo "$data" | jq -r '.categories.memorySafety[] | select(.id == "environment-constants-calculator") | .metrics.responseTime')
    
    if [[ "$response_time" =~ ^[0-9]+$ ]] && [[ "$response_time" -ge 0 ]]; then
        log_success "EnvironmentConstantsCalculator has valid response time: ${response_time}ms"
    else
        log_error "EnvironmentConstantsCalculator has invalid response time: $response_time"
        return 1
    fi
}

test_component_health_scores() {
    run_test "Individual Component Health Scores"
    
    local data
    data=$(get_component_data)
    
    # Get all components across all categories
    local categories=("governance" "tracking" "memorySafety" "integration")
    local total_score=0
    local component_count=0
    
    for category in "${categories[@]}"; do
        local components
        components=$(echo "$data" | jq -r ".categories.$category[].id")
        
        for component_id in $components; do
            ((component_count++))
            
            local health_score
            health_score=$(echo "$data" | jq -r ".categories.$category[] | select(.id == \"$component_id\") | .healthScore")
            
            if [[ "$health_score" =~ ^[0-9]+$ ]] && [[ "$health_score" -ge 0 ]] && [[ "$health_score" -le 100 ]]; then
                total_score=$((total_score + health_score))
                
                if [[ "$health_score" == "100" ]]; then
                    log_success "Component $component_id: health score $health_score"
                else
                    log_error "Component $component_id: health score $health_score (expected 100)"
                    return 1
                fi
            else
                log_error "Component $component_id: invalid health score $health_score"
                return 1
            fi
        done
    done
    
    # Calculate average health score
    local avg_score=$((total_score / component_count))
    log_success "Average component health score: $avg_score (from $component_count components)"
}

test_health_check_refresh() {
    run_test "Health Check After Refresh"
    
    # Get initial health data
    local initial_data
    initial_data=$(get_component_data)
    
    local initial_healthy
    initial_healthy=$(echo "$initial_data" | jq -r '.healthyComponents')
    
    # Refresh and get new data
    local refreshed_data
    refreshed_data=$(refresh_component_data)
    
    local refreshed_healthy
    refreshed_healthy=$(echo "$refreshed_data" | jq -r '.healthyComponents')
    
    if [[ "$refreshed_healthy" == "$initial_healthy" ]]; then
        log_success "Health status consistent after refresh: $refreshed_healthy healthy components"
    else
        log_error "Health status changed after refresh: $initial_healthy -> $refreshed_healthy"
        return 1
    fi
    
    # Verify EnvironmentConstantsCalculator remains healthy after refresh
    local env_calc_status_after_refresh
    env_calc_status_after_refresh=$(echo "$refreshed_data" | jq -r '.categories.memorySafety[] | select(.id == "environment-constants-calculator") | .status')
    
    if [[ "$env_calc_status_after_refresh" == "healthy" ]]; then
        log_success "EnvironmentConstantsCalculator remains healthy after refresh"
    else
        log_error "EnvironmentConstantsCalculator status after refresh: $env_calc_status_after_refresh"
        return 1
    fi
}

test_system_metrics_health() {
    run_test "System Metrics Health Validation"
    
    local data
    data=$(get_component_data)
    
    # Test error rate
    local error_rate
    error_rate=$(echo "$data" | jq -r '.systemMetrics.errorRate')
    
    if [[ "$error_rate" == "0" ]]; then
        log_success "System error rate: $error_rate%"
    else
        log_error "System error rate: $error_rate%, expected: 0%"
        return 1
    fi
    
    # Test average response time
    local avg_response_time
    avg_response_time=$(echo "$data" | jq -r '.systemMetrics.averageResponseTime')
    
    if [[ $(echo "$avg_response_time < 100" | bc -l) == 1 ]]; then
        log_success "Average response time: ${avg_response_time}ms (< 100ms)"
    else
        log_error "Average response time: ${avg_response_time}ms (should be < 100ms)"
        return 1
    fi
    
    # Test total operations
    local total_operations
    total_operations=$(echo "$data" | jq -r '.systemMetrics.totalOperations')
    
    if [[ "$total_operations" =~ ^[0-9]+$ ]] && [[ "$total_operations" -gt 0 ]]; then
        log_success "Total operations: $total_operations"
    else
        log_error "Invalid total operations: $total_operations"
        return 1
    fi
}

test_component_metrics_validity() {
    run_test "Component Metrics Validity"
    
    local data
    data=$(get_component_data)
    
    # Test a sample of components from each category
    local test_components=(
        "governance:governance-rule-engine-core"
        "tracking:session-log-tracker"
        "memorySafety:environment-constants-calculator"
        "integration:governance-tracking-bridge"
    )
    
    for component_spec in "${test_components[@]}"; do
        IFS=':' read -r category component_id <<< "$component_spec"
        
        # Test response time
        local response_time
        response_time=$(echo "$data" | jq -r ".categories.$category[] | select(.id == \"$component_id\") | .metrics.responseTime")
        
        if [[ "$response_time" =~ ^[0-9]+$ ]] && [[ "$response_time" -ge 0 ]]; then
            log_success "Component $component_id: valid response time ${response_time}ms"
        else
            log_error "Component $component_id: invalid response time $response_time"
            return 1
        fi
        
        # Test operation count
        local operation_count
        operation_count=$(echo "$data" | jq -r ".categories.$category[] | select(.id == \"$component_id\") | .metrics.operationCount")
        
        if [[ "$operation_count" =~ ^[0-9]+$ ]] && [[ "$operation_count" -gt 0 ]]; then
            log_success "Component $component_id: valid operation count $operation_count"
        else
            log_error "Component $component_id: invalid operation count $operation_count"
            return 1
        fi
    done
}

test_health_status_consistency() {
    run_test "Health Status Consistency Check"
    
    local data
    data=$(get_component_data)
    
    # Count components by status across all categories
    local healthy_count=0
    local warning_count=0
    local error_count=0
    local offline_count=0
    
    local categories=("governance" "tracking" "memorySafety" "integration")
    
    for category in "${categories[@]}"; do
        local components
        components=$(echo "$data" | jq -r ".categories.$category[].status")
        
        for status in $components; do
            case "$status" in
                "healthy") ((healthy_count++)) ;;
                "warning") ((warning_count++)) ;;
                "error") ((error_count++)) ;;
                "offline") ((offline_count++)) ;;
                *) log_error "Unknown status: $status"; return 1 ;;
            esac
        done
    done
    
    # Verify counts match system metrics
    local system_healthy
    system_healthy=$(echo "$data" | jq -r '.healthyComponents')
    
    local system_error
    system_error=$(echo "$data" | jq -r '.errorComponents')
    
    if [[ "$healthy_count" == "$system_healthy" ]]; then
        log_success "Healthy component count consistent: $healthy_count"
    else
        log_error "Healthy count mismatch: counted $healthy_count, system reports $system_healthy"
        return 1
    fi
    
    if [[ "$error_count" == "$system_error" ]]; then
        log_success "Error component count consistent: $error_count"
    else
        log_error "Error count mismatch: counted $error_count, system reports $system_error"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    echo "============================================================================"
    echo "M0 Real Component Integration Dashboard - Health Check Tests"
    echo "============================================================================"
    echo ""
    
    # Check if server is running
    if ! curl -s --max-time 5 "$BASE_URL" > /dev/null 2>&1; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: cd demos/m0-real-dashboard && npm run dev"
        exit 1
    fi
    
    # Run all tests
    test_overall_health_metrics || true
    test_environment_constants_calculator_health || true
    test_component_health_scores || true
    test_health_check_refresh || true
    test_system_metrics_health || true
    test_component_metrics_validity || true
    test_health_status_consistency || true
    
    # Print summary
    echo ""
    echo "============================================================================"
    echo "TEST SUMMARY"
    echo "============================================================================"
    echo "Tests Run: $TESTS_RUN"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All health check tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some health check tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
