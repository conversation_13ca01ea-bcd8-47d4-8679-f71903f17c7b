#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - Performance Tests
# ============================================================================
# 
# This script tests performance metrics for the M0 dashboard:
# - API response times (GET and POST endpoints)
# - Component initialization performance
# - Dashboard load times
# - Memory usage validation
# - Concurrent request handling
# - Performance under load
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"
TIMEOUT=30

# Performance thresholds (in milliseconds)
MAX_GET_RESPONSE_TIME=2000      # 2 seconds
MAX_POST_RESPONSE_TIME=5000     # 5 seconds
MAX_DASHBOARD_LOAD_TIME=3000    # 3 seconds
MAX_CONCURRENT_RESPONSE=10000   # 10 seconds for concurrent requests

# Load test configuration
CONCURRENT_REQUESTS=5
LOAD_TEST_DURATION=10  # seconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

run_test() {
    local test_name="$1"
    ((TESTS_RUN++))
    log_info "Running test: $test_name"
}

# Get current timestamp in milliseconds
get_timestamp_ms() {
    date +%s%3N
}

# Measure request time
measure_request_time() {
    local method="$1"
    local url="$2"
    local data="$3"
    
    local start_time
    start_time=$(get_timestamp_ms)
    
    if [[ "$method" == "GET" ]]; then
        curl -s --max-time $TIMEOUT "$url" > /dev/null
    elif [[ "$method" == "POST" ]]; then
        curl -s --max-time $TIMEOUT \
            -X POST \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$url" > /dev/null
    fi
    
    local end_time
    end_time=$(get_timestamp_ms)
    
    echo $((end_time - start_time))
}

# ============================================================================
# PERFORMANCE TESTS
# ============================================================================

test_get_endpoint_performance() {
    run_test "GET Endpoint Performance"
    
    local total_time=0
    local iterations=5
    local max_time=0
    local min_time=999999
    
    for i in $(seq 1 $iterations); do
        local request_time
        request_time=$(measure_request_time "GET" "$BASE_URL$API_ENDPOINT" "")
        
        total_time=$((total_time + request_time))
        
        if [[ $request_time -gt $max_time ]]; then
            max_time=$request_time
        fi
        
        if [[ $request_time -lt $min_time ]]; then
            min_time=$request_time
        fi
        
        log_info "GET request $i: ${request_time}ms"
    done
    
    local avg_time=$((total_time / iterations))
    
    if [[ $avg_time -lt $MAX_GET_RESPONSE_TIME ]]; then
        log_success "GET average response time: ${avg_time}ms (< ${MAX_GET_RESPONSE_TIME}ms)"
    else
        log_error "GET average response time: ${avg_time}ms (should be < ${MAX_GET_RESPONSE_TIME}ms)"
        return 1
    fi
    
    log_info "GET performance stats - Min: ${min_time}ms, Max: ${max_time}ms, Avg: ${avg_time}ms"
}

test_post_endpoint_performance() {
    run_test "POST Endpoint Performance"
    
    local total_time=0
    local iterations=5
    local max_time=0
    local min_time=999999
    
    for i in $(seq 1 $iterations); do
        local request_time
        request_time=$(measure_request_time "POST" "$BASE_URL$API_ENDPOINT" '{"action":"refresh"}')
        
        total_time=$((total_time + request_time))
        
        if [[ $request_time -gt $max_time ]]; then
            max_time=$request_time
        fi
        
        if [[ $request_time -lt $min_time ]]; then
            min_time=$request_time
        fi
        
        log_info "POST request $i: ${request_time}ms"
    done
    
    local avg_time=$((total_time / iterations))
    
    if [[ $avg_time -lt $MAX_POST_RESPONSE_TIME ]]; then
        log_success "POST average response time: ${avg_time}ms (< ${MAX_POST_RESPONSE_TIME}ms)"
    else
        log_error "POST average response time: ${avg_time}ms (should be < ${MAX_POST_RESPONSE_TIME}ms)"
        return 1
    fi
    
    log_info "POST performance stats - Min: ${min_time}ms, Max: ${max_time}ms, Avg: ${avg_time}ms"
}

test_dashboard_load_performance() {
    run_test "Dashboard Load Performance"
    
    local total_time=0
    local iterations=3
    
    for i in $(seq 1 $iterations); do
        local request_time
        request_time=$(measure_request_time "GET" "$BASE_URL" "")
        
        total_time=$((total_time + request_time))
        log_info "Dashboard load $i: ${request_time}ms"
    done
    
    local avg_time=$((total_time / iterations))
    
    if [[ $avg_time -lt $MAX_DASHBOARD_LOAD_TIME ]]; then
        log_success "Dashboard average load time: ${avg_time}ms (< ${MAX_DASHBOARD_LOAD_TIME}ms)"
    else
        log_error "Dashboard average load time: ${avg_time}ms (should be < ${MAX_DASHBOARD_LOAD_TIME}ms)"
        return 1
    fi
}

test_concurrent_requests() {
    run_test "Concurrent Request Performance"
    
    local start_time
    start_time=$(get_timestamp_ms)
    
    # Launch concurrent requests
    local pids=()
    for i in $(seq 1 $CONCURRENT_REQUESTS); do
        (
            curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT" > /dev/null
        ) &
        pids+=($!)
    done
    
    # Wait for all requests to complete
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    local end_time
    end_time=$(get_timestamp_ms)
    
    local total_time=$((end_time - start_time))
    
    if [[ $total_time -lt $MAX_CONCURRENT_RESPONSE ]]; then
        log_success "Concurrent requests ($CONCURRENT_REQUESTS) completed in: ${total_time}ms"
    else
        log_error "Concurrent requests took: ${total_time}ms (should be < ${MAX_CONCURRENT_RESPONSE}ms)"
        return 1
    fi
}

test_api_response_size() {
    run_test "API Response Size Analysis"
    
    local response
    response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    local response_size
    response_size=$(echo "$response" | wc -c)
    
    # Convert to KB
    local response_size_kb=$((response_size / 1024))
    
    if [[ $response_size_kb -lt 100 ]]; then  # Less than 100KB
        log_success "API response size: ${response_size_kb}KB (reasonable size)"
    else
        log_warning "API response size: ${response_size_kb}KB (consider optimization)"
    fi
    
    # Test JSON parsing performance
    local parse_start
    parse_start=$(get_timestamp_ms)
    
    echo "$response" | jq '.data.totalComponents' > /dev/null
    
    local parse_end
    parse_end=$(get_timestamp_ms)
    
    local parse_time=$((parse_end - parse_start))
    
    if [[ $parse_time -lt 100 ]]; then  # Less than 100ms
        log_success "JSON parsing time: ${parse_time}ms"
    else
        log_warning "JSON parsing time: ${parse_time}ms (consider response optimization)"
    fi
}

test_component_metrics_performance() {
    run_test "Component Metrics Performance Analysis"
    
    local response
    response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    # Test average response time from system metrics
    local avg_response_time
    avg_response_time=$(echo "$response" | jq -r '.data.systemMetrics.averageResponseTime')
    
    if [[ $(echo "$avg_response_time < 50" | bc -l) == 1 ]]; then
        log_success "System average response time: ${avg_response_time}ms (excellent)"
    elif [[ $(echo "$avg_response_time < 100" | bc -l) == 1 ]]; then
        log_success "System average response time: ${avg_response_time}ms (good)"
    else
        log_warning "System average response time: ${avg_response_time}ms (consider optimization)"
    fi
    
    # Test total operations
    local total_operations
    total_operations=$(echo "$response" | jq -r '.data.systemMetrics.totalOperations')
    
    if [[ "$total_operations" =~ ^[0-9]+$ ]] && [[ "$total_operations" -gt 0 ]]; then
        log_success "Total operations tracked: $total_operations"
    else
        log_error "Invalid total operations: $total_operations"
        return 1
    fi
}

test_memory_usage_metrics() {
    run_test "Memory Usage Metrics"
    
    local response
    response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    # Test system memory usage
    local memory_usage
    memory_usage=$(echo "$response" | jq -r '.data.systemMetrics.totalMemoryUsage')
    
    if [[ "$memory_usage" =~ ^[0-9]+$ ]]; then
        if [[ $memory_usage -lt 100 ]]; then  # Less than 100MB
            log_success "System memory usage: ${memory_usage}MB (efficient)"
        elif [[ $memory_usage -lt 500 ]]; then  # Less than 500MB
            log_success "System memory usage: ${memory_usage}MB (acceptable)"
        else
            log_warning "System memory usage: ${memory_usage}MB (consider optimization)"
        fi
    else
        log_info "Memory usage not tracked or invalid: $memory_usage"
    fi
    
    # Test individual component memory usage
    local components_with_memory=0
    local categories=("governance" "tracking" "memorySafety" "integration")
    
    for category in "${categories[@]}"; do
        local component_count
        component_count=$(echo "$response" | jq ".data.categories.$category | length")
        
        if [[ "$component_count" -gt 0 ]]; then
            components_with_memory=$((components_with_memory + component_count))
        fi
    done
    
    log_success "Components reporting metrics: $components_with_memory"
}

test_load_performance() {
    run_test "Load Performance Test"
    
    log_info "Running load test for $LOAD_TEST_DURATION seconds with continuous requests..."
    
    local start_time
    start_time=$(get_timestamp_ms)
    
    local end_time=$((start_time + LOAD_TEST_DURATION * 1000))
    local request_count=0
    local success_count=0
    local total_response_time=0
    
    while [[ $(get_timestamp_ms) -lt $end_time ]]; do
        local request_start
        request_start=$(get_timestamp_ms)
        
        if curl -s --max-time 5 "$BASE_URL$API_ENDPOINT" > /dev/null 2>&1; then
            ((success_count++))
            local request_end
            request_end=$(get_timestamp_ms)
            total_response_time=$((total_response_time + request_end - request_start))
        fi
        
        ((request_count++))
        
        # Small delay to prevent overwhelming the server
        sleep 0.1
    done
    
    local success_rate=$((success_count * 100 / request_count))
    local avg_response_time=$((total_response_time / success_count))
    
    if [[ $success_rate -ge 95 ]]; then
        log_success "Load test success rate: ${success_rate}% ($success_count/$request_count)"
    else
        log_error "Load test success rate: ${success_rate}% (should be >= 95%)"
        return 1
    fi
    
    if [[ $avg_response_time -lt $MAX_GET_RESPONSE_TIME ]]; then
        log_success "Load test average response time: ${avg_response_time}ms"
    else
        log_warning "Load test average response time: ${avg_response_time}ms (degraded under load)"
    fi
    
    local requests_per_second=$((request_count / LOAD_TEST_DURATION))
    log_info "Load test throughput: ${requests_per_second} requests/second"
}

test_error_rate_performance() {
    run_test "Error Rate Performance"
    
    local response
    response=$(curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT")
    
    local error_rate
    error_rate=$(echo "$response" | jq -r '.data.systemMetrics.errorRate')
    
    if [[ $(echo "$error_rate == 0" | bc -l) == 1 ]]; then
        log_success "System error rate: ${error_rate}% (perfect)"
    elif [[ $(echo "$error_rate < 1" | bc -l) == 1 ]]; then
        log_success "System error rate: ${error_rate}% (excellent)"
    elif [[ $(echo "$error_rate < 5" | bc -l) == 1 ]]; then
        log_warning "System error rate: ${error_rate}% (acceptable)"
    else
        log_error "System error rate: ${error_rate}% (too high)"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    echo "============================================================================"
    echo "M0 Real Component Integration Dashboard - Performance Tests"
    echo "============================================================================"
    echo ""
    
    # Check if server is running
    if ! curl -s --max-time 5 "$BASE_URL" > /dev/null 2>&1; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: cd demos/m0-real-dashboard && npm run dev"
        exit 1
    fi
    
    # Check if bc is available for floating point calculations
    if ! command -v bc &> /dev/null; then
        log_warning "bc command not found, some floating point comparisons may not work"
    fi
    
    # Run all tests
    test_get_endpoint_performance || true
    test_post_endpoint_performance || true
    test_dashboard_load_performance || true
    test_concurrent_requests || true
    test_api_response_size || true
    test_component_metrics_performance || true
    test_memory_usage_metrics || true
    test_load_performance || true
    test_error_rate_performance || true
    
    # Print summary
    echo ""
    echo "============================================================================"
    echo "TEST SUMMARY"
    echo "============================================================================"
    echo "Tests Run: $TESTS_RUN"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All performance tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some performance tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
