#!/bin/bash

# ============================================================================
# M0 Real Component Integration Dashboard - Component Integration Tests
# ============================================================================
# 
# This script tests the integration of all 21 M0 components across four categories:
# - Governance Components (11 components)
# - Tracking Components (6 components) 
# - Memory Safety Components (1 component)
# - Integration Components (3 components)
#
# Validates component registration, health status, and category organization.
#
# Author: OA Framework Test Suite
# Version: 1.0.0
# ============================================================================

set -e  # Exit on any error

# Test configuration
BASE_URL="http://localhost:3000"
API_ENDPOINT="/api/m0-components"
TIMEOUT=30

# Expected component counts by category
EXPECTED_GOVERNANCE=11
EXPECTED_TRACKING=6
EXPECTED_MEMORY_SAFETY=1
EXPECTED_INTEGRATION=3
EXPECTED_TOTAL=21

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

run_test() {
    local test_name="$1"
    ((TESTS_RUN++))
    log_info "Running test: $test_name"
}

# Get fresh component data
get_component_data() {
    curl -s --max-time $TIMEOUT "$BASE_URL$API_ENDPOINT" | jq '.data'
}

# ============================================================================
# COMPONENT INTEGRATION TESTS
# ============================================================================

test_total_component_count() {
    run_test "Total Component Count Validation"
    
    local data
    data=$(get_component_data)
    
    local total_components
    total_components=$(echo "$data" | jq -r '.totalComponents')
    
    if [[ "$total_components" == "$EXPECTED_TOTAL" ]]; then
        log_success "Total components: $total_components (expected: $EXPECTED_TOTAL)"
    else
        log_error "Total components: $total_components, expected: $EXPECTED_TOTAL"
        return 1
    fi
}

test_governance_components() {
    run_test "Governance Components Integration"
    
    local data
    data=$(get_component_data)
    
    local governance_count
    governance_count=$(echo "$data" | jq '.categories.governance | length')
    
    if [[ "$governance_count" == "$EXPECTED_GOVERNANCE" ]]; then
        log_success "Governance components: $governance_count (expected: $EXPECTED_GOVERNANCE)"
    else
        log_error "Governance components: $governance_count, expected: $EXPECTED_GOVERNANCE"
        return 1
    fi
    
    # Test specific governance components
    local expected_governance_components=(
        "governance-rule-engine-core"
        "governance-rule-compliance-checker"
        "governance-rule-validator-factory"
        "governance-rule-compliance-framework"
        "governance-rule-quality-framework"
        "governance-rule-testing-framework"
        "governance-rule-enterprise-framework"
        "governance-rule-governance-framework"
        "governance-rule-integration-framework"
        "governance-rule-csrf-manager"
        "governance-rule-template-security"
    )
    
    for component_id in "${expected_governance_components[@]}"; do
        if echo "$data" | jq -e ".categories.governance[] | select(.id == \"$component_id\")" > /dev/null 2>&1; then
            log_success "Found governance component: $component_id"
        else
            log_error "Missing governance component: $component_id"
            return 1
        fi
    done
}

test_tracking_components() {
    run_test "Tracking Components Integration"
    
    local data
    data=$(get_component_data)
    
    local tracking_count
    tracking_count=$(echo "$data" | jq '.categories.tracking | length')
    
    if [[ "$tracking_count" == "$EXPECTED_TRACKING" ]]; then
        log_success "Tracking components: $tracking_count (expected: $EXPECTED_TRACKING)"
    else
        log_error "Tracking components: $tracking_count, expected: $EXPECTED_TRACKING"
        return 1
    fi
    
    # Test specific tracking components
    local expected_tracking_components=(
        "session-log-tracker"
        "implementation-progress-tracker"
        "cross-reference-validation-engine"
        "context-authority-protocol"
        "authority-tracking-service"
        "governance-tracking-system"
    )
    
    for component_id in "${expected_tracking_components[@]}"; do
        if echo "$data" | jq -e ".categories.tracking[] | select(.id == \"$component_id\")" > /dev/null 2>&1; then
            log_success "Found tracking component: $component_id"
        else
            log_error "Missing tracking component: $component_id"
            return 1
        fi
    done
}

test_memory_safety_components() {
    run_test "Memory Safety Components Integration"
    
    local data
    data=$(get_component_data)
    
    local memory_safety_count
    memory_safety_count=$(echo "$data" | jq '.categories.memorySafety | length')
    
    if [[ "$memory_safety_count" == "$EXPECTED_MEMORY_SAFETY" ]]; then
        log_success "Memory safety components: $memory_safety_count (expected: $EXPECTED_MEMORY_SAFETY)"
    else
        log_error "Memory safety components: $memory_safety_count, expected: $EXPECTED_MEMORY_SAFETY"
        return 1
    fi
    
    # Test EnvironmentConstantsCalculator specifically (this was the error component)
    if echo "$data" | jq -e '.categories.memorySafety[] | select(.id == "environment-constants-calculator")' > /dev/null 2>&1; then
        log_success "Found EnvironmentConstantsCalculator component"
    else
        log_error "Missing EnvironmentConstantsCalculator component"
        return 1
    fi
}

test_integration_components() {
    run_test "Integration Components Integration"
    
    local data
    data=$(get_component_data)
    
    local integration_count
    integration_count=$(echo "$data" | jq '.categories.integration | length')
    
    if [[ "$integration_count" == "$EXPECTED_INTEGRATION" ]]; then
        log_success "Integration components: $integration_count (expected: $EXPECTED_INTEGRATION)"
    else
        log_error "Integration components: $integration_count, expected: $EXPECTED_INTEGRATION"
        return 1
    fi
    
    # Test specific integration components
    local expected_integration_components=(
        "governance-tracking-bridge"
        "realtime-event-coordinator"
        "authority-compliance-monitor-bridge"
    )
    
    for component_id in "${expected_integration_components[@]}"; do
        if echo "$data" | jq -e ".categories.integration[] | select(.id == \"$component_id\")" > /dev/null 2>&1; then
            log_success "Found integration component: $component_id"
        else
            log_error "Missing integration component: $component_id"
            return 1
        fi
    done
}

test_component_structure_validation() {
    run_test "Component Structure Validation"
    
    local data
    data=$(get_component_data)
    
    # Test that each component has required fields
    local required_fields=("id" "name" "category" "status" "lastUpdate" "metrics" "healthScore")
    
    # Test governance components structure
    local governance_components
    governance_components=$(echo "$data" | jq -r '.categories.governance[].id')
    
    for component_id in $governance_components; do
        for field in "${required_fields[@]}"; do
            if echo "$data" | jq -e ".categories.governance[] | select(.id == \"$component_id\") | .$field" > /dev/null 2>&1; then
                continue  # Field exists
            else
                log_error "Component $component_id missing field: $field"
                return 1
            fi
        done
    done
    
    log_success "All governance components have required fields"
    
    # Test metrics structure
    local metrics_fields=("responseTime" "errorRate" "memoryUsage" "operationCount")
    
    for component_id in $governance_components; do
        for field in "${metrics_fields[@]}"; do
            if echo "$data" | jq -e ".categories.governance[] | select(.id == \"$component_id\") | .metrics.$field" > /dev/null 2>&1; then
                continue  # Field exists
            else
                log_error "Component $component_id missing metrics field: $field"
                return 1
            fi
        done
    done
    
    log_success "All governance components have required metrics fields"
}

test_component_health_status() {
    run_test "Component Health Status Validation"
    
    local data
    data=$(get_component_data)
    
    # Get all components across all categories
    local all_components
    all_components=$(echo "$data" | jq -r '.categories | to_entries[] | .value[] | .id')
    
    local healthy_count=0
    local total_count=0
    
    for component_id in $all_components; do
        ((total_count++))
        
        # Find component status across all categories
        local status
        status=$(echo "$data" | jq -r ".categories | to_entries[] | .value[] | select(.id == \"$component_id\") | .status")
        
        if [[ "$status" == "healthy" ]]; then
            ((healthy_count++))
        else
            log_error "Component $component_id has status: $status (expected: healthy)"
        fi
    done
    
    if [[ $healthy_count -eq $total_count ]]; then
        log_success "All $total_count components are healthy"
    else
        log_error "Only $healthy_count out of $total_count components are healthy"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    echo "============================================================================"
    echo "M0 Real Component Integration Dashboard - Component Integration Tests"
    echo "============================================================================"
    echo ""
    
    # Check if server is running
    if ! curl -s --max-time 5 "$BASE_URL" > /dev/null 2>&1; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the server with: cd demos/m0-real-dashboard && npm run dev"
        exit 1
    fi
    
    # Run all tests
    test_total_component_count || true
    test_governance_components || true
    test_tracking_components || true
    test_memory_safety_components || true
    test_integration_components || true
    test_component_structure_validation || true
    test_component_health_status || true
    
    # Print summary
    echo ""
    echo "============================================================================"
    echo "TEST SUMMARY"
    echo "============================================================================"
    echo "Tests Run: $TESTS_RUN"
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}All component integration tests passed!${NC}"
        exit 0
    else
        echo -e "${RED}Some component integration tests failed!${NC}"
        exit 1
    fi
}

# Run main function
main "$@"
