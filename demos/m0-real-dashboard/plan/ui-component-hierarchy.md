# M0 Dashboard UI Component Hierarchy & Design System

**Document**: React Component Structure & Design Patterns  
**Authority**: President & CEO, <PERSON><PERSON><PERSON><PERSON> Consultancy  
**Status**: UI ARCHITECTURE  
**Created**: 2025-09-04  
**Version**: 1.0  

---

## 🎨 **Design System Overview**

The M0 Dashboard follows Material-UI design principles with custom OA Framework theming, ensuring enterprise-grade user experience while maintaining consistency with the operational M0 system's professional standards.

### **Design Principles**
- **Enterprise Professional**: Clean, professional interface suitable for enterprise monitoring
- **Real-Time Focus**: Emphasis on live data visualization and status indicators
- **Memory-Safe UI**: All components follow React best practices with proper cleanup
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Accessibility**: WCAG 2.1 AA compliance throughout

---

## 🏗️ **Component Hierarchy Structure**

### **Root Level Architecture**
```
App (Next.js App Router)
├── RootLayout                    # Global layout with M0 theming
│   ├── M0ErrorBoundary          # Global error boundary
│   ├── M0ThemeProvider          # Material-UI theme provider
│   ├── M0ContextProvider        # Global M0 system context
│   └── PageContent              # Dynamic page content
│       ├── AppLayout            # Main application layout
│       │   ├── Header           # Top navigation and status
│       │   ├── Sidebar          # Navigation sidebar
│       │   └── MainContent      # Dashboard content area
│       └── DashboardPages       # Individual dashboard pages
```

---

## 📱 **Layout Components**

### **1. RootLayout Component**
**File**: `src/app/layout.tsx`
**Purpose**: Global application layout with M0 theming and context

```typescript
interface RootLayoutProps {
  children: React.ReactNode;
}

const RootLayout: React.FC<RootLayoutProps> = ({ children }) => {
  return (
    <html lang="en">
      <body>
        <M0ThemeProvider>
          <M0ContextProvider>
            <M0ErrorBoundary>
              <CssBaseline />
              {children}
            </M0ErrorBoundary>
          </M0ContextProvider>
        </M0ThemeProvider>
      </body>
    </html>
  );
};
```

### **2. AppLayout Component**
**File**: `src/components/layout/AppLayout.tsx`
**Purpose**: Main application layout with header, sidebar, and content area

```typescript
interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { systemStatus } = useM0Context();
  
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Header 
        onMenuClick={() => setSidebarOpen(!sidebarOpen)}
        systemStatus={systemStatus}
      />
      <Sidebar 
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />
      <MainContent sidebarOpen={sidebarOpen}>
        {children}
      </MainContent>
    </Box>
  );
};
```

### **3. Header Component**
**File**: `src/components/layout/Header.tsx`
**Purpose**: Top navigation with M0 system status and controls

```typescript
interface HeaderProps {
  onMenuClick: () => void;
  systemStatus: M0SystemStatus;
}

const Header: React.FC<HeaderProps> = ({ onMenuClick, systemStatus }) => {
  return (
    <AppBar position="fixed" sx={{ zIndex: theme => theme.zIndex.drawer + 1 }}>
      <Toolbar>
        <IconButton onClick={onMenuClick} edge="start" color="inherit">
          <MenuIcon />
        </IconButton>
        <Typography variant="h6" sx={{ flexGrow: 1 }}>
          M0 Real Component Integration Dashboard
        </Typography>
        <M0StatusIndicator status={systemStatus} />
        <M0SystemControls />
      </Toolbar>
    </AppBar>
  );
};
```

### **4. Sidebar Component**
**File**: `src/components/layout/Sidebar.tsx`
**Purpose**: Navigation sidebar with dashboard sections

```typescript
interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const navigationItems = [
    { label: 'System Overview', path: '/', icon: <DashboardIcon /> },
    { label: 'Security Dashboard', path: '/security', icon: <SecurityIcon /> },
    { label: 'Governance Panel', path: '/governance', icon: <GovernanceIcon /> },
    { label: 'Tracking Monitor', path: '/tracking', icon: <TrackingIcon /> },
    { label: 'Integration Console', path: '/integration', icon: <IntegrationIcon /> },
    { label: 'System Status', path: '/system', icon: <SystemIcon /> }
  ];
  
  return (
    <Drawer
      variant="persistent"
      anchor="left"
      open={open}
      sx={{ width: 280, flexShrink: 0 }}
    >
      <Toolbar /> {/* Spacer for header */}
      <List>
        {navigationItems.map((item) => (
          <NavigationItem key={item.path} {...item} />
        ))}
      </List>
    </Drawer>
  );
};
```

---

## 📊 **Dashboard Page Components**

### **1. System Overview Dashboard**
**File**: `src/app/page.tsx`
**Purpose**: Main dashboard showing complete M0 system status

```typescript
const SystemOverviewDashboard: React.FC = () => {
  const { data: systemData } = useRealM0Data('/api/m0-system/status', { refreshInterval: 30000 });
  const { data: healthData } = useRealM0Data('/api/m0-integration/health-check', { refreshInterval: 60000 });
  
  return (
    <DashboardContainer>
      <DashboardHeader 
        title="M0 System Overview"
        subtitle="Real-time monitoring of 95+ M0 components"
      />
      <DashboardGrid>
        <SystemOverviewCard data={systemData} />
        <ComponentCategoryGrid data={healthData} />
        <SystemMetricsPanel data={systemData} />
        <RecentActivityFeed />
      </DashboardGrid>
    </DashboardContainer>
  );
};
```

### **2. Security Dashboard**
**File**: `src/app/security/page.tsx`
**Purpose**: Memory safety and security monitoring

```typescript
const SecurityDashboard: React.FC = () => {
  const { data: memoryData } = useRealM0Data('/api/m0-security/memory-usage', { refreshInterval: 2000 });
  const { data: protectionData } = useRealM0Data('/api/m0-security/protection-status', { refreshInterval: 5000 });
  
  return (
    <DashboardContainer>
      <DashboardHeader 
        title="Security & Memory Safety"
        subtitle="Real-time memory protection monitoring"
      />
      <DashboardGrid>
        <MemoryUsageChart data={memoryData} />
        <ProtectedServicesGrid data={protectionData} />
        <AttackPreventionLog />
        <SecurityControlPanel />
      </DashboardGrid>
    </DashboardContainer>
  );
};
```

---

## 🧩 **Widget Components**

### **Widget Base Component**
```typescript
interface DashboardWidgetProps {
  title: string;
  subtitle?: string;
  loading?: boolean;
  error?: Error;
  refreshInterval?: number;
  onRefresh?: () => void;
  children: React.ReactNode;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  title,
  subtitle,
  loading,
  error,
  onRefresh,
  children
}) => {
  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardHeader
        title={title}
        subheader={subtitle}
        action={
          <IconButton onClick={onRefresh} disabled={loading}>
            <RefreshIcon />
          </IconButton>
        }
      />
      <CardContent sx={{ flexGrow: 1 }}>
        {loading && <CircularProgress />}
        {error && <ErrorDisplay error={error} />}
        {!loading && !error && children}
      </CardContent>
    </Card>
  );
};
```

### **Specialized Widget Components**

#### **1. Memory Usage Chart Widget**
**File**: `src/components/widgets/MemoryUsageChart.tsx`
```typescript
interface MemoryUsageChartProps {
  data: MemoryUsageResponse;
}

const MemoryUsageChart: React.FC<MemoryUsageChartProps> = ({ data }) => {
  const chartData = useMemo(() => 
    formatMemoryDataForChart(data?.memoryMetrics), [data]
  );
  
  return (
    <DashboardWidget 
      title="Memory Usage"
      subtitle={`${data?.memoryMetrics.protectedServices} protected services`}
    >
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="timestamp" />
          <YAxis />
          <Tooltip />
          <Line 
            type="monotone" 
            dataKey="usage" 
            stroke="#1976d2" 
            strokeWidth={2}
          />
          <Line 
            type="monotone" 
            dataKey="limit" 
            stroke="#d32f2f" 
            strokeDasharray="5 5"
          />
        </LineChart>
      </ResponsiveContainer>
    </DashboardWidget>
  );
};
```

#### **2. Component Health Grid Widget**
**File**: `src/components/widgets/ComponentHealthGrid.tsx`
```typescript
interface ComponentHealthGridProps {
  data: TrackingComponentsResponse;
}

const ComponentHealthGrid: React.FC<ComponentHealthGridProps> = ({ data }) => {
  return (
    <DashboardWidget 
      title="Component Health"
      subtitle={`${data?.healthyComponents}/${data?.totalComponents} healthy`}
    >
      <Grid container spacing={1}>
        {data?.components.map((component) => (
          <Grid item xs={12} sm={6} md={4} key={component.componentId}>
            <ComponentHealthCard component={component} />
          </Grid>
        ))}
      </Grid>
    </DashboardWidget>
  );
};
```

#### **3. Governance Rules List Widget**
**File**: `src/components/widgets/GovernanceRulesList.tsx`
```typescript
interface GovernanceRulesListProps {
  data: GovernanceRulesResponse;
}

const GovernanceRulesList: React.FC<GovernanceRulesListProps> = ({ data }) => {
  return (
    <DashboardWidget 
      title="Active Governance Rules"
      subtitle={`${data?.activeRules} active rules`}
    >
      <List>
        {data?.rules.map((rule) => (
          <ListItem key={rule.ruleId}>
            <ListItemIcon>
              <GovernanceRuleIcon status={rule.status} />
            </ListItemIcon>
            <ListItemText
              primary={rule.name}
              secondary={`${rule.ruleId} - Score: ${rule.complianceScore}%`}
            />
            <Chip 
              label={rule.status}
              color={getStatusColor(rule.status)}
              size="small"
            />
          </ListItem>
        ))}
      </List>
    </DashboardWidget>
  );
};
```

---

## 🎨 **Common UI Components**

### **1. Status Indicators**
```typescript
interface M0StatusIndicatorProps {
  status: M0SystemStatus;
  size?: 'small' | 'medium' | 'large';
}

const M0StatusIndicator: React.FC<M0StatusIndicatorProps> = ({ status, size = 'medium' }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'success';
      case 'degraded': return 'warning';
      case 'critical': return 'error';
      default: return 'default';
    }
  };
  
  return (
    <Chip
      icon={<CircleIcon />}
      label={status.toUpperCase()}
      color={getStatusColor(status)}
      size={size}
      variant="outlined"
    />
  );
};
```

### **2. Real-Time Data Display**
```typescript
interface RealTimeDataDisplayProps {
  value: number | string;
  label: string;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  format?: 'number' | 'percentage' | 'bytes' | 'duration';
}

const RealTimeDataDisplay: React.FC<RealTimeDataDisplayProps> = ({
  value,
  label,
  unit,
  trend,
  format = 'number'
}) => {
  const formattedValue = formatValue(value, format);
  
  return (
    <Box sx={{ textAlign: 'center', p: 2 }}>
      <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
        {formattedValue}
        {unit && <Typography component="span" variant="h6"> {unit}</Typography>}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {label}
      </Typography>
      {trend && <TrendIndicator trend={trend} />}
    </Box>
  );
};
```

### **3. Error Display Component**
```typescript
interface ErrorDisplayProps {
  error: Error;
  onRetry?: () => void;
  showDetails?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, onRetry, showDetails }) => {
  return (
    <Alert 
      severity="error" 
      action={
        onRetry && (
          <Button color="inherit" size="small" onClick={onRetry}>
            Retry
          </Button>
        )
      }
    >
      <AlertTitle>M0 Component Error</AlertTitle>
      {error.message}
      {showDetails && (
        <Accordion sx={{ mt: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="body2">Error Details</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
              {error.stack}
            </Typography>
          </AccordionDetails>
        </Accordion>
      )}
    </Alert>
  );
};
```

---

## 📱 **Responsive Design Patterns**

### **Grid Layout System**
```typescript
const DashboardGrid = styled(Grid)(({ theme }) => ({
  padding: theme.spacing(2),
  
  // Mobile: Single column
  [theme.breakpoints.down('md')]: {
    '& .MuiGrid-item': {
      width: '100%',
    },
  },
  
  // Tablet: Two columns
  [theme.breakpoints.between('md', 'lg')]: {
    '& .widget-large': {
      width: '100%',
    },
    '& .widget-medium': {
      width: '50%',
    },
  },
  
  // Desktop: Three columns
  [theme.breakpoints.up('lg')]: {
    '& .widget-large': {
      width: '66.666%',
    },
    '& .widget-medium': {
      width: '33.333%',
    },
    '& .widget-small': {
      width: '25%',
    },
  },
}));
```

### **Mobile Navigation**
```typescript
const MobileNavigation: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  
  return (
    <>
      <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
        <MoreVertIcon />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => navigateTo('/security')}>
          Security Dashboard
        </MenuItem>
        <MenuItem onClick={() => navigateTo('/governance')}>
          Governance Panel
        </MenuItem>
        <MenuItem onClick={() => navigateTo('/tracking')}>
          Tracking Monitor
        </MenuItem>
      </Menu>
    </>
  );
};
```

---

## 🎨 **Theme Configuration**

### **M0 Custom Theme**
```typescript
const m0Theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',      // M0 brand blue
      light: '#42a5f5',
      dark: '#1565c0',
    },
    secondary: {
      main: '#4caf50',      // Operational green
      light: '#81c784',
      dark: '#388e3c',
    },
    error: {
      main: '#d32f2f',      // Error red
    },
    warning: {
      main: '#ff9800',      // Warning orange
    },
    background: {
      default: '#f5f5f5',   // Light gray background
      paper: '#ffffff',     // White cards
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          borderRadius: 8,
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          fontWeight: 500,
        },
      },
    },
  },
});
```

---

## ✅ **Component Development Checklist**

### **Quality Standards** ✅
- [ ] TypeScript strict mode compliance
- [ ] React.memo optimization for expensive components
- [ ] Proper useEffect cleanup for subscriptions
- [ ] Error boundary implementation
- [ ] Loading state handling
- [ ] Responsive design implementation

### **Accessibility Standards** ✅
- [ ] ARIA labels and roles
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Color contrast compliance
- [ ] Focus management

### **Performance Standards** ✅
- [ ] Code splitting for large components
- [ ] Virtual scrolling for large lists
- [ ] Debounced search and filters
- [ ] Optimized re-renders
- [ ] Memory leak prevention

**UI Component Hierarchy Status**: ✅ **COMPLETE - READY FOR IMPLEMENTATION**
