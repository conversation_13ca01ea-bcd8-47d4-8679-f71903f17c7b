/**
 * ============================================================================
 * M0 Components API Route - Server-Side M0 Integration
 * ============================================================================
 * 
 * This API route handles server-side integration with real M0 components,
 * avoiding browser compatibility issues with Node.js modules.
 * 
 * Features:
 * - Real M0 component integration on server-side
 * - Memory-safe patterns with BaseTrackingService
 * - Component health monitoring and metrics
 * - RESTful API for dashboard consumption
 * 
 * Author: AI Assistant (Phase 1 Day 2 Implementation)
 * Created: 2025-09-05
 * ============================================================================
 */

import { NextRequest, NextResponse } from 'next/server';

// Import the expanded M0ComponentManager
import { M0ComponentManager, getM0ComponentManager } from '../../../lib/M0ComponentManager';

// Types are now imported from M0ComponentManager

// API Route Handlers
export async function GET(): Promise<NextResponse> {
  try {
    console.log('� M0 Components API: Processing GET request...');

    const manager = await getM0ComponentManager();
    const dashboardData = manager.getDashboardData();

    console.log(`✅ M0 Components API: Returning data for ${dashboardData.totalComponents} components`);

    return NextResponse.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('❌ M0 Components API: Error processing request:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch M0 component data',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// POST handler for refresh functionality
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('📡 M0 Components API: Processing POST request (refresh)...');

    const body = await request.json();
    const { action } = body;

    if (action !== 'refresh') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid action',
          message: 'Only "refresh" action is supported',
          timestamp: new Date().toISOString()
        },
        { status: 400 }
      );
    }

    // Get fresh data from the manager
    const manager = await getM0ComponentManager();
    const dashboardData = manager.getDashboardData();

    console.log(`✅ M0 Components API: Refresh completed for ${dashboardData.totalComponents} components`);

    return NextResponse.json({
      success: true,
      data: dashboardData,
      message: 'Components refreshed successfully',
      timestamp: new Date().toISOString()
    }, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });

  } catch (error) {
    console.error('❌ M0 Components API: Error processing refresh request:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to refresh M0 component data',
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
