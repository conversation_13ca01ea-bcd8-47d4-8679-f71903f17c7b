/**
 * ============================================================================
 * M0 SECURITY API ROUTE
 * ============================================================================
 * 
 * API endpoint for M0 memory safety and security component data
 * Provides specialized security-focused data and memory monitoring
 * 
 * @route GET /api/m0-security - Get security component data
 * @route POST /api/m0-security - Execute security operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getM0ComponentManager } from '../../../lib/M0ComponentManager';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface ISecurityMetrics {
  memoryUsage: number;
  bufferUtilization: number;
  threatLevel: 'low' | 'medium' | 'high';
  activeProtections: number;
  lastSecurityScan: string;
}

interface ISecurityData {
  totalSecurityComponents: number;
  healthyComponents: number;
  errorComponents: number;
  metrics: ISecurityMetrics;
  components: Array<{
    id: string;
    name: string;
    status: string;
    category: string;
    healthScore: number;
    lastUpdate: string;
    securityType: 'memory-management' | 'buffer-protection' | 'event-handling' | 'environment-control';
  }>;
}

// ============================================================================
// API HANDLERS
// ============================================================================

/**
 * GET /api/m0-security
 * Returns security-specific component data and metrics
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const manager = getM0ComponentManager();
    const securityComponents = manager.getComponentsByCategory('memory-safety');
    
    // Calculate security metrics
    const healthyComponents = securityComponents.filter(c => c.status === 'healthy').length;
    const errorComponents = securityComponents.filter(c => c.status === 'error').length;
    
    // Calculate memory usage from components
    const totalMemoryUsage = securityComponents.reduce((sum, c) => sum + (c.metrics?.memoryUsage || 0), 0);
    
    // Count different types of security components
    const bufferComponents = securityComponents.filter(c => 
      c.name.includes('Buffer') || c.name.includes('Circular')
    ).length;
    
    const eventComponents = securityComponents.filter(c => 
      c.name.includes('Event') || c.name.includes('Handler')
    ).length;
    
    const environmentComponents = securityComponents.filter(c => 
      c.name.includes('Environment') || c.name.includes('Constants')
    ).length;
    
    // Determine threat level based on component health
    const threatLevel: 'low' | 'medium' | 'high' = errorComponents === 0 ? 'low' : 
                                                   errorComponents <= 1 ? 'medium' : 'high';
    
    // Categorize components by security type
    const categorizedComponents = securityComponents.map(component => ({
      ...component,
      securityType: getSecurityType(component.name)
    }));
    
    const securityData: ISecurityData = {
      totalSecurityComponents: securityComponents.length,
      healthyComponents,
      errorComponents,
      metrics: {
        memoryUsage: totalMemoryUsage,
        bufferUtilization: bufferComponents > 0 ? Math.floor(Math.random() * 40) + 30 : 0, // 30-70%
        threatLevel,
        activeProtections: healthyComponents,
        lastSecurityScan: new Date().toISOString()
      },
      components: categorizedComponents
    };
    
    return NextResponse.json({
      success: true,
      data: securityData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Security API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch security data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/m0-security
 * Execute security operations (memory scans, buffer analysis, etc.)
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { operation, componentId, parameters } = body;
    
    const manager = getM0ComponentManager();
    
    switch (operation) {
      case 'memory-scan':
        return await handleMemoryScan(manager, componentId, parameters);
      
      case 'buffer-analysis':
        return await handleBufferAnalysis(manager, componentId, parameters);
      
      case 'security-audit':
        return await handleSecurityAudit(manager, componentId, parameters);
      
      default:
        return NextResponse.json(
          { success: false, error: 'Unknown security operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Security operation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to execute security operation',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Determine security type based on component name
 */
function getSecurityType(componentName: string): 'memory-management' | 'buffer-protection' | 'event-handling' | 'environment-control' {
  if (componentName.includes('Memory') || componentName.includes('Resource')) {
    return 'memory-management';
  }
  if (componentName.includes('Buffer') || componentName.includes('Circular')) {
    return 'buffer-protection';
  }
  if (componentName.includes('Event') || componentName.includes('Handler')) {
    return 'event-handling';
  }
  if (componentName.includes('Environment') || componentName.includes('Constants')) {
    return 'environment-control';
  }
  return 'memory-management'; // default
}

/**
 * Handle memory scan operation
 */
async function handleMemoryScan(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate memory scan
  const scanResult = {
    componentId,
    memoryAllocated: Math.floor(Math.random() * 100) + 50, // MB
    memoryUsed: Math.floor(Math.random() * 80) + 20, // MB
    memoryLeaks: component.status === 'error' ? Math.floor(Math.random() * 5) + 1 : 0,
    fragmentationLevel: Math.floor(Math.random() * 20) + 5, // 5-25%
    recommendedAction: component.status === 'healthy' ? 'No action required' : 'Memory cleanup recommended',
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'memory-scan',
    result: scanResult
  });
}

/**
 * Handle buffer analysis operation
 */
async function handleBufferAnalysis(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate buffer analysis
  const analysisResult = {
    componentId,
    bufferSize: Math.floor(Math.random() * 2000) + 1000,
    bufferUtilization: Math.floor(Math.random() * 60) + 20, // 20-80%
    overflowEvents: component.status === 'error' ? Math.floor(Math.random() * 10) + 1 : 0,
    underflowEvents: component.status === 'error' ? Math.floor(Math.random() * 5) : 0,
    performanceScore: component.healthScore,
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'buffer-analysis',
    result: analysisResult
  });
}

/**
 * Handle security audit operation
 */
async function handleSecurityAudit(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate security audit
  const auditResult = {
    componentId,
    securityScore: component.healthScore,
    vulnerabilities: component.status === 'error' ? ['Memory leak detected', 'Buffer overflow risk'] : [],
    protections: ['Memory bounds checking', 'Buffer overflow protection', 'Resource cleanup'],
    lastAudit: new Date().toISOString(),
    nextAudit: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
    recommendations: component.status === 'healthy' 
      ? ['Continue monitoring', 'Regular security scans']
      : ['Immediate attention required', 'Review component configuration'],
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'security-audit',
    result: auditResult
  });
}
