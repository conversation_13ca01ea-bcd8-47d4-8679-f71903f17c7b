/**
 * ============================================================================
 * M0 GOVERNANCE API ROUTE
 * ============================================================================
 * 
 * API endpoint for M0 governance component data and operations
 * Provides specialized governance-focused data and controls
 * 
 * @route GET /api/m0-governance - Get governance component data
 * @route POST /api/m0-governance - Execute governance operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getM0ComponentManager } from '../../../lib/M0ComponentManager';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface IGovernanceMetrics {
  complianceScore: number;
  ruleCount: number;
  violationCount: number;
  frameworksActive: number;
  lastAudit: string;
}

interface IGovernanceData {
  totalGovernanceComponents: number;
  healthyComponents: number;
  errorComponents: number;
  metrics: IGovernanceMetrics;
  components: Array<{
    id: string;
    name: string;
    status: string;
    category: string;
    healthScore: number;
    lastUpdate: string;
    governanceType: 'rule-engine' | 'compliance' | 'framework' | 'analytics' | 'reporting';
  }>;
}

// ============================================================================
// API HANDLERS
// ============================================================================

/**
 * GET /api/m0-governance
 * Returns governance-specific component data and metrics
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const manager = getM0ComponentManager();
    const governanceComponents = manager.getComponentsByCategory('governance');
    
    // Calculate governance metrics
    const healthyComponents = governanceComponents.filter(c => c.status === 'healthy').length;
    const errorComponents = governanceComponents.filter(c => c.status === 'error').length;
    
    // Calculate compliance score based on component health
    const complianceScore = governanceComponents.length > 0 
      ? Math.round((healthyComponents / governanceComponents.length) * 100)
      : 0;
    
    // Count different types of governance components
    const ruleEngines = governanceComponents.filter(c => 
      c.name.includes('Engine') || c.name.includes('Core')
    ).length;
    
    const frameworks = governanceComponents.filter(c => 
      c.name.includes('Framework')
    ).length;
    
    const analytics = governanceComponents.filter(c => 
      c.name.includes('Analytics') || c.name.includes('Insights') || c.name.includes('Reporting')
    ).length;
    
    // Categorize components by governance type
    const categorizedComponents = governanceComponents.map(component => ({
      ...component,
      governanceType: getGovernanceType(component.name)
    }));
    
    const governanceData: IGovernanceData = {
      totalGovernanceComponents: governanceComponents.length,
      healthyComponents,
      errorComponents,
      metrics: {
        complianceScore,
        ruleCount: ruleEngines,
        violationCount: errorComponents,
        frameworksActive: frameworks,
        lastAudit: new Date().toISOString()
      },
      components: categorizedComponents
    };
    
    return NextResponse.json({
      success: true,
      data: governanceData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Governance API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch governance data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/m0-governance
 * Execute governance operations (compliance checks, rule validation, etc.)
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { operation, componentId, parameters } = body;
    
    const manager = getM0ComponentManager();
    
    switch (operation) {
      case 'compliance-check':
        return await handleComplianceCheck(manager, componentId, parameters);
      
      case 'rule-validation':
        return await handleRuleValidation(manager, componentId, parameters);
      
      case 'framework-audit':
        return await handleFrameworkAudit(manager, componentId, parameters);
      
      default:
        return NextResponse.json(
          { success: false, error: 'Unknown governance operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Governance operation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to execute governance operation',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Determine governance type based on component name
 */
function getGovernanceType(componentName: string): 'rule-engine' | 'compliance' | 'framework' | 'analytics' | 'reporting' {
  if (componentName.includes('Engine') || componentName.includes('Core')) {
    return 'rule-engine';
  }
  if (componentName.includes('Compliance') || componentName.includes('Validator')) {
    return 'compliance';
  }
  if (componentName.includes('Framework')) {
    return 'framework';
  }
  if (componentName.includes('Analytics') || componentName.includes('Insights') || componentName.includes('Optimization')) {
    return 'analytics';
  }
  if (componentName.includes('Reporting') || componentName.includes('Dashboard') || componentName.includes('Alert')) {
    return 'reporting';
  }
  return 'rule-engine'; // default
}

/**
 * Handle compliance check operation
 */
async function handleComplianceCheck(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate compliance check
  const complianceResult = {
    componentId,
    complianceScore: component.healthScore,
    status: component.status === 'healthy' ? 'compliant' : 'non-compliant',
    issues: component.status === 'error' ? ['Component health check failed'] : [],
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'compliance-check',
    result: complianceResult
  });
}

/**
 * Handle rule validation operation
 */
async function handleRuleValidation(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate rule validation
  const validationResult = {
    componentId,
    rulesValidated: Math.floor(Math.random() * 50) + 10,
    rulesValid: component.status === 'healthy' ? Math.floor(Math.random() * 50) + 10 : Math.floor(Math.random() * 30),
    rulesInvalid: component.status === 'error' ? Math.floor(Math.random() * 10) + 1 : 0,
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'rule-validation',
    result: validationResult
  });
}

/**
 * Handle framework audit operation
 */
async function handleFrameworkAudit(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate framework audit
  const auditResult = {
    componentId,
    auditScore: component.healthScore,
    frameworkVersion: '2.0.0',
    lastAudit: new Date().toISOString(),
    recommendations: component.status === 'error' 
      ? ['Review component configuration', 'Check system resources']
      : ['Component operating within normal parameters'],
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'framework-audit',
    result: auditResult
  });
}
