/**
 * ============================================================================
 * M0 INTEGRATION API ROUTE
 * ============================================================================
 * 
 * API endpoint for M0 integration component data and operations
 * Provides specialized integration-focused data and cross-component monitoring
 * 
 * @route GET /api/m0-integration - Get integration component data
 * @route POST /api/m0-integration - Execute integration operations
 */

import { NextRequest, NextResponse } from 'next/server';
import { getM0ComponentManager } from '../../../lib/M0ComponentManager';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

interface IIntegrationMetrics {
  activeBridges: number;
  messagesThroughput: number;
  integrationHealth: number;
  crossComponentCalls: number;
  lastIntegrationTest: string;
}

interface IIntegrationData {
  totalIntegrationComponents: number;
  healthyComponents: number;
  errorComponents: number;
  metrics: IIntegrationMetrics;
  components: Array<{
    id: string;
    name: string;
    status: string;
    category: string;
    healthScore: number;
    lastUpdate: string;
    integrationType: 'bridge' | 'coordinator' | 'monitor' | 'validator';
  }>;
}

// ============================================================================
// API HANDLERS
// ============================================================================

/**
 * GET /api/m0-integration
 * Returns integration-specific component data and metrics
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const manager = getM0ComponentManager();
    const integrationComponents = manager.getComponentsByCategory('integration');
    
    // Calculate integration metrics
    const healthyComponents = integrationComponents.filter(c => c.status === 'healthy').length;
    const errorComponents = integrationComponents.filter(c => c.status === 'error').length;
    
    // Calculate integration health score
    const integrationHealth = integrationComponents.length > 0 
      ? Math.round((healthyComponents / integrationComponents.length) * 100)
      : 0;
    
    // Count different types of integration components
    const bridges = integrationComponents.filter(c => 
      c.name.includes('Bridge')
    ).length;
    
    const coordinators = integrationComponents.filter(c => 
      c.name.includes('Coordinator') || c.name.includes('Realtime')
    ).length;
    
    const monitors = integrationComponents.filter(c => 
      c.name.includes('Monitor') || c.name.includes('Compliance')
    ).length;
    
    // Categorize components by integration type
    const categorizedComponents = integrationComponents.map(component => ({
      ...component,
      integrationType: getIntegrationType(component.name)
    }));
    
    const integrationData: IIntegrationData = {
      totalIntegrationComponents: integrationComponents.length,
      healthyComponents,
      errorComponents,
      metrics: {
        activeBridges: bridges,
        messagesThroughput: Math.floor(Math.random() * 10000) + 1000, // messages per minute
        integrationHealth,
        crossComponentCalls: integrationComponents.reduce((sum, c) => sum + (c.metrics?.operationCount || 0), 0),
        lastIntegrationTest: new Date().toISOString()
      },
      components: categorizedComponents
    };
    
    return NextResponse.json({
      success: true,
      data: integrationData,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Integration API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch integration data',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/m0-integration
 * Execute integration operations (bridge testing, coordination checks, etc.)
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { operation, componentId, parameters } = body;
    
    const manager = getM0ComponentManager();
    
    switch (operation) {
      case 'bridge-test':
        return await handleBridgeTest(manager, componentId, parameters);
      
      case 'coordination-check':
        return await handleCoordinationCheck(manager, componentId, parameters);
      
      case 'integration-health':
        return await handleIntegrationHealth(manager, componentId, parameters);
      
      default:
        return NextResponse.json(
          { success: false, error: 'Unknown integration operation' },
          { status: 400 }
        );
    }
    
  } catch (error) {
    console.error('Integration operation error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to execute integration operation',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Determine integration type based on component name
 */
function getIntegrationType(componentName: string): 'bridge' | 'coordinator' | 'monitor' | 'validator' {
  if (componentName.includes('Bridge')) {
    return 'bridge';
  }
  if (componentName.includes('Coordinator') || componentName.includes('Realtime')) {
    return 'coordinator';
  }
  if (componentName.includes('Monitor') || componentName.includes('Compliance')) {
    return 'monitor';
  }
  if (componentName.includes('Validator') || componentName.includes('Validation')) {
    return 'validator';
  }
  return 'bridge'; // default
}

/**
 * Handle bridge test operation
 */
async function handleBridgeTest(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate bridge test
  const testResult = {
    componentId,
    bridgeStatus: component.status === 'healthy' ? 'operational' : 'degraded',
    connectionLatency: Math.floor(Math.random() * 50) + 10, // 10-60ms
    messagesSent: Math.floor(Math.random() * 1000) + 100,
    messagesReceived: Math.floor(Math.random() * 1000) + 100,
    errorRate: component.status === 'error' ? Math.floor(Math.random() * 10) + 1 : 0,
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'bridge-test',
    result: testResult
  });
}

/**
 * Handle coordination check operation
 */
async function handleCoordinationCheck(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Simulate coordination check
  const coordinationResult = {
    componentId,
    coordinationStatus: component.status === 'healthy' ? 'synchronized' : 'out-of-sync',
    connectedComponents: Math.floor(Math.random() * 20) + 5,
    eventsSynchronized: Math.floor(Math.random() * 5000) + 1000,
    syncLatency: Math.floor(Math.random() * 100) + 20, // 20-120ms
    conflictResolutions: component.status === 'error' ? Math.floor(Math.random() * 5) + 1 : 0,
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'coordination-check',
    result: coordinationResult
  });
}

/**
 * Handle integration health operation
 */
async function handleIntegrationHealth(manager: any, componentId: string, parameters: any): Promise<NextResponse> {
  const component = manager.getComponentStatus(componentId);
  if (!component) {
    return NextResponse.json(
      { success: false, error: 'Component not found' },
      { status: 404 }
    );
  }
  
  // Get all components for system-wide health check
  const allComponents = manager.getDashboardData();
  const totalComponents = allComponents.totalComponents;
  const healthyComponents = allComponents.healthyComponents;
  
  // Simulate integration health check
  const healthResult = {
    componentId,
    integrationHealth: component.healthScore,
    systemWideHealth: Math.round((healthyComponents / totalComponents) * 100),
    dependencyStatus: component.status === 'healthy' ? 'all-dependencies-healthy' : 'dependency-issues-detected',
    integrationPoints: Math.floor(Math.random() * 10) + 3,
    dataFlowRate: Math.floor(Math.random() * 1000) + 500, // records per second
    lastHealthCheck: new Date().toISOString(),
    recommendations: component.status === 'healthy' 
      ? ['System integration operating normally', 'Continue monitoring']
      : ['Review component dependencies', 'Check integration configuration'],
    timestamp: new Date().toISOString()
  };
  
  return NextResponse.json({
    success: true,
    operation: 'integration-health',
    result: healthResult
  });
}
