'use client';

/**
 * M0 Real Dashboard - Main Page
 *
 * Enterprise-grade dashboard displaying real-time status of all 95+ M0 components
 * with 100% authentic integration (zero simulation/mocking)
 *
 * Authority: President & CEO, E<PERSON>Z. Consultancy
 * Purpose: Demonstrate operational M0 system capabilities
 * Status: Foundation Implementation
 */

import React, { useState, useEffect } from 'react';
import {
  m0ApiService,
  IM0DashboardData,
  IM0ComponentStatus,
  getStatusIcon,
  getStatusColor,
  getHealthScoreColor
} from '../lib/M0ApiService';

export default function M0RealDashboard() {
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<IM0DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    initializeM0Dashboard();

    // Set up real-time updates
    const updateInterval = setInterval(() => {
      refreshDashboardData();
    }, 5000); // Update every 5 seconds

    return () => {
      clearInterval(updateInterval);
    };
  }, []);

  const initializeM0Dashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🚀 Initializing M0 Real Dashboard with server-side API integration...');

      // Fetch initial dashboard data from API
      const initialData = await m0ApiService.getDashboardData(false); // Don't use cache on init
      setDashboardData(initialData);
      setLastUpdate(new Date());

      setLoading(false);
      console.log('✅ M0 Real Dashboard initialized successfully with expanded components');

    } catch (err) {
      console.error('❌ Failed to initialize M0 Real Dashboard:', err);
      setError(err instanceof Error ? err.message : 'Unknown initialization error');
      setLoading(false);
    }
  };

  const refreshDashboardData = async () => {
    try {
      const updatedData = await m0ApiService.getDashboardData(true); // Use cache for regular updates
      setDashboardData(updatedData);
      setLastUpdate(new Date());
    } catch (err) {
      console.warn('Failed to refresh dashboard data:', err);
      // Don't show error for background updates, just log it
    }
  };

  const handleManualRefresh = async () => {
    try {
      setIsRefreshing(true);
      const refreshedData = await m0ApiService.refreshComponents();
      setDashboardData(refreshedData);
      setLastUpdate(new Date());
    } catch (err) {
      console.error('Failed to manually refresh components:', err);
      setError(err instanceof Error ? err.message : 'Refresh failed');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Utility functions are now imported from M0ApiService

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900">
            Initializing M0 Real Dashboard...
          </h2>
          <p className="text-gray-600 mt-2">
            Connecting to expanded M0 component integration
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Discovering governance, tracking, memory safety, and integration components
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-600 text-6xl mb-4">❌</div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Dashboard Initialization Failed
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={initializeM0Dashboard}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
          >
            Retry Initialization
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-yellow-600 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-gray-900">
            No Dashboard Data Available
          </h2>
          <p className="text-gray-600 mt-2">
            M0ComponentManager initialized but no data received
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                M0 Real Component Integration Dashboard
              </h1>
              <p className="text-gray-600 mt-2">
                Real-time monitoring of {dashboardData.totalComponents} operational M0 components
              </p>
              <p className="text-sm text-gray-500 mt-1">
                Last updated: {lastUpdate.toLocaleTimeString()} • Phase 1 Day 2 - Server-Side API Integration
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleManualRefresh}
                disabled={isRefreshing}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              >
                <span className={isRefreshing ? 'animate-spin' : ''}>
                  {isRefreshing ? '🔄' : '🔄'}
                </span>
                <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
              </button>
            </div>
          </div>
          <div className="mt-3 flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">Live Data</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Real M0 Integration</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span className="text-sm text-gray-600">Server-Side API</span>
            </div>
          </div>
        </div>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{dashboardData.healthyComponents}</p>
                <p className="text-gray-600">Healthy Components</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{dashboardData.warningComponents}</p>
                <p className="text-gray-600">Warning Components</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-2xl font-semibold text-gray-900">{dashboardData.errorComponents}</p>
                <p className="text-gray-600">Error Components</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  dashboardData.overallHealthScore >= 90 ? 'bg-green-100' :
                  dashboardData.overallHealthScore >= 70 ? 'bg-yellow-100' : 'bg-red-100'
                }`}>
                  <svg className={`w-5 h-5 ${getHealthScoreColor(dashboardData.overallHealthScore)}`} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-4">
                <p className={`text-2xl font-semibold ${getHealthScoreColor(dashboardData.overallHealthScore)}`}>
                  {dashboardData.overallHealthScore}%
                </p>
                <p className="text-gray-600">Overall Health Score</p>
              </div>
            </div>
          </div>
        </div>

        {/* Component Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(dashboardData.categories).map(([categoryKey, components]) => {
            const categoryName = categoryKey === 'memorySafety' ? 'Memory Safety' :
                               categoryKey.charAt(0).toUpperCase() + categoryKey.slice(1);
            const categoryIcon = categoryKey === 'governance' ? '⚖️' :
                               categoryKey === 'tracking' ? '📊' :
                               categoryKey === 'memorySafety' ? '🛡️' : '🔗';

            return (
              <div key={categoryKey} className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{categoryIcon}</span>
                      <h3 className="text-lg font-medium text-gray-900">
                        {categoryName} Components ({components.length})
                      </h3>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="text-sm text-gray-500">
                        Health: {components.length > 0 ?
                          Math.round(components.reduce((sum, c) => sum + c.healthScore, 0) / components.length) : 0}%
                      </div>
                    </div>
                  </div>
                </div>
                <div className="px-6 py-4">
                  {components.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      No components in this category
                    </p>
                  ) : (
                    <div className="space-y-3">
                      {components.map((component) => (
                        <div
                          key={component.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
                        >
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">{getStatusIcon(component.status)}</span>
                            <div>
                              <p className="font-medium text-gray-900">{component.name}</p>
                              <p className="text-sm text-gray-500">
                                Last updated: {component.lastUpdate.toLocaleTimeString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="text-right">
                              <p className={`text-sm font-medium ${getHealthScoreColor(component.healthScore)}`}>
                                {component.healthScore}%
                              </p>
                              <p className="text-xs text-gray-500">
                                {component.metrics.responseTime}ms
                              </p>
                            </div>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(component.status)}`}>
                              {component.status.toUpperCase()}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* System Metrics */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">System Metrics</h3>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">
                  {Math.round(dashboardData.systemMetrics.averageResponseTime)}ms
                </p>
                <p className="text-sm text-gray-600">Average Response Time</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">
                  {dashboardData.systemMetrics.totalOperations.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Total Operations</p>
              </div>
              <div className="text-center">
                <p className={`text-2xl font-semibold ${getHealthScoreColor(100 - dashboardData.systemMetrics.errorRate)}`}>
                  {dashboardData.systemMetrics.errorRate.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-600">Error Rate</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-semibold text-gray-900">
                  {Math.round(dashboardData.systemMetrics.totalMemoryUsage)}MB
                </p>
                <p className="text-sm text-gray-600">Memory Usage</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            🚀 M0 Real Dashboard - Phase 1 Day 2 - Expanded Component Integration
          </p>
          <p className="mt-1">
            Enterprise-grade monitoring of {dashboardData.totalComponents} operational M0 system components
          </p>
          <p className="mt-1">
            ✅ Real M0 Integration • 🛡️ Memory-Safe Patterns • ⚡ Real-Time Updates
          </p>
        </div>
      </div>
    </div>
  );
}
