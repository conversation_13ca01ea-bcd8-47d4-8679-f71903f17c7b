#!/bin/bash

# M0 Demo Dashboard - Firewall Configuration Script
# Purpose: Configure firewall to allow network access to the dashboard
# Usage: ./configure-firewall.sh [enable|disable|status]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PORT=3000
SERVICE_NAME="M0 Dashboard"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  M0 Dashboard Firewall Setup   ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Detect OS and firewall
detect_firewall() {
    if command -v ufw >/dev/null 2>&1; then
        echo "ufw"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        echo "firewalld"
    elif command -v iptables >/dev/null 2>&1; then
        echo "iptables"
    else
        echo "unknown"
    fi
}

# Configure UFW (Ubuntu/Debian)
configure_ufw() {
    local action=$1
    
    case $action in
        enable)
            print_info "Configuring UFW firewall..."
            sudo ufw allow $PORT/tcp comment "$SERVICE_NAME"
            print_success "UFW rule added: Allow port $PORT/tcp"
            ;;
        disable)
            print_info "Removing UFW firewall rule..."
            sudo ufw delete allow $PORT/tcp
            print_success "UFW rule removed: Port $PORT/tcp"
            ;;
        status)
            print_info "UFW firewall status:"
            sudo ufw status | grep -E "(Status|$PORT)" || echo "No rules found for port $PORT"
            ;;
    esac
}

# Configure FirewallD (CentOS/RHEL/Fedora)
configure_firewalld() {
    local action=$1
    
    case $action in
        enable)
            print_info "Configuring FirewallD..."
            sudo firewall-cmd --permanent --add-port=$PORT/tcp
            sudo firewall-cmd --reload
            print_success "FirewallD rule added: Allow port $PORT/tcp"
            ;;
        disable)
            print_info "Removing FirewallD rule..."
            sudo firewall-cmd --permanent --remove-port=$PORT/tcp
            sudo firewall-cmd --reload
            print_success "FirewallD rule removed: Port $PORT/tcp"
            ;;
        status)
            print_info "FirewallD status:"
            sudo firewall-cmd --list-ports | grep $PORT || echo "Port $PORT not found in allowed ports"
            ;;
    esac
}

# Configure iptables (Generic Linux)
configure_iptables() {
    local action=$1
    
    case $action in
        enable)
            print_info "Configuring iptables..."
            sudo iptables -A INPUT -p tcp --dport $PORT -j ACCEPT
            print_success "iptables rule added: Allow port $PORT/tcp"
            print_warning "Note: iptables rules are not persistent. Consider using iptables-persistent package."
            ;;
        disable)
            print_info "Removing iptables rule..."
            sudo iptables -D INPUT -p tcp --dport $PORT -j ACCEPT 2>/dev/null || true
            print_success "iptables rule removed: Port $PORT/tcp"
            ;;
        status)
            print_info "iptables status:"
            sudo iptables -L INPUT | grep $PORT || echo "No rules found for port $PORT"
            ;;
    esac
}

# Test network connectivity
test_connectivity() {
    local host_ip=$(hostname -I | awk '{print $1}')
    
    print_info "Testing network connectivity..."
    echo "Host IP: $host_ip"
    echo "Dashboard URL: http://$host_ip:$PORT"
    echo
    
    # Test if port is listening
    if netstat -tlnp 2>/dev/null | grep -q ":$PORT "; then
        print_success "Port $PORT is listening"
    else
        print_warning "Port $PORT is not listening. Make sure the dashboard server is running."
    fi
    
    # Test HTTP response
    if command -v curl >/dev/null 2>&1; then
        print_info "Testing HTTP response..."
        if curl -s -o /dev/null -w "%{http_code}" "http://$host_ip:$PORT" | grep -q "200"; then
            print_success "Dashboard is accessible at http://$host_ip:$PORT"
        else
            print_warning "Dashboard may not be running or accessible"
        fi
    fi
}

# Main function
main() {
    local action=${1:-"help"}
    local firewall=$(detect_firewall)
    
    print_header
    
    case $action in
        enable)
            print_info "Enabling firewall access for $SERVICE_NAME on port $PORT..."
            echo
            
            case $firewall in
                ufw)
                    configure_ufw enable
                    ;;
                firewalld)
                    configure_firewalld enable
                    ;;
                iptables)
                    configure_iptables enable
                    ;;
                unknown)
                    print_error "No supported firewall found. Please configure manually."
                    exit 1
                    ;;
            esac
            
            echo
            test_connectivity
            ;;
            
        disable)
            print_info "Disabling firewall access for $SERVICE_NAME on port $PORT..."
            echo
            
            case $firewall in
                ufw)
                    configure_ufw disable
                    ;;
                firewalld)
                    configure_firewalld disable
                    ;;
                iptables)
                    configure_iptables disable
                    ;;
                unknown)
                    print_error "No supported firewall found. Please configure manually."
                    exit 1
                    ;;
            esac
            ;;
            
        status)
            print_info "Checking firewall status for $SERVICE_NAME..."
            echo "Detected firewall: $firewall"
            echo
            
            case $firewall in
                ufw)
                    configure_ufw status
                    ;;
                firewalld)
                    configure_firewalld status
                    ;;
                iptables)
                    configure_iptables status
                    ;;
                unknown)
                    print_warning "No supported firewall found."
                    ;;
            esac
            
            echo
            test_connectivity
            ;;
            
        test)
            test_connectivity
            ;;
            
        help|*)
            echo "Usage: $0 [enable|disable|status|test]"
            echo
            echo "Commands:"
            echo "  enable   - Allow network access to M0 Dashboard (port $PORT)"
            echo "  disable  - Block network access to M0 Dashboard (port $PORT)"
            echo "  status   - Show current firewall status"
            echo "  test     - Test network connectivity"
            echo "  help     - Show this help message"
            echo
            echo "Examples:"
            echo "  $0 enable    # Allow dashboard access"
            echo "  $0 status    # Check current status"
            echo "  $0 test      # Test connectivity"
            echo
            ;;
    esac
}

# Run main function with all arguments
main "$@"
