/**
 * Environment Configuration Utilities
 * Purpose: Centralized access to M0 demo environment variables
 */

export const ENV = {
  // Application Information
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'M0 Control Center',
  APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '4.1.0',

  // M0 Component Counts
  M0_COMPONENTS_COUNT: parseInt(process.env.NEXT_PUBLIC_M0_COMPONENTS_COUNT || '95'),
  GOVERNANCE_COMPONENTS: parseInt(process.env.NEXT_PUBLIC_GOVERNANCE_COMPONENTS || '61'),
  TRACKING_COMPONENTS: parseInt(process.env.NEXT_PUBLIC_TRACKING_COMPONENTS || '33'),
  MEMORY_SAFETY_COMPONENTS: parseInt(process.env.NEXT_PUBLIC_MEMORY_SAFETY_COMPONENTS || '14'),

  // M0 Achievement Metrics
  TOTAL_LOC: parseInt(process.env.NEXT_PUBLIC_TOTAL_LOC || '31545'),
  COMPLETION_PERCENTAGE: parseInt(process.env.NEXT_PUBLIC_COMPLETION_PERCENTAGE || '129'),
  PROTECTED_SERVICES: parseInt(process.env.NEXT_PUBLIC_PROTECTED_SERVICES || '22'),
  MEMORY_MAPS: parseInt(process.env.NEXT_PUBLIC_MEMORY_MAPS || '48'),
  TYPESCRIPT_ERRORS: parseInt(process.env.NEXT_PUBLIC_TYPESCRIPT_ERRORS || '0'),

  // Demo Configuration
  REFRESH_INTERVAL: parseInt(process.env.NEXT_PUBLIC_REFRESH_INTERVAL || '5000'),
  DEMO_MODE: process.env.NEXT_PUBLIC_DEMO_MODE === 'true',
  ENHANCED_IMPLEMENTATION: process.env.NEXT_PUBLIC_ENHANCED_IMPLEMENTATION === 'true',

  // Authority and Compliance
  AUTHORITY_COMPLIANCE: process.env.NEXT_PUBLIC_AUTHORITY_COMPLIANCE || 'E.Z.Consultancy',
  MILESTONE_STATUS: process.env.NEXT_PUBLIC_MILESTONE_STATUS || 'ENHANCED_COMPLETE',

  // Performance Settings
  MEMORY_UPDATE_INTERVAL: parseInt(process.env.NEXT_PUBLIC_MEMORY_UPDATE_INTERVAL || '3000'),
  GOVERNANCE_UPDATE_INTERVAL: parseInt(process.env.NEXT_PUBLIC_GOVERNANCE_UPDATE_INTERVAL || '5000'),
  TRACKING_UPDATE_INTERVAL: parseInt(process.env.NEXT_PUBLIC_TRACKING_UPDATE_INTERVAL || '5000'),
  INTEGRATION_UPDATE_INTERVAL: parseInt(process.env.NEXT_PUBLIC_INTEGRATION_UPDATE_INTERVAL || '10000'),

  // Foundation Readiness
  M1_READY: process.env.NEXT_PUBLIC_M1_READY === 'true',
  M2_READY: process.env.NEXT_PUBLIC_M2_READY === 'true',
  EXTENSION_POINTS: parseInt(process.env.NEXT_PUBLIC_EXTENSION_POINTS || '12'),
} as const;

// Type-safe environment validation
export function validateEnvironment(): boolean {
  const requiredVars = [
    'NEXT_PUBLIC_APP_NAME',
    'NEXT_PUBLIC_M0_COMPONENTS_COUNT',
    'NEXT_PUBLIC_TOTAL_LOC',
    'NEXT_PUBLIC_COMPLETION_PERCENTAGE',
  ];

  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.warn('Missing environment variables:', missing);
    return false;
  }

  return true;
}

// Development helper
export function logEnvironmentInfo(): void {
  if (process.env.NODE_ENV === 'development') {
    console.log('M0 Demo Environment:', {
      appName: ENV.APP_NAME,
      components: ENV.M0_COMPONENTS_COUNT,
      completion: `${ENV.COMPLETION_PERCENTAGE}%`,
      demoMode: ENV.DEMO_MODE,
    });
  }
}
