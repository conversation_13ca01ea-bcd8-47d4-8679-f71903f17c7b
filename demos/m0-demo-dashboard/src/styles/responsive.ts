/**
 * ============================================================================
 * AI CONTEXT: Responsive Utilities - M0 Demo Responsive Design System
 * Purpose: Centralized responsive design utilities and theme extensions
 * Complexity: Simple - Design system utilities
 * AI Navigation: 4 sections, responsive design domain
 * Lines: ~280 / Target limit: 300
 * ============================================================================
 */

import { Theme } from '@mui/material/styles';

// ============================================================================
// SECTION 1: BREAKPOINT DEFINITIONS
// AI Context: Consistent breakpoint values across the application
// ============================================================================

export const breakpoints = {
  xs: 0,
  sm: 600,
  md: 900,
  lg: 1200,
  xl: 1536
} as const;

export const mediaQueries = {
  mobile: `@media (max-width: ${breakpoints.sm - 1}px)`,
  tablet: `@media (min-width: ${breakpoints.sm}px) and (max-width: ${breakpoints.md - 1}px)`,
  desktop: `@media (min-width: ${breakpoints.md}px)`,
  largeDesktop: `@media (min-width: ${breakpoints.lg}px)`,
  extraLarge: `@media (min-width: ${breakpoints.xl}px)`
} as const;

// ============================================================================
// SECTION 2: RESPONSIVE SPACING UTILITIES
// AI Context: Consistent spacing patterns for responsive design
// ============================================================================

export const responsiveSpacing = {
  // Padding utilities
  padding: {
    compact: { xs: 1, sm: 1.5, md: 2 },
    normal: { xs: 1, sm: 2, md: 3 },
    spacious: { xs: 2, sm: 3, md: 4 },
    extraSpacious: { xs: 3, sm: 4, md: 6 }
  },
  
  // Margin utilities
  margin: {
    compact: { xs: 1, sm: 1.5, md: 2 },
    normal: { xs: 1, sm: 2, md: 3 },
    spacious: { xs: 2, sm: 3, md: 4 }
  },
  
  // Gap utilities for flexbox/grid
  gap: {
    tight: { xs: 0.5, sm: 1, md: 1.5 },
    normal: { xs: 1, sm: 2, md: 3 },
    loose: { xs: 2, sm: 3, md: 4 }
  }
} as const;

// ============================================================================
// SECTION 3: RESPONSIVE TYPOGRAPHY UTILITIES
// AI Context: Typography scaling for different screen sizes
// ============================================================================

export const responsiveTypography = {
  fontSize: {
    caption: { xs: '0.6rem', sm: '0.75rem' },
    body2: { xs: '0.75rem', sm: '0.875rem' },
    body1: { xs: '0.875rem', sm: '1rem' },
    subtitle2: { xs: '0.875rem', sm: '1rem', md: '1.125rem' },
    subtitle1: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
    h6: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
    h5: { xs: '1.125rem', sm: '1.25rem', md: '1.5rem' },
    h4: { xs: '1.25rem', sm: '1.5rem', md: '2.125rem' },
    h3: { xs: '1.5rem', sm: '2rem', md: '2.5rem' },
    h2: { xs: '2rem', sm: '2.5rem', md: '3rem' },
    h1: { xs: '2.5rem', sm: '3rem', md: '4rem' }
  },
  
  lineHeight: {
    tight: { xs: 1.2, sm: 1.3, md: 1.4 },
    normal: { xs: 1.4, sm: 1.5, md: 1.6 },
    loose: { xs: 1.6, sm: 1.7, md: 1.8 }
  }
} as const;

// ============================================================================
// SECTION 4: RESPONSIVE COMPONENT UTILITIES
// AI Context: Common responsive patterns for components
// ============================================================================

export const responsiveComponents = {
  // Card responsive styles
  card: {
    padding: responsiveSpacing.padding.normal,
    borderRadius: { xs: 1, sm: 2 },
    boxShadow: { xs: 1, sm: 2, md: 3 }
  },
  
  // Button responsive styles
  button: {
    padding: { xs: '8px 16px', sm: '10px 20px', md: '12px 24px' },
    fontSize: responsiveTypography.fontSize.body2,
    minHeight: { xs: 36, sm: 40, md: 44 }
  },
  
  // Icon responsive sizes
  icon: {
    small: { xs: 16, sm: 18, md: 20 },
    medium: { xs: 20, sm: 24, md: 28 },
    large: { xs: 28, sm: 32, md: 36 },
    extraLarge: { xs: 36, sm: 40, md: 48 }
  },
  
  // Grid responsive columns
  grid: {
    cards: { xs: 1, sm: 2, md: 3, lg: 4 },
    metrics: { xs: 1, sm: 2, md: 2, lg: 4 },
    buttons: { xs: 1, sm: 2, md: 4 },
    content: { xs: 1, sm: 1, md: 2, lg: 3 }
  }
} as const;

// ============================================================================
// RESPONSIVE HELPER FUNCTIONS
// AI Context: Utility functions for responsive design
// ============================================================================

export const createResponsiveValue = <T>(
  xs: T,
  sm?: T,
  md?: T,
  lg?: T,
  xl?: T
) => ({
  xs,
  ...(sm !== undefined && { sm }),
  ...(md !== undefined && { md }),
  ...(lg !== undefined && { lg }),
  ...(xl !== undefined && { xl })
});

export const getResponsiveValue = (
  base: number,
  multipliers: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number } = {}
) => ({
  xs: base * (multipliers.xs ?? 0.75),
  sm: base * (multipliers.sm ?? 0.875),
  md: base * (multipliers.md ?? 1),
  lg: base * (multipliers.lg ?? 1.125),
  xl: base * (multipliers.xl ?? 1.25)
});

export const createFlexResponsive = (
  direction: 'row' | 'column' = 'row',
  mobileDirection: 'row' | 'column' = 'column'
) => ({
  display: 'flex',
  flexDirection: { xs: mobileDirection, sm: direction },
  gap: responsiveSpacing.gap.normal
});

export const createGridResponsive = (
  columns: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number },
  gap: number | { xs?: number; sm?: number; md?: number; lg?: number; xl?: number } = 2
) => ({
  display: 'grid',
  gridTemplateColumns: {
    xs: `repeat(${columns.xs || 1}, 1fr)`,
    sm: `repeat(${columns.sm || 2}, 1fr)`,
    md: `repeat(${columns.md || 3}, 1fr)`,
    lg: `repeat(${columns.lg || 4}, 1fr)`,
    xl: `repeat(${columns.xl || columns.lg || 4}, 1fr)`
  },
  gap: typeof gap === 'number' ? gap : {
    xs: gap.xs || 2,
    sm: gap.sm || 2.5,
    md: gap.md || 3,
    lg: gap.lg || gap.md || 3,
    xl: gap.xl || gap.lg || gap.md || 3
  }
});

// Animation utilities
export const animations = {
  fadeIn: {
    '@keyframes fadeIn': {
      '0%': { opacity: 0, transform: 'translateY(10px)' },
      '100%': { opacity: 1, transform: 'translateY(0)' }
    },
    animation: 'fadeIn 0.3s ease-in-out'
  },
  
  slideUp: {
    '@keyframes slideUp': {
      '0%': { transform: 'translateY(20px)', opacity: 0 },
      '100%': { transform: 'translateY(0)', opacity: 1 }
    },
    animation: 'slideUp 0.4s ease-out'
  },
  
  scaleIn: {
    '@keyframes scaleIn': {
      '0%': { transform: 'scale(0.9)', opacity: 0 },
      '100%': { transform: 'scale(1)', opacity: 1 }
    },
    animation: 'scaleIn 0.3s ease-out'
  }
} as const;

// Hover effects
export const hoverEffects = {
  lift: {
    transition: 'all 0.3s ease-in-out',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: 3
    }
  },
  
  scale: {
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      transform: 'scale(1.02)'
    }
  },
  
  glow: {
    transition: 'all 0.3s ease-in-out',
    '&:hover': {
      boxShadow: '0 0 20px rgba(25, 118, 210, 0.3)'
    }
  }
} as const;

export default {
  breakpoints,
  mediaQueries,
  responsiveSpacing,
  responsiveTypography,
  responsiveComponents,
  createResponsiveValue,
  getResponsiveValue,
  createFlexResponsive,
  createGridResponsive,
  animations,
  hoverEffects
};
