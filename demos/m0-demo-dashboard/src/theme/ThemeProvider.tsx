'use client';

/**
 * Theme Provider Component for M0 Demo Dashboard
 * Purpose: Wraps the application with Material-UI theme and emotion cache
 * Hydration-safe implementation to prevent SSR mismatches
 */

import React, { useState, useEffect } from 'react';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import { m0Theme } from './theme';

// Create emotion cache for client-side rendering
const createEmotionCache = () => {
  return createCache({ key: 'css', prepend: true });
};

// Client-side cache
const clientSideEmotionCache = createEmotionCache();

interface ThemeProviderProps {
  children: React.ReactNode;
  emotionCache?: ReturnType<typeof createEmotionCache>;
}

export default function ThemeProvider({
  children,
  emotionCache = clientSideEmotionCache
}: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return children without theme during SSR to prevent hydration mismatch
    return <>{children}</>;
  }

  return (
    <CacheProvider value={emotionCache}>
      <MuiThemeProvider theme={m0Theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </CacheProvider>
  );
}
