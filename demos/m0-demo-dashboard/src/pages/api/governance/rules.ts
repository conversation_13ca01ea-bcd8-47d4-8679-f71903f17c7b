/**
 * Governance Rules API Endpoint
 * Purpose: Provide mock data for governance rule validation system
 * Represents 61+ governance components with G-TSK systems
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IGovernanceRule, IGovernanceRulesResponse, ICreateGovernanceRuleRequest, IUpdateGovernanceRuleRequest } from '../../../types/governance.types';
import { ENV } from '../../../utils/env';

// Mock governance rules data representing 61+ components
const mockGovernanceRules: IGovernanceRule[] = [
  // G-TSK System Rules (G-TSK-01 through G-TSK-08)
  {
    id: 'G-TSK-01',
    name: 'Authority Chain Validation',
    description: 'Validates E.Z. Consultancy → M0 → Operations authority flow',
    status: 'active',
    priority: 'critical',
    category: 'authority',
    authorityLevel: 'E.Z.Consultancy',
    complianceScore: 98.5,
    lastValidated: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    validationCount: 1247,
    failureCount: 3,
  },
  {
    id: 'G-TSK-02',
    name: 'Cross-Reference Integrity',
    description: 'Ensures component dependencies within M0 scope are valid',
    status: 'active',
    priority: 'high',
    category: 'cross-reference',
    authorityLevel: 'M0',
    complianceScore: 96.8,
    lastValidated: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago
    createdAt: '2024-01-15T10:15:00Z',
    updatedAt: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago
    validationCount: 892,
    failureCount: 1,
  },
  {
    id: 'G-TSK-03',
    name: 'Component Validation Engine',
    description: 'Validates all 95+ M0 components for compliance',
    status: 'active',
    priority: 'critical',
    category: 'validation',
    authorityLevel: 'M0',
    complianceScore: 99.2,
    lastValidated: new Date(Date.now() - 30 * 1000).toISOString(), // 30 seconds ago
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago
    validationCount: 2156,
    failureCount: 0,
  },
  {
    id: 'G-TSK-04',
    name: 'Compliance Scoring System',
    description: 'Calculates and maintains 122% compliance achievement',
    status: 'active',
    priority: 'high',
    category: 'compliance',
    authorityLevel: 'M0',
    complianceScore: 97.4,
    lastValidated: new Date(Date.now() - 45 * 1000).toISOString(), // 45 seconds ago
    createdAt: '2024-01-15T11:00:00Z',
    updatedAt: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    validationCount: 1678,
    failureCount: 2,
  },
  {
    id: 'G-TSK-05',
    name: 'Smart Path Resolution',
    description: 'Optimizes component interaction paths within M0',
    status: 'active',
    priority: 'medium',
    category: 'validation',
    authorityLevel: 'Operations',
    complianceScore: 94.7,
    lastValidated: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago
    createdAt: '2024-01-15T11:15:00Z',
    updatedAt: new Date(Date.now() - 4 * 60 * 1000).toISOString(), // 4 minutes ago
    validationCount: 743,
    failureCount: 5,
  },
  {
    id: 'G-TSK-06',
    name: 'Context Authority Protocol',
    description: 'Manages context-aware authority validation',
    status: 'active',
    priority: 'high',
    category: 'authority',
    authorityLevel: 'E.Z.Consultancy',
    complianceScore: 95.9,
    lastValidated: new Date(Date.now() - 90 * 1000).toISOString(), // 90 seconds ago
    createdAt: '2024-01-15T11:30:00Z',
    updatedAt: new Date(Date.now() - 6 * 60 * 1000).toISOString(), // 6 minutes ago
    validationCount: 1034,
    failureCount: 4,
  },
  {
    id: 'G-TSK-07',
    name: 'Orchestration Governance',
    description: 'Governs OrchestrationCoordinator functionality',
    status: 'active',
    priority: 'high',
    category: 'validation',
    authorityLevel: 'M0',
    complianceScore: 98.1,
    lastValidated: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    createdAt: '2024-01-15T11:45:00Z',
    updatedAt: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago
    validationCount: 567,
    failureCount: 1,
  },
  {
    id: 'G-TSK-08',
    name: 'Foundation Readiness Validation',
    description: 'Validates M0 foundation readiness for M1, M2+ milestones',
    status: 'active',
    priority: 'critical',
    category: 'validation',
    authorityLevel: 'E.Z.Consultancy',
    complianceScore: 99.7,
    lastValidated: new Date(Date.now() - 15 * 1000).toISOString(), // 15 seconds ago
    createdAt: '2024-01-15T12:00:00Z',
    updatedAt: new Date(Date.now() - 30 * 1000).toISOString(), // 30 seconds ago
    validationCount: 234,
    failureCount: 0,
  },
];

// Generate additional governance rules to reach 61+ components
const generateAdditionalRules = (): IGovernanceRule[] => {
  const additionalRules: IGovernanceRule[] = [];
  const categories = ['validation', 'compliance', 'authority', 'cross-reference'] as const;
  const priorities = ['low', 'medium', 'high', 'critical'] as const;
  const authorities = ['E.Z.Consultancy', 'M0', 'Operations'] as const;
  const statuses = ['active', 'inactive', 'pending'] as const;

  for (let i = 9; i <= ENV.GOVERNANCE_COMPONENTS; i++) {
    const category = categories[i % categories.length];
    const priority = priorities[i % priorities.length];
    const authority = authorities[i % authorities.length];
    const status = i <= 58 ? 'active' : statuses[i % statuses.length]; // Most rules active

    additionalRules.push({
      id: `GOV-${i.toString().padStart(3, '0')}`,
      name: `Governance Rule ${i}`,
      description: `Automated governance rule for ${category} management`,
      status,
      priority,
      category,
      authorityLevel: authority,
      complianceScore: Math.round((85 + Math.random() * 15) * 10) / 10, // 85-100%
      lastValidated: new Date(Date.now() - Math.random() * 10 * 60 * 1000).toISOString(), // 0-10 minutes ago
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(), // 0-30 days ago
      updatedAt: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString(), // 0-60 minutes ago
      validationCount: Math.floor(Math.random() * 2000) + 100,
      failureCount: Math.floor(Math.random() * 10),
    });
  }

  return additionalRules;
};

const allGovernanceRules = [...mockGovernanceRules, ...generateAdditionalRules()];

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IGovernanceRulesResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle pagination and filtering
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 20;
        const status = req.query.status as string;
        const category = req.query.category as string;
        const search = req.query.search as string;

        let filteredRules = allGovernanceRules;

        // Apply filters
        if (status) {
          filteredRules = filteredRules.filter(rule => rule.status === status);
        }
        if (category) {
          filteredRules = filteredRules.filter(rule => rule.category === category);
        }
        if (search) {
          filteredRules = filteredRules.filter(rule => 
            rule.name.toLowerCase().includes(search.toLowerCase()) ||
            rule.description.toLowerCase().includes(search.toLowerCase())
          );
        }

        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedRules = filteredRules.slice(startIndex, endIndex);

        const response: IGovernanceRulesResponse = {
          rules: paginatedRules,
          total: filteredRules.length,
          page,
          limit,
          hasMore: endIndex < filteredRules.length,
        };

        res.status(200).json(response);
      } else if (req.method === 'POST') {
        // Create new rule
        const newRuleData = req.body as ICreateGovernanceRuleRequest;
        
        const newRule: IGovernanceRule = {
          id: `GOV-${Date.now()}`,
          ...newRuleData,
          status: 'pending',
          complianceScore: 0,
          lastValidated: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          validationCount: 0,
          failureCount: 0,
        };

        allGovernanceRules.push(newRule);

        res.status(201).json({
          rules: [newRule],
          total: 1,
          page: 1,
          limit: 1,
          hasMore: false,
        });
      } else if (req.method === 'PUT') {
        // Update existing rule
        const updateData = req.body as IUpdateGovernanceRuleRequest;
        const ruleIndex = allGovernanceRules.findIndex(rule => rule.id === updateData.id);

        if (ruleIndex === -1) {
          res.status(404).json({ error: 'Rule not found' });
          return;
        }

        allGovernanceRules[ruleIndex] = {
          ...allGovernanceRules[ruleIndex],
          ...updateData,
          updatedAt: new Date().toISOString(),
        };

        res.status(200).json({
          rules: [allGovernanceRules[ruleIndex]],
          total: 1,
          page: 1,
          limit: 1,
          hasMore: false,
        });
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Governance rules API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
