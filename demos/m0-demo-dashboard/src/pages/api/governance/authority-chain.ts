/**
 * Governance Authority Chain API Endpoint
 * Purpose: Provide E.Z. Consultancy → M0 → Operations authority flow validation
 * Shows hierarchical authority structure and validation status
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IAuthorityChain, IAuthorityChainResponse } from '../../../types/governance.types';

// Define the authority chain structure
const authorityChainData: IAuthorityChain[] = [
  {
    level: 'E.Z.Consultancy',
    name: 'E.Z. Consultancy Authority',
    status: 'active',
    permissions: [
      'ULTIMATE_AUTHORITY',
      'MILESTONE_APPROVAL',
      'ARCHITECTURE_DECISIONS',
      'QUALITY_STANDARDS',
      'COMPLIANCE_OVERSIGHT',
      'STRATEGIC_DIRECTION',
      'RESOURCE_ALLOCATION',
      'GOVERNANCE_RULES',
    ],
    validationRequired: true,
    lastValidation: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    childLevels: ['M0'],
    parentLevel: undefined,
  },
  {
    level: 'M0',
    name: 'M0 Foundation Authority',
    status: 'active',
    permissions: [
      'COMPONENT_MANAGEMENT',
      'INTEGRATION_OVERSIGHT',
      'TECHNICAL_VALIDATION',
      'CROSS_REFERENCE_VALIDATION',
      'FOUNDATION_SERVICES',
      'MILESTONE_EXECUTION',
      'QUALITY_ASSURANCE',
      'DEPENDENCY_MANAGEMENT',
    ],
    validationRequired: true,
    lastValidation: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago
    childLevels: ['Operations'],
    parentLevel: 'E.Z.Consultancy',
  },
  {
    level: 'Operations',
    name: 'Operations Authority',
    status: 'active',
    permissions: [
      'DAILY_OPERATIONS',
      'MONITORING_OVERSIGHT',
      'PERFORMANCE_TRACKING',
      'ROUTINE_VALIDATION',
      'MAINTENANCE_TASKS',
      'OPERATIONAL_REPORTING',
      'SERVICE_COORDINATION',
      'INCIDENT_RESPONSE',
    ],
    validationRequired: false,
    lastValidation: new Date(Date.now() - 30 * 1000).toISOString(), // 30 seconds ago
    childLevels: [],
    parentLevel: 'M0',
  },
];

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IAuthorityChainResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Determine overall validation status
        const allActive = authorityChainData.every(level => level.status === 'active');
        const recentValidations = authorityChainData.every(level => {
          const validationTime = new Date(level.lastValidation).getTime();
          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
          return validationTime > fiveMinutesAgo;
        });

        const validationStatus = allActive && recentValidations ? 'valid' : 'pending';
        const now = new Date();

        const response: IAuthorityChainResponse = {
          chain: authorityChainData,
          validationStatus,
          lastValidation: new Date(Math.max(...authorityChainData.map(level => 
            new Date(level.lastValidation).getTime()
          ))).toISOString(),
          nextValidation: new Date(now.getTime() + 10 * 60 * 1000).toISOString(), // 10 minutes from now
        };

        res.status(200).json(response);
      } else if (req.method === 'POST') {
        // Trigger authority chain validation
        const now = new Date().toISOString();
        
        // Update validation timestamps
        authorityChainData.forEach(level => {
          level.lastValidation = now;
          level.status = 'active'; // Ensure all levels are active after validation
        });

        const response: IAuthorityChainResponse = {
          chain: authorityChainData,
          validationStatus: 'valid',
          lastValidation: now,
          nextValidation: new Date(Date.now() + 10 * 60 * 1000).toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Governance authority chain API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
