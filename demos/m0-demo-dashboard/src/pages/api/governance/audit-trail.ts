/**
 * Governance Audit Trail API Endpoint
 * Purpose: Provide audit log entries with authority-level filtering
 * Shows comprehensive governance activity tracking
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IAuditEntry, IAuditTrailResponse } from '../../../types/governance.types';

// Generate realistic audit entries
const generateAuditEntries = (): IAuditEntry[] => {
  const entries: IAuditEntry[] = [];
  const actions = [
    'Authority chain validation completed',
    'G-TSK system validation passed',
    'Cross-reference integrity check completed',
    'Compliance scoring updated',
    'Rule validation executed',
    'Component dependency verified',
    'Smart path resolution optimized',
    'Context authority protocol validated',
    'Orchestration governance applied',
    'Foundation readiness assessed',
  ];
  
  const components = [
    'G-TSK-01', 'G-TSK-02', 'G-TSK-03', 'G-TSK-04', 'G-TSK-05',
    'G-TSK-06', 'G-TSK-07', 'G-TSK-08', 'GovernanceEngine',
    'AuthorityManager', 'ComplianceValidator', 'CrossReferenceEngine',
    'SmartPathResolver', 'ContextAuthorityProtocol', 'OrchestrationGovernor',
  ];
  
  const authorities = ['E.Z.Consultancy', 'M0', 'Operations'] as const;
  const statuses = ['success', 'failure', 'warning', 'info'] as const;
  const impactLevels = ['low', 'medium', 'high', 'critical'] as const;

  // Generate entries for the last 24 hours
  const now = Date.now();
  const oneDayAgo = now - 24 * 60 * 60 * 1000;

  for (let i = 0; i < 150; i++) {
    const timestamp = new Date(oneDayAgo + Math.random() * (now - oneDayAgo));
    const action = actions[Math.floor(Math.random() * actions.length)];
    const component = components[Math.floor(Math.random() * components.length)];
    const authority = authorities[Math.floor(Math.random() * authorities.length)];
    const status = i < 130 ? 'success' : statuses[Math.floor(Math.random() * statuses.length)]; // Most successful
    const impactLevel = impactLevels[Math.floor(Math.random() * impactLevels.length)];

    entries.push({
      id: `audit-${i.toString().padStart(4, '0')}`,
      timestamp: timestamp.toISOString(),
      action,
      component,
      authorityLevel: authority,
      status,
      details: `${action} for component ${component} under ${authority} authority`,
      userId: Math.random() > 0.7 ? `user-${Math.floor(Math.random() * 10)}` : undefined,
      ruleId: component.startsWith('G-TSK') ? component : undefined,
      impactLevel,
    });
  }

  // Sort by timestamp (newest first)
  return entries.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

const auditEntries = generateAuditEntries();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IAuditTrailResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle pagination and filtering
        const page = parseInt(req.query.page as string) || 1;
        const limit = parseInt(req.query.limit as string) || 50;
        const authorityLevel = req.query.authorityLevel as string;
        const status = req.query.status as string;
        const component = req.query.component as string;
        const startDate = req.query.startDate as string;
        const endDate = req.query.endDate as string;

        let filteredEntries = auditEntries;

        // Apply filters
        if (authorityLevel) {
          filteredEntries = filteredEntries.filter(entry => entry.authorityLevel === authorityLevel);
        }
        if (status) {
          filteredEntries = filteredEntries.filter(entry => entry.status === status);
        }
        if (component) {
          filteredEntries = filteredEntries.filter(entry => 
            entry.component.toLowerCase().includes(component.toLowerCase())
          );
        }
        if (startDate) {
          filteredEntries = filteredEntries.filter(entry => 
            new Date(entry.timestamp) >= new Date(startDate)
          );
        }
        if (endDate) {
          filteredEntries = filteredEntries.filter(entry => 
            new Date(entry.timestamp) <= new Date(endDate)
          );
        }

        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedEntries = filteredEntries.slice(startIndex, endIndex);

        const response: IAuditTrailResponse = {
          entries: paginatedEntries,
          total: filteredEntries.length,
          page,
          limit,
          filters: {
            startDate,
            endDate,
            authorityLevel,
            status,
            component,
          },
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Governance audit trail API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
