/**
 * Governance Cross-Reference API Endpoint
 * Purpose: Provide component dependency validation within M0 scope
 * Shows cross-reference integrity and validation status
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { ICrossReference, ICrossReferenceResponse } from '../../../types/governance.types';
import { ENV } from '../../../utils/env';

// Generate realistic cross-reference data within M0 scope
const generateCrossReferences = (): ICrossReference[] => {
  const references: ICrossReference[] = [];
  
  // M0 component categories
  const governanceComponents = [
    'G-TSK-01', 'G-TSK-02', 'G-TSK-03', 'G-TSK-04', 'G-TSK-05',
    'G-TSK-06', 'G-TSK-07', 'G-TSK-08', 'GovernanceEngine',
    'AuthorityManager', 'ComplianceValidator', 'CrossReferenceEngine',
  ];
  
  const trackingComponents = [
    'SessionTrackingCore', 'SessionTrackingAudit', 'SessionTrackingRealtime',
    'SessionTrackingUtils', 'AnalyticsCacheManager', 'TrackingCoordinator',
    'PerformanceMonitor', 'ComponentHealthTracker', 'OrchestrationCoordinator',
  ];
  
  const securityComponents = [
    'BaseTrackingService', 'MemorySafeResourceManager', 'MemoryBoundaryEnforcer',
    'AttackPreventionSystem', 'SecurityValidator', 'ThreatDetectionEngine',
  ];
  
  const allComponents = [...governanceComponents, ...trackingComponents, ...securityComponents];
  const referenceTypes = ['dependency', 'validation', 'integration', 'inheritance'] as const;
  const statuses = ['valid', 'invalid', 'pending', 'deprecated'] as const;
  const frequencies = ['realtime', 'hourly', 'daily', 'weekly'] as const;
  const criticalityLevels = ['low', 'medium', 'high', 'critical'] as const;

  // Generate cross-references between components
  for (let i = 0; i < 284; i++) { // 284 interconnections as mentioned in integration dashboard
    const sourceComponent = allComponents[Math.floor(Math.random() * allComponents.length)];
    let targetComponent = allComponents[Math.floor(Math.random() * allComponents.length)];
    
    // Ensure source and target are different
    while (targetComponent === sourceComponent) {
      targetComponent = allComponents[Math.floor(Math.random() * allComponents.length)];
    }
    
    const referenceType = referenceTypes[Math.floor(Math.random() * referenceTypes.length)];
    const status = i < 270 ? 'valid' : statuses[Math.floor(Math.random() * statuses.length)]; // Most valid
    const frequency = frequencies[Math.floor(Math.random() * frequencies.length)];
    const criticalityLevel = criticalityLevels[Math.floor(Math.random() * criticalityLevels.length)];
    
    const lastValidated = new Date(Date.now() - Math.random() * 60 * 60 * 1000); // 0-60 minutes ago

    references.push({
      id: `xref-${i.toString().padStart(4, '0')}`,
      sourceComponent,
      targetComponent,
      referenceType,
      status,
      lastValidated: lastValidated.toISOString(),
      validationFrequency: frequency,
      criticalityLevel,
      withinM0Scope: true, // All references are within M0 scope for this demo
    });
  }

  return references.sort((a, b) => a.sourceComponent.localeCompare(b.sourceComponent));
};

const crossReferences = generateCrossReferences();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<ICrossReferenceResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle filtering
        const sourceComponent = req.query.sourceComponent as string;
        const targetComponent = req.query.targetComponent as string;
        const referenceType = req.query.referenceType as string;
        const status = req.query.status as string;
        const criticalityLevel = req.query.criticalityLevel as string;

        let filteredReferences = crossReferences;

        // Apply filters
        if (sourceComponent) {
          filteredReferences = filteredReferences.filter(ref => 
            ref.sourceComponent.toLowerCase().includes(sourceComponent.toLowerCase())
          );
        }
        if (targetComponent) {
          filteredReferences = filteredReferences.filter(ref => 
            ref.targetComponent.toLowerCase().includes(targetComponent.toLowerCase())
          );
        }
        if (referenceType) {
          filteredReferences = filteredReferences.filter(ref => ref.referenceType === referenceType);
        }
        if (status) {
          filteredReferences = filteredReferences.filter(ref => ref.status === status);
        }
        if (criticalityLevel) {
          filteredReferences = filteredReferences.filter(ref => ref.criticalityLevel === criticalityLevel);
        }

        // Calculate summary statistics
        const validReferences = filteredReferences.filter(ref => ref.status === 'valid').length;
        const invalidReferences = filteredReferences.filter(ref => ref.status === 'invalid').length;
        const pendingValidation = filteredReferences.filter(ref => ref.status === 'pending').length;

        const response: ICrossReferenceResponse = {
          references: filteredReferences,
          total: filteredReferences.length,
          validReferences,
          invalidReferences,
          pendingValidation,
          lastFullValidation: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
        };

        res.status(200).json(response);
      } else if (req.method === 'POST') {
        // Trigger cross-reference validation
        const now = new Date().toISOString();
        
        // Update validation timestamps for all references
        crossReferences.forEach(ref => {
          ref.lastValidated = now;
          // Simulate some validation results
          if (Math.random() > 0.95) {
            ref.status = 'invalid'; // 5% chance of becoming invalid
          } else {
            ref.status = 'valid';
          }
        });

        const validReferences = crossReferences.filter(ref => ref.status === 'valid').length;
        const invalidReferences = crossReferences.filter(ref => ref.status === 'invalid').length;
        const pendingValidation = crossReferences.filter(ref => ref.status === 'pending').length;

        const response: ICrossReferenceResponse = {
          references: crossReferences,
          total: crossReferences.length,
          validReferences,
          invalidReferences,
          pendingValidation,
          lastFullValidation: now,
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Governance cross-reference API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
