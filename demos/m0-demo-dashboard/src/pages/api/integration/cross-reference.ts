/**
 * Integration Cross-Reference API Endpoint
 * Purpose: Component dependency validation within M0 scope
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IEventCorrelation, IEventCorrelationResponse } from '../../../types/demo.types';

const generateEventCorrelations = (): IEventCorrelation[] => {
  const correlations: IEventCorrelation[] = [];
  const systems = ['governance', 'tracking', 'security', 'integration'] as const;
  const eventTypes = ['validation', 'monitoring', 'alert', 'update', 'sync'] as const;
  const statuses = ['success', 'failure', 'warning', 'info'] as const;

  for (let i = 0; i < 284; i++) { // 284 interconnections
    const sourceSystem = systems[Math.floor(Math.random() * systems.length)];
    let targetSystem = systems[Math.floor(Math.random() * systems.length)];
    while (targetSystem === sourceSystem) {
      targetSystem = systems[Math.floor(Math.random() * systems.length)];
    }

    correlations.push({
      correlationId: `corr-${i.toString().padStart(4, '0')}`,
      timestamp: new Date(Date.now() - Math.random() * 60 * 60 * 1000).toISOString(),
      sourceSystem,
      targetSystem,
      eventType: eventTypes[Math.floor(Math.random() * eventTypes.length)],
      status: i < 270 ? 'success' : statuses[Math.floor(Math.random() * statuses.length)],
      message: `${sourceSystem} → ${targetSystem} correlation`,
      details: { correlationStrength: Math.round(Math.random() * 100) },
      relatedEvents: [],
      withinM0Scope: true,
    });
  }

  return correlations.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

const eventCorrelations = generateEventCorrelations();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IEventCorrelationResponse | { error: string }>
) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        const governanceTracking = eventCorrelations.filter(e => 
          (e.sourceSystem === 'governance' && e.targetSystem === 'tracking') ||
          (e.sourceSystem === 'tracking' && e.targetSystem === 'governance')
        ).length;

        const securityGovernance = eventCorrelations.filter(e => 
          (e.sourceSystem === 'security' && e.targetSystem === 'governance') ||
          (e.sourceSystem === 'governance' && e.targetSystem === 'security')
        ).length;

        const trackingSecurity = eventCorrelations.filter(e => 
          (e.sourceSystem === 'tracking' && e.targetSystem === 'security') ||
          (e.sourceSystem === 'security' && e.targetSystem === 'tracking')
        ).length;

        const fullSystem = eventCorrelations.filter(e => e.sourceSystem === 'integration' || e.targetSystem === 'integration').length;

        const response: IEventCorrelationResponse = {
          events: eventCorrelations.slice(0, 50), // Latest 50 events
          correlations: {
            governanceTracking,
            securityGovernance,
            trackingSecurity,
            fullSystem,
          },
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Integration cross-reference API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
