/**
 * Integration Health Check API Endpoint
 * Purpose: System health across all 95+ M0 components
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IHealthCheck, IHealthCheckResponse } from '../../../types/demo.types';
import { ENV } from '../../../utils/env';

const generateHealthChecks = (): IHealthCheck[] => {
  const checks: IHealthCheck[] = [];
  const statuses = ['healthy', 'warning', 'critical', 'unknown'] as const;

  for (let i = 0; i < ENV.M0_COMPONENTS_COUNT; i++) {
    const status = i < 85 ? 'healthy' : i < 92 ? 'warning' : statuses[Math.floor(Math.random() * statuses.length)];
    const responseTime = status === 'healthy' ? Math.random() * 100 + 10 : Math.random() * 500 + 100;

    checks.push({
      checkId: `health-${i.toString().padStart(4, '0')}`,
      componentId: `component-${i.toString().padStart(4, '0')}`,
      timestamp: new Date(Date.now() - Math.random() * 5 * 60 * 1000).toISOString(),
      status,
      responseTime: Math.round(responseTime * 10) / 10,
      checks: {
        connectivity: Math.random() > 0.05,
        memory: Math.random() > 0.1,
        cpu: Math.random() > 0.08,
        dependencies: Math.random() > 0.03,
        functionality: Math.random() > 0.02,
      },
      details: `Component ${i} health check completed`,
      recommendations: status !== 'healthy' ? ['Monitor closely', 'Check logs', 'Verify dependencies'] : [],
    });
  }

  return checks;
};

const healthChecks = generateHealthChecks();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IHealthCheckResponse | { error: string }>
) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        const totalChecks = healthChecks.length;
        const healthyChecks = healthChecks.filter(c => c.status === 'healthy').length;
        const warningChecks = healthChecks.filter(c => c.status === 'warning').length;
        const criticalChecks = healthChecks.filter(c => c.status === 'critical').length;
        const score = Math.round((healthyChecks / totalChecks) * 100);

        const response: IHealthCheckResponse = {
          checks: healthChecks,
          overallHealth: {
            score,
            status: score > 90 ? 'healthy' : score > 70 ? 'warning' : 'critical',
            totalChecks,
            healthyChecks,
            warningChecks,
            criticalChecks,
          },
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Integration health check API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
