/**
 * System Dependencies API Endpoint
 * Purpose: Provide system dependency health and availability monitoring
 * Features: Dependency status, health checks, relationships, metadata
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { 
  ISystemDependency, 
  ISystemDependenciesResponse,
  IDependencyCheck,
  IDependencyMetadata,
  IDependencyRelationship
} from '../../../types/foundation.types';
import { ENV } from '../../../utils/env';

// Generate realistic system dependencies data
const generateDependencies = (): ISystemDependency[] => {
  const dependencies: ISystemDependency[] = [];
  const currentTime = new Date();

  // Dependency definitions with realistic scenarios
  const dependencyDefinitions = [
    {
      name: 'PostgreSQL Database',
      type: 'internal' as const,
      category: 'database' as const,
      baseHealth: 97,
      critical: true,
      responseTime: 15,
      version: '14.9',
    },
    {
      name: 'Redis Cache',
      type: 'internal' as const,
      category: 'database' as const,
      baseHealth: 98,
      critical: false,
      responseTime: 5,
      version: '7.0.12',
    },
    {
      name: 'Authentication Service',
      type: 'internal' as const,
      category: 'service' as const,
      baseHealth: 95,
      critical: true,
      responseTime: 25,
      version: '2.1.4',
    },
    {
      name: 'External Payment API',
      type: 'external' as const,
      category: 'api' as const,
      baseHealth: 92,
      critical: true,
      responseTime: 150,
      version: '3.2.1',
    },
    {
      name: 'Logging Service',
      type: 'internal' as const,
      category: 'service' as const,
      baseHealth: 94,
      critical: false,
      responseTime: 30,
      version: '1.8.7',
    },
    {
      name: 'Message Queue',
      type: 'internal' as const,
      category: 'infrastructure' as const,
      baseHealth: 96,
      critical: true,
      responseTime: 20,
      version: '3.9.2',
    },
    {
      name: 'Third-party Analytics',
      type: 'third-party' as const,
      category: 'api' as const,
      baseHealth: 89,
      critical: false,
      responseTime: 200,
      version: '4.1.0',
    },
    {
      name: 'File Storage Service',
      type: 'internal' as const,
      category: 'infrastructure' as const,
      baseHealth: 93,
      critical: false,
      responseTime: 45,
      version: '2.3.1',
    },
    {
      name: 'Monitoring Library',
      type: 'internal' as const,
      category: 'library' as const,
      baseHealth: 99,
      critical: false,
      responseTime: 2,
      version: '1.2.3',
    },
    {
      name: 'Email Service Provider',
      type: 'external' as const,
      category: 'api' as const,
      baseHealth: 91,
      critical: false,
      responseTime: 120,
      version: '2.0.5',
    },
  ];

  dependencyDefinitions.forEach((depDef, index) => {
    const dependencyId = `dep-${index + 1}`;
    const healthVariation = (Math.random() - 0.5) * 10;
    const health = Math.max(0, Math.min(100, depDef.baseHealth + healthVariation));
    
    let status: 'available' | 'unavailable' | 'degraded' | 'unknown';
    if (health >= 95) status = 'available';
    else if (health >= 80) status = 'degraded';
    else if (health >= 30) status = 'unavailable';
    else status = 'unknown';

    // Generate dependency checks
    const checks: IDependencyCheck[] = [
      {
        checkId: `${dependencyId}-ping`,
        checkName: 'Connectivity Check',
        type: 'ping',
        status: status === 'unavailable' ? 'failed' : Math.random() > 0.05 ? 'passed' : 'warning',
        value: depDef.responseTime + (Math.random() - 0.5) * 20,
        threshold: depDef.responseTime * 2,
        unit: 'ms',
        timestamp: new Date(currentTime.getTime() - Math.random() * 300000).toISOString(),
        duration: Math.random() * 1000,
      },
      {
        checkId: `${dependencyId}-health`,
        checkName: 'Health Check',
        type: 'health',
        status: health > 90 ? 'passed' : health > 70 ? 'warning' : 'failed',
        value: health,
        threshold: 90,
        unit: '%',
        timestamp: new Date(currentTime.getTime() - Math.random() * 300000).toISOString(),
        duration: Math.random() * 500,
      },
      {
        checkId: `${dependencyId}-version`,
        checkName: 'Version Check',
        type: 'version',
        status: Math.random() > 0.1 ? 'passed' : 'warning',
        value: depDef.version,
        message: Math.random() > 0.9 ? 'Version update available' : undefined,
        timestamp: new Date(currentTime.getTime() - Math.random() * 300000).toISOString(),
        duration: Math.random() * 200,
      },
    ];

    // Generate dependency metadata
    const metadata: IDependencyMetadata = {
      description: `${depDef.name} - ${depDef.category} dependency for M0 foundation`,
      documentation: `https://docs.example.com/${depDef.name.toLowerCase().replace(/\s+/g, '-')}`,
      maintainer: depDef.type === 'internal' ? 'Internal Team' : depDef.type === 'external' ? 'External Provider' : 'Third Party',
      supportLevel: depDef.critical ? 'full' : Math.random() > 0.5 ? 'limited' : 'community',
      licenseType: depDef.type === 'internal' ? 'Proprietary' : Math.random() > 0.5 ? 'MIT' : 'Apache 2.0',
      securityRating: health > 95 ? 'high' : health > 80 ? 'medium' : 'low',
      updateFrequency: depDef.critical ? 'weekly' : Math.random() > 0.5 ? 'monthly' : 'quarterly',
    };

    // Generate relationships (will be populated later)
    const relationships: IDependencyRelationship[] = [];

    dependencies.push({
      dependencyId,
      dependencyName: depDef.name,
      type: depDef.type,
      category: depDef.category,
      status,
      health,
      version: depDef.version,
      critical: depDef.critical,
      responseTime: depDef.responseTime + (Math.random() - 0.5) * 10,
      lastCheck: new Date(currentTime.getTime() - Math.random() * 300000).toISOString(),
      checks,
      metadata,
      relationships,
    });
  });

  return dependencies;
};

// Generate dependency relationships
const generateRelationships = (dependencies: ISystemDependency[]): IDependencyRelationship[] => {
  const relationships: IDependencyRelationship[] = [];
  
  // Define realistic relationships
  const relationshipDefinitions = [
    {
      from: 'Authentication Service',
      to: 'PostgreSQL Database',
      type: 'depends-on' as const,
      strength: 0.9,
      description: 'Authentication service stores user data in PostgreSQL',
    },
    {
      from: 'Authentication Service',
      to: 'Redis Cache',
      type: 'depends-on' as const,
      strength: 0.7,
      description: 'Authentication service uses Redis for session caching',
    },
    {
      from: 'External Payment API',
      to: 'Logging Service',
      type: 'integrates-with' as const,
      strength: 0.6,
      description: 'Payment API logs transactions to logging service',
    },
    {
      from: 'Message Queue',
      to: 'Logging Service',
      type: 'provides-to' as const,
      strength: 0.8,
      description: 'Message queue provides async logging capabilities',
    },
    {
      from: 'File Storage Service',
      to: 'PostgreSQL Database',
      type: 'depends-on' as const,
      strength: 0.5,
      description: 'File storage service stores metadata in PostgreSQL',
    },
  ];

  relationshipDefinitions.forEach((relDef, index) => {
    const fromDep = dependencies.find(d => d.dependencyName === relDef.from);
    const toDep = dependencies.find(d => d.dependencyName === relDef.to);
    
    if (fromDep && toDep) {
      relationships.push({
        relationshipId: `rel-${index + 1}`,
        relatedDependency: toDep.dependencyId,
        relationshipType: relDef.type,
        strength: relDef.strength,
        bidirectional: false,
        description: relDef.description,
      });
    }
  });

  return relationships;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ISystemDependenciesResponse>
) {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1400 + Math.random() * 1800));

    const dependencies = generateDependencies();
    const relationships = generateRelationships(dependencies);
    
    // Calculate summary statistics
    const total = dependencies.length;
    const available = dependencies.filter(d => d.status === 'available').length;
    const unavailable = dependencies.filter(d => d.status === 'unavailable').length;
    const degraded = dependencies.filter(d => d.status === 'degraded').length;
    const unknown = dependencies.filter(d => d.status === 'unknown').length;
    const critical = dependencies.filter(d => d.critical).length;
    const overallHealth = dependencies.reduce((sum, d) => sum + d.health, 0) / Math.max(1, dependencies.length);
    const averageResponseTime = dependencies.reduce((sum, d) => sum + d.responseTime, 0) / Math.max(1, dependencies.length);

    // Calculate category statistics
    const categories: { [key: string]: { count: number; health: number; availability: number } } = {};
    const categoryGroups = dependencies.reduce((groups, dep) => {
      if (!groups[dep.category]) {
        groups[dep.category] = [];
      }
      groups[dep.category].push(dep);
      return groups;
    }, {} as { [key: string]: ISystemDependency[] });

    Object.entries(categoryGroups).forEach(([category, items]) => {
      const avgHealth = items.reduce((sum, item) => sum + item.health, 0) / items.length;
      const availableCount = items.filter(item => item.status === 'available').length;
      const availability = (availableCount / items.length) * 100;
      
      categories[category] = {
        count: items.length,
        health: avgHealth,
        availability,
      };
    });

    const response: ISystemDependenciesResponse = {
      dependencies,
      summary: {
        total,
        available,
        unavailable,
        degraded,
        unknown,
        critical,
        overallHealth,
        averageResponseTime,
      },
      categories,
      relationships,
      lastUpdate: new Date().toISOString(),
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('System dependencies API error:', error);
    res.status(500).json({
      dependencies: [],
      summary: {
        total: 0,
        available: 0,
        unavailable: 0,
        degraded: 0,
        unknown: 0,
        critical: 0,
        overallHealth: 0,
        averageResponseTime: 0,
      },
      categories: {},
      relationships: [],
      lastUpdate: new Date().toISOString(),
    });
  }
}
