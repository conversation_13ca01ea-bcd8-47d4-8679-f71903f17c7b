/**
 * Security Attack Simulation API Endpoint
 * Purpose: Provide attack simulation endpoints and prevention metrics
 * Shows memory attack simulation and protection response
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IAttackSimulation, ISimulationLog, IAttackSimulationResponse } from '../../../types/security.types';

// Generate realistic attack simulation data
const generateAttackSimulations = (): IAttackSimulation[] => {
  const simulations: IAttackSimulation[] = [];
  const simulationTypes = ['memory-exhaustion', 'buffer-overflow', 'stress-test', 'penetration-test'] as const;
  const statuses = ['pending', 'running', 'completed', 'failed', 'cancelled'] as const;
  const intensities = ['low', 'medium', 'high', 'maximum'] as const;
  const impactLevels = ['none', 'minimal', 'moderate', 'severe'] as const;

  for (let i = 0; i < 20; i++) {
    const type = simulationTypes[Math.floor(Math.random() * simulationTypes.length)];
    const status = i < 15 ? 'completed' : statuses[Math.floor(Math.random() * statuses.length)];
    const intensity = intensities[Math.floor(Math.random() * intensities.length)];
    const duration = Math.floor(Math.random() * 300) + 60; // 1-5 minutes
    const startTime = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);
    const endTime = status === 'completed' ? new Date(startTime.getTime() + duration * 1000) : undefined;

    // Generate simulation logs
    const logs: ISimulationLog[] = [];
    const logCount = Math.floor(Math.random() * 10) + 5;
    for (let j = 0; j < logCount; j++) {
      const logTime = new Date(startTime.getTime() + (j * duration * 1000 / logCount));
      logs.push({
        timestamp: logTime.toISOString(),
        level: j === 0 ? 'info' : Math.random() > 0.8 ? 'warning' : 'info',
        message: `Simulation step ${j + 1}: ${type} attack in progress`,
        serviceId: `service-${Math.floor(Math.random() * 10).toString().padStart(3, '0')}`,
        memoryUsage: Math.floor(Math.random() * 200) + 50,
        responseTime: Math.round((100 + Math.random() * 500) * 10) / 10,
      });
    }

    simulations.push({
      simulationId: `sim-${i.toString().padStart(4, '0')}`,
      name: `${type.charAt(0).toUpperCase() + type.slice(1)} Simulation ${i + 1}`,
      type,
      status,
      targetServices: [`service-${Math.floor(Math.random() * 22).toString().padStart(3, '0')}`],
      startTime: startTime.toISOString(),
      endTime: endTime?.toISOString(),
      duration,
      parameters: {
        intensity,
        duration,
        targetMemory: Math.floor(Math.random() * 500) + 100,
        concurrentRequests: Math.floor(Math.random() * 1000) + 100,
        payloadSize: Math.floor(Math.random() * 10000) + 1000,
      },
      results: status === 'completed' ? {
        successful: Math.random() > 0.8, // 20% successful attacks (protection working)
        protectionTriggered: Math.random() > 0.2, // 80% protection triggered
        mitigationTime: Math.floor(Math.random() * 5000) + 500, // 0.5-5.5 seconds
        impactLevel: impactLevels[Math.floor(Math.random() * impactLevels.length)],
        servicesAffected: Math.floor(Math.random() * 3),
        memoryPeakUsage: Math.floor(Math.random() * 300) + 100,
        responseTimeImpact: Math.round((Math.random() * 200) * 10) / 10,
      } : {
        successful: false,
        protectionTriggered: false,
        mitigationTime: 0,
        impactLevel: 'none',
        servicesAffected: 0,
        memoryPeakUsage: 0,
        responseTimeImpact: 0,
      },
      logs,
    });
  }

  return simulations.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
};

const attackSimulations = generateAttackSimulations();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IAttackSimulationResponse | { error: string }>
) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        const type = req.query.type as string;
        const status = req.query.status as string;

        let filteredSimulations = attackSimulations;
        if (type) filteredSimulations = filteredSimulations.filter(sim => sim.type === type);
        if (status) filteredSimulations = filteredSimulations.filter(sim => sim.status === status);

        const activeSimulations = filteredSimulations.filter(s => s.status === 'running').length;
        const completedSimulations = filteredSimulations.filter(s => s.status === 'completed').length;
        const successfulAttacks = filteredSimulations.filter(s => s.results.successful).length;
        const blockedAttacks = completedSimulations - successfulAttacks;

        const response: IAttackSimulationResponse = {
          simulations: filteredSimulations,
          activeSimulations,
          completedSimulations,
          successfulAttacks,
          blockedAttacks,
          lastSimulation: filteredSimulations.length > 0 ? filteredSimulations[0].startTime : undefined,
        };

        res.status(200).json(response);
      } else if (req.method === 'POST') {
        // Start new simulation
        const { type, intensity, targetServices } = req.body;
        const newSimulation: IAttackSimulation = {
          simulationId: `sim-${Date.now()}`,
          name: `${type} Simulation`,
          type,
          status: 'running',
          targetServices: targetServices || ['service-001'],
          startTime: new Date().toISOString(),
          duration: 0,
          parameters: {
            intensity: intensity || 'medium',
            duration: 300,
            targetMemory: 200,
            concurrentRequests: 500,
            payloadSize: 5000,
          },
          results: {
            successful: false,
            protectionTriggered: false,
            mitigationTime: 0,
            impactLevel: 'none',
            servicesAffected: 0,
            memoryPeakUsage: 0,
            responseTimeImpact: 0,
          },
          logs: [{
            timestamp: new Date().toISOString(),
            level: 'info',
            message: 'Attack simulation started',
          }],
        };

        attackSimulations.unshift(newSimulation);
        res.status(201).json({ simulations: [newSimulation], activeSimulations: 1, completedSimulations: 0, successfulAttacks: 0, blockedAttacks: 0 });
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Security attack simulation API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
