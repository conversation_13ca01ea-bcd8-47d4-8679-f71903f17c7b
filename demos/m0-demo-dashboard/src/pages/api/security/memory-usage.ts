/**
 * Security Memory Usage API Endpoint
 * Purpose: Provide real-time memory data for 22+ protected services
 * Shows memory boundary enforcement and BaseTrackingService protection
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IMemorySafetyMetrics, IMemoryDataPoint, ISecurityAlert, IMemoryUsageResponse } from '../../../types/security.types';
import { ENV } from '../../../utils/env';

// Generate realistic memory safety metrics
const generateMemorySafetyMetrics = (): IMemorySafetyMetrics[] => {
  const services: IMemorySafetyMetrics[] = [];
  const serviceTypes = ['BaseTrackingService', 'MemorySafeResourceManager', 'Enhanced', 'Utility'] as const;
  const statuses = ['protected', 'vulnerable', 'monitoring', 'offline'] as const;
  const protectionLevels = ['basic', 'enhanced', 'enterprise', 'maximum'] as const;

  // Core protected services
  const coreServices = [
    { name: 'SessionTrackingCore', type: 'BaseTrackingService' as const, protectionLevel: 'enterprise' as const },
    { name: 'SessionTrackingAudit', type: 'BaseTrackingService' as const, protectionLevel: 'enhanced' as const },
    { name: 'SessionTrackingRealtime', type: 'BaseTrackingService' as const, protectionLevel: 'maximum' as const },
    { name: 'AnalyticsCacheManager', type: 'Enhanced' as const, protectionLevel: 'enterprise' as const },
    { name: 'GovernanceEngine', type: 'BaseTrackingService' as const, protectionLevel: 'maximum' as const },
    { name: 'AuthorityManager', type: 'MemorySafeResourceManager' as const, protectionLevel: 'enhanced' as const },
    { name: 'ComplianceValidator', type: 'BaseTrackingService' as const, protectionLevel: 'enterprise' as const },
    { name: 'CrossReferenceEngine', type: 'Enhanced' as const, protectionLevel: 'enhanced' as const },
    { name: 'OrchestrationCoordinator', type: 'BaseTrackingService' as const, protectionLevel: 'maximum' as const },
    { name: 'SmartPathResolver', type: 'MemorySafeResourceManager' as const, protectionLevel: 'enhanced' as const },
  ];

  // Generate core services
  coreServices.forEach((serviceConfig, index) => {
    const currentMemory = Math.floor(Math.random() * 200) + 50; // 50-250MB
    const maxMemory = Math.floor(currentMemory * (1.5 + Math.random() * 0.5)); // 1.5-2x current
    const threshold = Math.floor(maxMemory * 0.8); // 80% threshold
    const percentage = Math.round((currentMemory / maxMemory) * 100);
    
    // Generate memory history (last 24 hours)
    const memoryHistory: IMemoryDataPoint[] = [];
    const now = Date.now();
    for (let i = 0; i < 24; i++) {
      const timestamp = new Date(now - (23 - i) * 60 * 60 * 1000);
      const baseMemory = currentMemory + (Math.random() - 0.5) * 20; // ±10MB variation
      memoryHistory.push({
        timestamp: timestamp.toISOString(),
        memoryUsage: Math.max(10, Math.floor(baseMemory)),
        cpuUsage: Math.round((5 + Math.random() * 25) * 10) / 10, // 5-30%
        activeConnections: Math.floor(Math.random() * 100) + 10,
        requestCount: Math.floor(Math.random() * 1000) + 100,
        errorCount: Math.floor(Math.random() * 5),
      });
    }

    // Generate alerts
    const alerts: ISecurityAlert[] = [];
    if (percentage > 90) {
      alerts.push({
        alertId: `alert-${index}-memory`,
        serviceId: `service-${index.toString().padStart(3, '0')}`,
        type: 'memory-threshold',
        severity: 'high',
        status: 'active',
        timestamp: new Date(now - Math.random() * 60 * 60 * 1000).toISOString(),
        message: `Memory usage above 90% threshold`,
        details: `Current usage: ${percentage}%, Threshold: 80%`,
        actions: ['Scale resources', 'Clear cache', 'Restart service'],
        relatedAlerts: [],
      });
    }

    services.push({
      serviceId: `service-${index.toString().padStart(3, '0')}`,
      serviceName: serviceConfig.name,
      type: serviceConfig.type,
      status: percentage < 85 ? 'protected' : percentage < 95 ? 'monitoring' : 'vulnerable',
      memoryUsage: {
        current: currentMemory,
        maximum: maxMemory,
        threshold,
        percentage,
      },
      boundaryEnforcement: {
        enabled: true,
        boundaryCount: Math.floor(Math.random() * 10) + 5, // 5-15 boundaries
        violationCount: Math.floor(Math.random() * 3), // 0-3 violations
        lastViolation: Math.random() > 0.7 ? new Date(now - Math.random() * 24 * 60 * 60 * 1000).toISOString() : undefined,
      },
      protectionLevel: serviceConfig.protectionLevel,
      lastMemoryCheck: new Date(now - Math.random() * 5 * 60 * 1000).toISOString(), // 0-5 minutes ago
      memoryHistory,
      alerts,
    });
  });

  // Generate additional services to reach 22+ protected services
  for (let i = coreServices.length; i < ENV.PROTECTED_SERVICES; i++) {
    const type = serviceTypes[Math.floor(Math.random() * serviceTypes.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const protectionLevel = protectionLevels[Math.floor(Math.random() * protectionLevels.length)];
    
    const currentMemory = Math.floor(Math.random() * 150) + 30;
    const maxMemory = Math.floor(currentMemory * (1.3 + Math.random() * 0.7));
    const threshold = Math.floor(maxMemory * 0.8);
    const percentage = Math.round((currentMemory / maxMemory) * 100);

    // Generate simplified memory history
    const memoryHistory: IMemoryDataPoint[] = [];
    const now = Date.now();
    for (let j = 0; j < 12; j++) { // 12 hours of data
      const timestamp = new Date(now - (11 - j) * 2 * 60 * 60 * 1000);
      memoryHistory.push({
        timestamp: timestamp.toISOString(),
        memoryUsage: Math.max(10, Math.floor(currentMemory + (Math.random() - 0.5) * 15)),
        cpuUsage: Math.round((3 + Math.random() * 20) * 10) / 10,
        activeConnections: Math.floor(Math.random() * 50) + 5,
        requestCount: Math.floor(Math.random() * 500) + 50,
        errorCount: Math.floor(Math.random() * 3),
      });
    }

    services.push({
      serviceId: `service-${i.toString().padStart(3, '0')}`,
      serviceName: `ProtectedService${i}`,
      type,
      status,
      memoryUsage: {
        current: currentMemory,
        maximum: maxMemory,
        threshold,
        percentage,
      },
      boundaryEnforcement: {
        enabled: Math.random() > 0.1, // 90% enabled
        boundaryCount: Math.floor(Math.random() * 8) + 3,
        violationCount: Math.floor(Math.random() * 2),
        lastViolation: Math.random() > 0.8 ? new Date(now - Math.random() * 48 * 60 * 60 * 1000).toISOString() : undefined,
      },
      protectionLevel,
      lastMemoryCheck: new Date(now - Math.random() * 10 * 60 * 1000).toISOString(),
      memoryHistory,
      alerts: [],
    });
  }

  return services;
};

const memorySafetyMetrics = generateMemorySafetyMetrics();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IMemoryUsageResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle filtering
        const status = req.query.status as string;
        const type = req.query.type as string;
        const protectionLevel = req.query.protectionLevel as string;

        let filteredServices = memorySafetyMetrics;

        // Apply filters
        if (status) {
          filteredServices = filteredServices.filter(service => service.status === status);
        }
        if (type) {
          filteredServices = filteredServices.filter(service => service.type === type);
        }
        if (protectionLevel) {
          filteredServices = filteredServices.filter(service => service.protectionLevel === protectionLevel);
        }

        // Calculate summary statistics
        const totalServices = filteredServices.length;
        const protectedServices = filteredServices.filter(s => s.status === 'protected').length;
        const vulnerableServices = filteredServices.filter(s => s.status === 'vulnerable').length;
        const averageMemoryUsage = totalServices > 0 
          ? Math.round((filteredServices.reduce((sum, s) => sum + s.memoryUsage.percentage, 0) / totalServices) * 10) / 10
          : 0;
        const totalMemoryUsage = filteredServices.reduce((sum, s) => sum + s.memoryUsage.current, 0);
        const totalBoundaries = filteredServices.reduce((sum, s) => sum + s.boundaryEnforcement.boundaryCount, 0);
        const totalViolations = filteredServices.reduce((sum, s) => sum + s.boundaryEnforcement.violationCount, 0);

        const response: IMemoryUsageResponse = {
          services: filteredServices,
          summary: {
            totalServices,
            protectedServices,
            vulnerableServices,
            averageMemoryUsage,
            totalMemoryUsage,
            totalBoundaries,
            totalViolations,
          },
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Security memory usage API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
