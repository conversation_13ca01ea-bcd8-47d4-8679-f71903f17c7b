/**
 * Component Status API Endpoint
 * Purpose: Provide status and metrics for all 95+ M0 components
 * Shows component health, alerts, and operational status
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IComponentStatus, IComponentStatusResponse, IComponentAlert } from '../../../types/tracking.types';
import { ENV } from '../../../utils/env';

// Generate realistic component status data
const generateComponentStatuses = (): IComponentStatus[] => {
  const components: IComponentStatus[] = [];

  // Core component definitions
  const componentDefinitions = [
    {
      name: 'SessionTrackingCore',
      category: 'tracking' as const,
      baseHealthScore: 98,
    },
    {
      name: 'SessionTrackingAudit',
      category: 'tracking' as const,
      baseHealthScore: 96,
    },
    {
      name: 'GovernanceRuleEngine',
      category: 'governance' as const,
      baseHealthScore: 94,
    },
    {
      name: 'SecurityEnforcementLayer',
      category: 'security' as const,
      baseHealthScore: 92,
    },
    {
      name: 'IntegrationBridge',
      category: 'integration' as const,
      baseHealthScore: 99,
    },
    {
      name: 'FoundationCore',
      category: 'foundation' as const,
      baseHealthScore: 97,
    },
    {
      name: 'PerformanceMonitor',
      type: 'analytics' as const,
      isEnhanced: true,
      baseHealthScore: 95,
    },
    {
      name: 'ComponentHealthTracker',
      type: 'core' as const,
      isEnhanced: false,
      baseHealthScore: 93,
    },
    {
      name: 'OrchestrationCoordinator',
      type: 'core' as const,
      isEnhanced: true,
      baseHealthScore: 98,
    },
  ];

  // Generate core components
  componentDefinitions.forEach((componentConfig, index) => {
    const now = Date.now();
    const healthScore = componentConfig.baseHealthScore + Math.random() * 2;
    const uptime = now - Math.random() * 86400000 * 30;

    const alerts: IComponentAlert[] = [];
    if (healthScore < 90) {
      alerts.push({
        alertId: `alert-${index}-001`,
        componentId: `comp-${index.toString().padStart(3, '0')}`,
        type: 'performance',
        severity: healthScore < 80 ? 'critical' : 'high',
        message: `Component health below threshold: ${healthScore.toFixed(1)}%`,
        timestamp: new Date(now - Math.random() * 3600000).toISOString(),
        resolved: Math.random() > 0.7,
        acknowledged: Math.random() > 0.5,
        resolvedAt: Math.random() > 0.7 ? new Date(now - Math.random() * 1800000).toISOString() : undefined,
      });
    }

    components.push({
      componentId: `comp-${index.toString().padStart(3, '0')}`,
      componentName: componentConfig.name,
      category: componentConfig.category,
      status: healthScore > 95 ? 'operational' : healthScore > 85 ? 'degraded' : healthScore > 70 ? 'maintenance' : 'offline',
      healthScore: Math.round(healthScore * 10) / 10,
      lastHealthCheck: new Date(now - Math.random() * 300000).toISOString(),
      uptime: Math.round(uptime),
      version: `1.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      dependencies: [],
      dependents: [],
      metrics: {
        requestsPerSecond: Math.round((10 + Math.random() * 90) * 100) / 100,
        averageResponseTime: Math.round((1 + Math.random() * 9) * 100) / 100,
        errorRate: Math.round(Math.random() * 2 * 100) / 100,
        throughput: Math.round((50 + Math.random() * 200) * 100) / 100,
        latency: Math.round((1 + Math.random() * 4) * 100) / 100,
      },
      alerts,
    });
  });

  // Generate additional components to reach target count
  const additionalCategories = ['governance', 'tracking', 'security', 'integration', 'foundation'] as const;
  const statuses = ['operational', 'degraded', 'offline', 'maintenance'] as const;

  for (let i = componentDefinitions.length; i < ENV.TRACKING_COMPONENTS; i++) {
    const now = Date.now();
    const category = additionalCategories[i % additionalCategories.length];
    const healthScore = 85 + Math.random() * 15;
    const uptime = now - Math.random() * 86400000 * 60;
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    const alerts: IComponentAlert[] = [];
    if (healthScore < 90 || status === 'degraded' || status === 'offline') {
      alerts.push({
        alertId: `alert-${i}-001`,
        componentId: `comp-${i.toString().padStart(3, '0')}`,
        type: 'performance',
        severity: status === 'offline' ? 'critical' : 'medium',
        message: `Component ${status}: Health at ${healthScore.toFixed(1)}%`,
        timestamp: new Date(now - Math.random() * 7200000).toISOString(),
        resolved: Math.random() > 0.6,
        acknowledged: Math.random() > 0.4,
        resolvedAt: Math.random() > 0.6 ? new Date(now - Math.random() * 3600000).toISOString() : undefined,
      });
    }

    components.push({
      componentId: `comp-${i.toString().padStart(3, '0')}`,
      componentName: `${category.charAt(0).toUpperCase() + category.slice(1)}Component${i}`,
      category,
      status,
      healthScore: Math.round(healthScore * 10) / 10,
      lastHealthCheck: new Date(now - Math.random() * 600000).toISOString(),
      uptime: Math.round(uptime),
      version: `1.${Math.floor(Math.random() * 5)}.${Math.floor(Math.random() * 10)}`,
      dependencies: [],
      dependents: [],
      metrics: {
        requestsPerSecond: Math.round((5 + Math.random() * 50) * 100) / 100,
        averageResponseTime: Math.round((2 + Math.random() * 15) * 100) / 100,
        errorRate: Math.round(Math.random() * 5 * 100) / 100,
        throughput: Math.round((20 + Math.random() * 100) * 100) / 100,
        latency: Math.round((2 + Math.random() * 8) * 100) / 100,
      },
      alerts,
    });
  }

  return components;
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IComponentStatusResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        const components = generateComponentStatuses();

        // Apply filters if provided
        const status = req.query.status as string;
        const category = req.query.category as string;

        let filteredComponents = components;
        if (status) {
          filteredComponents = filteredComponents.filter(comp => comp.status === status);
        }
        if (category) {
          filteredComponents = filteredComponents.filter(comp => comp.category === category);
        }

        // Calculate summary statistics
        const total = filteredComponents.length;
        const operational = filteredComponents.filter(c => c.status === 'operational').length;
        const degraded = filteredComponents.filter(c => c.status === 'degraded').length;
        const offline = filteredComponents.filter(c => c.status === 'offline').length;
        const maintenance = filteredComponents.filter(c => c.status === 'maintenance').length;
        const overallHealth = filteredComponents.reduce((sum, c) => sum + c.healthScore, 0) / total;

        // Collect all alerts from components
        const allAlerts = filteredComponents.reduce((alerts, component) => {
          return alerts.concat(component.alerts);
        }, [] as IComponentAlert[]);

        const response: IComponentStatusResponse = {
          components: filteredComponents,
          summary: {
            total,
            operational,
            degraded,
            offline,
            maintenance,
            overallHealth: Math.round(overallHealth * 10) / 10,
          },
          alerts: allAlerts,
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Tracking components API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
