/**
 * Tracking Performance API Endpoint
 * Purpose: Provide AnalyticsCacheManager performance metrics and cache data
 * Shows cache hit/miss ratios and performance optimization
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IAnalyticsCacheMetrics, IAnalyticsPerformanceResponse } from '../../../types/tracking.types';

// Generate realistic analytics cache metrics
const generateCacheMetrics = (): IAnalyticsCacheMetrics[] => {
  const caches: IAnalyticsCacheMetrics[] = [];
  const cacheTypes = ['memory', 'redis', 'database', 'file'] as const;
  const statuses = ['healthy', 'degraded', 'offline'] as const;

  // Core analytics caches
  const coreCaches = [
    {
      name: 'AnalyticsCacheManager',
      type: 'memory' as const,
      baseHitRate: 98.5,
      baseResponseTime: 1.2,
    },
    {
      name: 'SessionDataCache',
      type: 'redis' as const,
      baseHitRate: 94.2,
      baseResponseTime: 3.8,
    },
    {
      name: 'ComponentMetricsCache',
      type: 'memory' as const,
      baseHitRate: 96.8,
      baseResponseTime: 0.8,
    },
    {
      name: 'PerformanceDataCache',
      type: 'database' as const,
      baseHitRate: 89.4,
      baseResponseTime: 12.5,
    },
    {
      name: 'TrackingEventsCache',
      type: 'redis' as const,
      baseHitRate: 92.7,
      baseResponseTime: 4.2,
    },
    {
      name: 'HealthCheckCache',
      type: 'memory' as const,
      baseHitRate: 99.1,
      baseResponseTime: 0.5,
    },
  ];

  coreCaches.forEach((cacheConfig, index) => {
    const totalRequests = Math.floor(Math.random() * 100000) + 10000;
    const hitRate = cacheConfig.baseHitRate + (Math.random() - 0.5) * 2; // ±1% variation
    const totalHits = Math.floor(totalRequests * hitRate / 100);
    const totalMisses = totalRequests - totalHits;
    const missRate = 100 - hitRate;
    const memoryUsage = Math.floor(Math.random() * 500) + 100; // 100-600MB
    const maxMemoryUsage = memoryUsage + Math.floor(Math.random() * 200) + 100; // Higher than current
    const keyCount = Math.floor(Math.random() * 10000) + 1000;
    const maxKeys = keyCount + Math.floor(Math.random() * 5000) + 1000;
    const evictionCount = Math.floor(Math.random() * 100);
    const ttl = Math.floor(Math.random() * 3600) + 300; // 5 minutes to 1 hour

    caches.push({
      cacheId: `cache-${index.toString().padStart(3, '0')}`,
      name: cacheConfig.name,
      type: cacheConfig.type,
      status: hitRate > 85 ? 'healthy' : hitRate > 70 ? 'degraded' : 'offline',
      hitRate: Math.round(hitRate * 10) / 10,
      missRate: Math.round(missRate * 10) / 10,
      totalRequests,
      totalHits,
      totalMisses,
      averageResponseTime: Math.round((cacheConfig.baseResponseTime + (Math.random() - 0.5) * 0.5) * 10) / 10,
      memoryUsage,
      maxMemoryUsage,
      evictionCount,
      lastEviction: evictionCount > 0 ? new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString() : undefined,
      ttl,
      keyCount,
      maxKeys,
    });
  });

  // Generate additional cache instances
  for (let i = coreCaches.length; i < 12; i++) {
    const type = cacheTypes[Math.floor(Math.random() * cacheTypes.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const totalRequests = Math.floor(Math.random() * 50000) + 5000;
    const hitRate = 70 + Math.random() * 25; // 70-95%
    const totalHits = Math.floor(totalRequests * hitRate / 100);
    const totalMisses = totalRequests - totalHits;
    const missRate = 100 - hitRate;
    const memoryUsage = Math.floor(Math.random() * 300) + 50;
    const maxMemoryUsage = memoryUsage + Math.floor(Math.random() * 150) + 50;
    const keyCount = Math.floor(Math.random() * 5000) + 500;
    const maxKeys = keyCount + Math.floor(Math.random() * 2500) + 500;
    const evictionCount = Math.floor(Math.random() * 50);
    const ttl = Math.floor(Math.random() * 7200) + 300;

    caches.push({
      cacheId: `cache-${i.toString().padStart(3, '0')}`,
      name: `Cache${i}`,
      type,
      status,
      hitRate: Math.round(hitRate * 10) / 10,
      missRate: Math.round(missRate * 10) / 10,
      totalRequests,
      totalHits,
      totalMisses,
      averageResponseTime: Math.round((2 + Math.random() * 20) * 10) / 10, // 2-22ms
      memoryUsage,
      maxMemoryUsage,
      evictionCount,
      lastEviction: evictionCount > 0 ? new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString() : undefined,
      ttl,
      keyCount,
      maxKeys,
    });
  }

  return caches;
};

const cacheMetrics = generateCacheMetrics();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<IAnalyticsPerformanceResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle filtering
        const type = req.query.type as string;
        const status = req.query.status as string;

        let filteredCaches = cacheMetrics;

        // Apply filters
        if (type) {
          filteredCaches = filteredCaches.filter(cache => cache.type === type);
        }
        if (status) {
          filteredCaches = filteredCaches.filter(cache => cache.status === status);
        }

        // Calculate overall performance metrics
        const totalRequests = filteredCaches.reduce((sum, cache) => sum + cache.totalRequests, 0);
        const totalHits = filteredCaches.reduce((sum, cache) => sum + cache.totalHits, 0);
        const averageHitRate = totalRequests > 0 ? Math.round((totalHits / totalRequests * 100) * 10) / 10 : 0;
        
        const averageResponseTime = filteredCaches.length > 0 
          ? Math.round((filteredCaches.reduce((sum, cache) => sum + cache.averageResponseTime, 0) / filteredCaches.length) * 10) / 10
          : 0;
        
        const totalMemoryUsage = filteredCaches.reduce((sum, cache) => sum + cache.memoryUsage, 0);
        
        // Calculate health score based on hit rates and response times
        const healthScore = filteredCaches.length > 0
          ? Math.round((filteredCaches.reduce((sum, cache) => {
              const hitRateScore = Math.min(cache.hitRate, 100);
              const responseTimeScore = Math.max(0, 100 - cache.averageResponseTime * 2); // Lower response time = higher score
              return sum + (hitRateScore + responseTimeScore) / 2;
            }, 0) / filteredCaches.length) * 10) / 10
          : 0;

        const response: IAnalyticsPerformanceResponse = {
          caches: filteredCaches,
          overallPerformance: {
            averageHitRate,
            averageResponseTime,
            totalRequests,
            totalMemoryUsage,
            healthScore,
          },
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Tracking performance API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
