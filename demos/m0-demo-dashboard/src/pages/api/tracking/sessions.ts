/**
 * Tracking Sessions API Endpoint
 * Purpose: Provide session tracking data for core, audit, realtime, and utils
 * Shows real-time session activity and monitoring
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { ISessionData, ISessionEvent, ISessionTrackingResponse } from '../../../types/tracking.types';

// Generate realistic session data
const generateSessionData = (): ISessionData[] => {
  const sessions: ISessionData[] = [];
  const trackingTypes = ['core', 'audit', 'realtime', 'utils'] as const;
  const statuses = ['active', 'completed', 'terminated', 'error'] as const;
  const eventTypes = ['page-view', 'action', 'error', 'performance', 'custom'] as const;
  const deviceTypes = ['desktop', 'mobile', 'tablet', 'server'] as const;

  // Generate sessions for the last 24 hours
  const now = Date.now();
  const oneDayAgo = now - 24 * 60 * 60 * 1000;

  for (let i = 0; i < 150; i++) {
    const startTime = new Date(oneDayAgo + Math.random() * (now - oneDayAgo));
    const duration = Math.floor(Math.random() * 3600000) + 60000; // 1 minute to 1 hour
    const endTime = Math.random() > 0.2 ? new Date(startTime.getTime() + duration) : undefined; // 80% completed
    const trackingType = trackingTypes[Math.floor(Math.random() * trackingTypes.length)];
    const status = endTime ? 'completed' : statuses[Math.floor(Math.random() * statuses.length)];
    const activityCount = Math.floor(Math.random() * 50) + 5;
    const deviceType = deviceTypes[Math.floor(Math.random() * deviceTypes.length)];

    // Generate events for this session
    const events: ISessionEvent[] = [];
    for (let j = 0; j < activityCount; j++) {
      const eventTime = new Date(startTime.getTime() + (j * duration / activityCount));
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      
      events.push({
        eventId: `event-${i}-${j}`,
        sessionId: `session-${i.toString().padStart(4, '0')}`,
        timestamp: eventTime.toISOString(),
        eventType,
        eventName: `${eventType}-${j}`,
        eventData: {
          component: `Component${Math.floor(Math.random() * 10)}`,
          action: `action-${Math.floor(Math.random() * 20)}`,
          value: Math.floor(Math.random() * 1000),
        },
        duration: Math.floor(Math.random() * 5000), // 0-5 seconds
        success: Math.random() > 0.1, // 90% success rate
      });
    }

    sessions.push({
      sessionId: `session-${i.toString().padStart(4, '0')}`,
      userId: Math.random() > 0.3 ? `user-${Math.floor(Math.random() * 100)}` : undefined,
      startTime: startTime.toISOString(),
      endTime: endTime?.toISOString(),
      duration: endTime ? duration : Date.now() - startTime.getTime(),
      activityCount,
      trackingType,
      status,
      metadata: {
        userAgent: `Mozilla/5.0 (${deviceType === 'mobile' ? 'Mobile' : 'Desktop'})`,
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        location: ['US', 'CA', 'UK', 'DE', 'FR'][Math.floor(Math.random() * 5)],
        deviceType,
      },
      events,
    });
  }

  return sessions.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
};

const sessionData = generateSessionData();

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<ISessionTrackingResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle filtering
        const trackingType = req.query.trackingType as string;
        const status = req.query.status as string;
        const userId = req.query.userId as string;
        const limit = parseInt(req.query.limit as string) || 50;

        let filteredSessions = sessionData;

        // Apply filters
        if (trackingType) {
          filteredSessions = filteredSessions.filter(session => session.trackingType === trackingType);
        }
        if (status) {
          filteredSessions = filteredSessions.filter(session => session.status === status);
        }
        if (userId) {
          filteredSessions = filteredSessions.filter(session => session.userId === userId);
        }

        // Limit results
        filteredSessions = filteredSessions.slice(0, limit);

        // Calculate summary statistics
        const activeSessions = sessionData.filter(s => s.status === 'active').length;
        const totalSessions = sessionData.length;
        const completedSessions = sessionData.filter(s => s.status === 'completed');
        const averageSessionDuration = completedSessions.length > 0 
          ? Math.round(completedSessions.reduce((sum, s) => sum + s.duration, 0) / completedSessions.length / 1000) // Convert to seconds
          : 0;
        const totalEvents = sessionData.reduce((sum, s) => sum + s.events.length, 0);

        const response: ISessionTrackingResponse = {
          sessions: filteredSessions,
          activeSessions,
          totalSessions,
          averageSessionDuration,
          totalEvents,
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Tracking sessions API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
