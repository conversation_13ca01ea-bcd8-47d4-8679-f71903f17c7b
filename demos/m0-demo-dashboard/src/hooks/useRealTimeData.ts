/**
 * Real-Time Data Hook for M0 Demo Dashboard
 * Purpose: SWR-based real-time updates with configurable intervals
 * Intervals: Memory (3s), Governance (5s), Tracking (5s), Integration (10s)
 */

'use client';

import useSWR from 'swr';
import { ENV } from '../utils/env';
import type { UseRealTimeDataReturn } from '../types';

// Fetcher function with error handling
const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  return response.json();
};

// Hook for real-time data with configurable intervals
export function useRealTimeData<T>(
  endpoint: string,
  options: {
    refreshInterval?: number;
    enabled?: boolean;
    retryCount?: number;
    retryDelay?: number;
  } = {}
): UseRealTimeDataReturn<T> {
  const {
    refreshInterval = ENV.REFRESH_INTERVAL,
    enabled = true,
    retryCount = 3,
    retryDelay = 1000,
  } = options;

  const { data, error, isLoading, isValidating, mutate } = useSWR<T>(
    enabled ? endpoint : null,
    fetcher,
    {
      refreshInterval,
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      dedupingInterval: refreshInterval / 2, // Dedupe for half the refresh interval
      errorRetryCount: retryCount,
      errorRetryInterval: retryDelay,
      onError: (error) => {
        console.warn(`Real-time data error for ${endpoint}:`, error);
      },
    }
  );

  return {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
  };
}

// Specialized hooks for different dashboard sections
export function useGovernanceData<T>(endpoint: string, enabled = true) {
  return useRealTimeData<T>(endpoint, {
    refreshInterval: ENV.GOVERNANCE_UPDATE_INTERVAL,
    enabled,
  });
}

export function useTrackingData<T>(endpoint: string, enabled = true) {
  return useRealTimeData<T>(endpoint, {
    refreshInterval: ENV.TRACKING_UPDATE_INTERVAL,
    enabled,
  });
}

export function useSecurityData<T>(endpoint: string, enabled = true) {
  return useRealTimeData<T>(endpoint, {
    refreshInterval: ENV.MEMORY_UPDATE_INTERVAL,
    enabled,
  });
}

export function useIntegrationData<T>(endpoint: string, enabled = true) {
  return useRealTimeData<T>(endpoint, {
    refreshInterval: ENV.INTEGRATION_UPDATE_INTERVAL,
    enabled,
  });
}

// Hook for multiple endpoints with different intervals
export function useMultipleRealTimeData(endpoints: Array<{
  key: string;
  endpoint: string;
  interval?: number;
  enabled?: boolean;
}>) {
  const results: Record<string, UseRealTimeDataReturn<unknown>> = {};

  endpoints.forEach(({ key, endpoint, interval, enabled = true }) => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    results[key] = useRealTimeData(endpoint, {
      refreshInterval: interval,
      enabled,
    });
  });

  return results;
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const startTime = Date.now();
  
  return {
    measureResponseTime: (endpoint: string) => {
      const responseTime = Date.now() - startTime;
      if (ENV.DEMO_MODE && responseTime > 5000) {
        console.warn(`Slow response for ${endpoint}: ${responseTime}ms`);
      }
      return responseTime;
    },
  };
}

export default useRealTimeData;
