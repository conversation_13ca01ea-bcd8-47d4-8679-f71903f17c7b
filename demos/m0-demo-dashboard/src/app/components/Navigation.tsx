'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container
} from '@mui/material';

const navigationItems = [
  { href: '/', label: 'Overview', icon: '🏠' },
  { href: '/security', label: 'Security', icon: '🛡️' },
  { href: '/governance', label: 'Governance', icon: '📊' },
  { href: '/tracking', label: 'Tracking', icon: '📈' },
  { href: '/integration', label: 'Integration', icon: '🔗' },
  { href: '/foundation', label: 'Foundation', icon: '🎯' },
];

export default function Navigation() {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a simple placeholder during SSR to prevent hydration mismatch
    return (
      <nav style={{ height: '64px', backgroundColor: '#fff', borderBottom: '1px solid #e0e0e0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 16px', height: '64px', display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <h1 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#1f2937', margin: 0 }}>
            M0 Control Center
          </h1>
          <div style={{ display: 'flex', gap: '16px' }}>
            {navigationItems.map((item) => (
              <span key={item.href} style={{ padding: '8px 12px', color: '#6b7280' }}>
                {item.icon} {item.label}
              </span>
            ))}
          </div>
          <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
            <div style={{ fontWeight: '600' }}>M0 Status</div>
            <div style={{ fontSize: '0.75rem', color: '#059669' }}>129% Complete</div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <AppBar
      position="static"
      elevation={1}
      sx={{
        backgroundColor: 'white',
        color: 'text.primary',
        borderBottom: 1,
        borderColor: 'divider'
      }}
    >
      <Container maxWidth="xl">
        <Toolbar sx={{ justifyContent: 'space-between', minHeight: '64px !important' }}>
          <Typography
            variant="h6"
            component="h1"
            sx={{
              fontWeight: 'bold',
              color: 'text.primary'
            }}
          >
            M0 Control Center
          </Typography>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Button
                  key={item.href}
                  component={Link}
                  href={item.href}
                  variant={isActive ? 'contained' : 'text'}
                  color={isActive ? 'primary' : 'inherit'}
                  startIcon={<span>{item.icon}</span>}
                  sx={{
                    minWidth: 'auto',
                    px: 2,
                    py: 1,
                    textTransform: 'none',
                    fontWeight: isActive ? 600 : 400,
                    color: isActive ? 'primary.contrastText' : 'text.primary',
                    '&:hover': {
                      backgroundColor: isActive ? 'primary.dark' : 'action.hover'
                    }
                  }}
                >
                  {item.label}
                </Button>
              );
            })}
          </Box>

          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
              M0 Status
            </Typography>
            <Typography variant="caption" sx={{ color: 'success.main' }}>
              129% Complete
            </Typography>
          </Box>
        </Toolbar>
      </Container>
    </AppBar>
  );
}
