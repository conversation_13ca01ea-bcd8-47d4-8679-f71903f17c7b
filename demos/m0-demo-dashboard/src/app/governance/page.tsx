/**
 * Governance Control Panel Page
 * Purpose: Showcase 61+ Governance components including rule validation and compliance
 * Features: Live rule validation, authority chain, compliance scoring
 */

import { Container, Box } from '@mui/material';
import GovernancePanel from '../../components/dashboards/GovernancePanel';

export default function GovernancePage() {
  return (
    <Container maxWidth="xl">
      <Box sx={{ py: 3 }}>
        <GovernancePanel />
      </Box>
    </Container>
  );
}
