/**
 * ============================================================================
 * AI CONTEXT: AccessibilityEnhancer - M0 Demo Accessibility Component
 * Purpose: Accessibility enhancements for responsive design and WCAG compliance
 * Complexity: Moderate - Accessibility utility component
 * AI Navigation: 4 sections, accessibility domain
 * Lines: ~250 / Target limit: 300
 * ============================================================================
 */

import React, { useEffect, useState } from 'react';
import { 
  Box, 
  Button, 
  Tooltip, 
  IconButton,
  Snackbar,
  Alert,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Accessibility as A11yIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Contrast as ContrastIcon,
  TextFields as TextIcon
} from '@mui/icons-material';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Accessibility enhancement interfaces and types
// ============================================================================

export interface AccessibilitySettings {
  fontSize: number;
  highContrast: boolean;
  reducedMotion: boolean;
  focusVisible: boolean;
}

export interface AccessibilityEnhancerProps {
  /** Whether to show accessibility controls */
  showControls?: boolean;
  /** Position of accessibility controls */
  controlsPosition?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  /** Initial accessibility settings */
  initialSettings?: Partial<AccessibilitySettings>;
  /** Callback when settings change */
  onSettingsChange?: (settings: AccessibilitySettings) => void;
}

// ============================================================================
// SECTION 2: ACCESSIBILITY ENHANCER COMPONENT
// AI Context: Main accessibility enhancement component
// ============================================================================

export const AccessibilityEnhancer: React.FC<AccessibilityEnhancerProps> = ({
  showControls = true,
  controlsPosition = 'top-right',
  initialSettings = {},
  onSettingsChange
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [settings, setSettings] = useState<AccessibilitySettings>({
    fontSize: 1,
    highContrast: false,
    reducedMotion: false,
    focusVisible: true,
    ...initialSettings
  });
  
  const [showNotification, setShowNotification] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState('');

  // Apply accessibility settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // Font size scaling
    root.style.setProperty('--font-scale', settings.fontSize.toString());
    
    // High contrast mode
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // Reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
    
    // Focus visible
    if (settings.focusVisible) {
      root.classList.add('focus-visible');
    } else {
      root.classList.remove('focus-visible');
    }
    
    // Notify parent component
    if (onSettingsChange) {
      onSettingsChange(settings);
    }
  }, [settings, onSettingsChange]);

  // Keyboard navigation support
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + A: Toggle accessibility controls
      if (event.altKey && event.key === 'a') {
        event.preventDefault();
        setShowNotification(true);
        setNotificationMessage('Accessibility controls activated');
      }
      
      // Alt + +: Increase font size
      if (event.altKey && event.key === '=') {
        event.preventDefault();
        handleFontSizeChange(0.1);
      }
      
      // Alt + -: Decrease font size
      if (event.altKey && event.key === '-') {
        event.preventDefault();
        handleFontSizeChange(-0.1);
      }
      
      // Alt + C: Toggle high contrast
      if (event.altKey && event.key === 'c') {
        event.preventDefault();
        handleContrastToggle();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [settings]);

  const handleFontSizeChange = (delta: number) => {
    const newFontSize = Math.max(0.8, Math.min(1.5, settings.fontSize + delta));
    setSettings(prev => ({ ...prev, fontSize: newFontSize }));
    setNotificationMessage(`Font size: ${Math.round(newFontSize * 100)}%`);
    setShowNotification(true);
  };

  const handleContrastToggle = () => {
    setSettings(prev => ({ ...prev, highContrast: !prev.highContrast }));
    setNotificationMessage(
      settings.highContrast ? 'High contrast disabled' : 'High contrast enabled'
    );
    setShowNotification(true);
  };

  const handleMotionToggle = () => {
    setSettings(prev => ({ ...prev, reducedMotion: !prev.reducedMotion }));
    setNotificationMessage(
      settings.reducedMotion ? 'Animations enabled' : 'Animations reduced'
    );
    setShowNotification(true);
  };

  const resetSettings = () => {
    setSettings({
      fontSize: 1,
      highContrast: false,
      reducedMotion: false,
      focusVisible: true
    });
    setNotificationMessage('Accessibility settings reset');
    setShowNotification(true);
  };

  const getControlsPosition = () => {
    const positions = {
      'top-right': { top: 16, right: 16 },
      'top-left': { top: 16, left: 16 },
      'bottom-right': { bottom: 16, right: 16 },
      'bottom-left': { bottom: 16, left: 16 }
    };
    return positions[controlsPosition];
  };

  if (!showControls) {
    return null;
  }

  return (
    <>
      {/* Accessibility Controls */}
      <Box
        sx={{
          position: 'fixed',
          ...getControlsPosition(),
          zIndex: 1300,
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          gap: 1,
          p: 1,
          backgroundColor: 'background.paper',
          borderRadius: 2,
          boxShadow: 3,
          border: '1px solid',
          borderColor: 'divider'
        }}
        role="toolbar"
        aria-label="Accessibility Controls"
      >
        <Tooltip title="Decrease font size (Alt + -)">
          <IconButton
            size="small"
            onClick={() => handleFontSizeChange(-0.1)}
            disabled={settings.fontSize <= 0.8}
            aria-label="Decrease font size"
          >
            <ZoomOutIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Increase font size (Alt + =)">
          <IconButton
            size="small"
            onClick={() => handleFontSizeChange(0.1)}
            disabled={settings.fontSize >= 1.5}
            aria-label="Increase font size"
          >
            <ZoomInIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Toggle high contrast (Alt + C)">
          <IconButton
            size="small"
            onClick={handleContrastToggle}
            color={settings.highContrast ? 'primary' : 'default'}
            aria-label="Toggle high contrast mode"
            aria-pressed={settings.highContrast}
          >
            <ContrastIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Toggle reduced motion">
          <IconButton
            size="small"
            onClick={handleMotionToggle}
            color={settings.reducedMotion ? 'primary' : 'default'}
            aria-label="Toggle reduced motion"
            aria-pressed={settings.reducedMotion}
          >
            <A11yIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        {!isMobile && (
          <Button
            size="small"
            variant="outlined"
            onClick={resetSettings}
            sx={{ fontSize: '0.75rem', minWidth: 'auto', px: 1 }}
          >
            Reset
          </Button>
        )}
      </Box>

      {/* Notification Snackbar */}
      <Snackbar
        open={showNotification}
        autoHideDuration={3000}
        onClose={() => setShowNotification(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={() => setShowNotification(false)} 
          severity="info"
          variant="filled"
        >
          {notificationMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

// ============================================================================
// SECTION 3: ACCESSIBILITY CSS INJECTION
// AI Context: CSS styles for accessibility enhancements
// ============================================================================

export const injectAccessibilityStyles = () => {
  const styles = `
    /* Font scaling */
    html {
      font-size: calc(16px * var(--font-scale, 1));
    }
    
    /* High contrast mode */
    .high-contrast {
      filter: contrast(150%) brightness(110%);
    }
    
    .high-contrast * {
      border-color: currentColor !important;
    }
    
    /* Reduced motion */
    .reduced-motion *,
    .reduced-motion *::before,
    .reduced-motion *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    /* Enhanced focus visibility */
    .focus-visible *:focus-visible {
      outline: 3px solid #005fcc !important;
      outline-offset: 2px !important;
    }
    
    /* Skip link */
    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 9999;
    }
    
    .skip-link:focus {
      top: 6px;
    }
  `;

  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
};

// ============================================================================
// SECTION 4: ACCESSIBILITY HOOKS AND UTILITIES
// AI Context: Utility hooks for accessibility features
// ============================================================================

export const useAccessibility = () => {
  const [settings, setSettings] = useState<AccessibilitySettings>({
    fontSize: 1,
    highContrast: false,
    reducedMotion: false,
    focusVisible: true
  });

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return {
    settings,
    updateSetting,
    increaseFontSize: () => updateSetting('fontSize', Math.min(1.5, settings.fontSize + 0.1)),
    decreaseFontSize: () => updateSetting('fontSize', Math.max(0.8, settings.fontSize - 0.1)),
    toggleHighContrast: () => updateSetting('highContrast', !settings.highContrast),
    toggleReducedMotion: () => updateSetting('reducedMotion', !settings.reducedMotion)
  };
};

export default AccessibilityEnhancer;
