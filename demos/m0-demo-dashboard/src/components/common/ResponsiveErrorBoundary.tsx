/**
 * ============================================================================
 * AI CONTEXT: ResponsiveErrorBoundary - M0 Demo Error Handling Component
 * Purpose: Responsive error boundary with graceful fallback UI and recovery
 * Complexity: Moderate - Error handling with responsive design
 * AI Navigation: 4 sections, error handling domain
 * Lines: ~250 / Target limit: 300
 * ============================================================================
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Alert, 
  Card, 
  CardContent,
  Collapse,
  IconButton
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  BugReport as BugIcon
} from '@mui/icons-material';
import { ResponsiveContainer } from './ResponsiveContainer';

// ============================================================================
// SECTION 1: TYPE DEFINITIONS
// AI Context: Error boundary interfaces and types
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  retryCount: number;
}

interface ResponsiveErrorBoundaryProps {
  children: ReactNode;
  /** Custom fallback component */
  fallback?: (error: Error, retry: () => void) => ReactNode;
  /** Maximum retry attempts */
  maxRetries?: number;
  /** Whether to show error details */
  showErrorDetails?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Whether to log errors to console */
  logErrors?: boolean;
  /** Callback when error occurs */
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

// ============================================================================
// SECTION 2: ERROR BOUNDARY CLASS COMPONENT
// AI Context: Main error boundary with responsive fallback UI
// ============================================================================

export class ResponsiveErrorBoundary extends Component<
  ResponsiveErrorBoundaryProps,
  ErrorBoundaryState
> {
  constructor(props: ResponsiveErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { logErrors = true, onError } = this.props;
    
    this.setState({
      error,
      errorInfo
    });

    if (logErrors) {
      console.error('Error Boundary caught an error:', error, errorInfo);
    }

    if (onError) {
      onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount } = this.state;

    if (retryCount < maxRetries) {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        showDetails: false,
        retryCount: retryCount + 1
      });
    }
  };

  handleToggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  render() {
    const { 
      children, 
      fallback, 
      maxRetries = 3, 
      showErrorDetails = true,
      errorMessage = 'Something went wrong'
    } = this.props;
    
    const { hasError, error, errorInfo, showDetails, retryCount } = this.state;

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, this.handleRetry);
      }

      // Default responsive error UI
      return (
        <ResponsiveContainer spacing="normal">
          <Card 
            sx={{ 
              border: '1px solid',
              borderColor: 'error.main',
              backgroundColor: 'error.light',
              color: 'error.contrastText'
            }}
          >
            <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
              <Box 
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  alignItems: { xs: 'center', sm: 'flex-start' },
                  gap: { xs: 2, sm: 3 },
                  textAlign: { xs: 'center', sm: 'left' }
                }}
              >
                <ErrorIcon 
                  sx={{ 
                    fontSize: { xs: 48, sm: 56, md: 64 },
                    color: 'error.main',
                    flexShrink: 0
                  }} 
                />
                
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography 
                    variant="h5" 
                    gutterBottom
                    sx={{ 
                      fontSize: { xs: '1.25rem', sm: '1.5rem', md: '1.75rem' },
                      fontWeight: 'bold',
                      color: 'error.main'
                    }}
                  >
                    {errorMessage}
                  </Typography>
                  
                  <Typography 
                    variant="body1" 
                    sx={{ 
                      mb: 2,
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      color: 'text.secondary'
                    }}
                  >
                    We apologize for the inconvenience. The application encountered an unexpected error.
                  </Typography>

                  <Box 
                    sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      gap: { xs: 1, sm: 2 },
                      alignItems: { xs: 'stretch', sm: 'center' }
                    }}
                  >
                    {retryCount < maxRetries && (
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<RefreshIcon />}
                        onClick={this.handleRetry}
                        sx={{ 
                          fontSize: { xs: '0.75rem', sm: '0.875rem' },
                          py: { xs: 1, sm: 1.5 }
                        }}
                      >
                        Try Again ({maxRetries - retryCount} attempts left)
                      </Button>
                    )}
                    
                    <Button
                      variant="outlined"
                      onClick={() => window.location.reload()}
                      sx={{ 
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        py: { xs: 1, sm: 1.5 }
                      }}
                    >
                      Reload Page
                    </Button>
                  </Box>
                </Box>
              </Box>

              {showErrorDetails && error && (
                <Box sx={{ mt: 3 }}>
                  <Button
                    variant="text"
                    startIcon={showDetails ? <CollapseIcon /> : <ExpandIcon />}
                    onClick={this.handleToggleDetails}
                    sx={{ 
                      fontSize: { xs: '0.75rem', sm: '0.875rem' },
                      color: 'text.secondary'
                    }}
                  >
                    {showDetails ? 'Hide' : 'Show'} Error Details
                  </Button>
                  
                  <Collapse in={showDetails}>
                    <Alert 
                      severity="error" 
                      icon={<BugIcon />}
                      sx={{ 
                        mt: 2,
                        '& .MuiAlert-message': {
                          fontSize: { xs: '0.75rem', sm: '0.875rem' }
                        }
                      }}
                    >
                      <Typography variant="subtitle2" gutterBottom>
                        Error: {error.name}
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                        {error.message}
                      </Typography>
                      {errorInfo && (
                        <details>
                          <summary style={{ cursor: 'pointer', marginBottom: '8px' }}>
                            Component Stack
                          </summary>
                          <pre style={{ 
                            fontSize: '0.75rem', 
                            overflow: 'auto',
                            maxHeight: '200px',
                            backgroundColor: 'rgba(0,0,0,0.05)',
                            padding: '8px',
                            borderRadius: '4px'
                          }}>
                            {errorInfo.componentStack}
                          </pre>
                        </details>
                      )}
                    </Alert>
                  </Collapse>
                </Box>
              )}
            </CardContent>
          </Card>
        </ResponsiveContainer>
      );
    }

    return children;
  }
}

// ============================================================================
// SECTION 3: FUNCTIONAL ERROR BOUNDARY WRAPPER
// AI Context: Functional component wrapper for easier usage
// ============================================================================

export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ResponsiveErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ResponsiveErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ResponsiveErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default ResponsiveErrorBoundary;
