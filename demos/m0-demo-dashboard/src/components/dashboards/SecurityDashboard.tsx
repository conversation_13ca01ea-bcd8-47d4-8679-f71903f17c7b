/**
 * Security & Memory Safety Dashboard
 * Purpose: Complete security monitoring dashboard for M0 demo
 * Features: Real-time memory monitoring, attack simulation, component health
 */

'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  Slider,
  FormControlLabel,
  Switch,
  Chip
} from '@mui/material';
import EducationalTooltip from '../common/EducationalTooltip';
import { ResponsiveContainer, ResponsiveGrid } from '../common/ResponsiveContainer';
import { ResponsiveButtonGroup } from '../common/ResponsiveButtonGroup';
import { MetricCard } from '../common/ResponsiveCard';
import { securityEducationalContent, foundationEducationalContent } from '../../data/educationalContent';
import {
  Security as SecurityIcon,
  Memory as MemoryIcon,
  Shield as ShieldIcon,
  PlayArrow as PlayIcon,
  Refresh as RefreshIcon,
  Tune as TuneIcon,
  Visibility as ShowIcon,
  VisibilityOff as HideIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import MemoryUsageChart from '../widgets/MemoryUsageChart';
import ComponentHealthGrid from '../widgets/ComponentHealthGrid';
import type {
  IMemoryUsageResponse,
  IProtectionStatusResponse
} from '../../types/security.types';

// Import the real-time data hook
import { useSecurityData } from '../../hooks/useRealTimeData';

interface SecurityDashboardProps {
  className?: string;
}

export default function SecurityDashboard({ className }: SecurityDashboardProps) {
  const [selectedService, setSelectedService] = useState<string>('');
  const [activeTab, setActiveTab] = useState(0);
  const [simulationRunning, setSimulationRunning] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState<'success' | 'warning' | 'error'>('success');

  // Milestone 4.2: Interactive Controls State
  const [memoryThreshold, setMemoryThreshold] = useState(75);
  const [maxMemoryLimit, setMaxMemoryLimit] = useState(512);
  const [enhancedFeaturesVisible, setEnhancedFeaturesVisible] = useState(false);
  const [boundaryAdjustmentActive, setBoundaryAdjustmentActive] = useState(false);

  // Fetch security data
  const { data: memoryData, isLoading: memoryLoading, error: memoryError } =
    useSecurityData<IMemoryUsageResponse>('/api/security/memory-usage');

  const { data: protectionData, isLoading: protectionLoading } =
    useSecurityData<IProtectionStatusResponse>('/api/security/protection-status');

  // Handle attack simulation
  const handleAttackSimulation = async (type: string) => {
    setSimulationRunning(true);
    try {
      const response = await fetch('/api/security/attack-simulation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type,
          intensity: 'medium',
          duration: 30,
          targetServices: selectedService ? [selectedService] : []
        })
      });

      if (response.ok) {
        setAlertMessage(`${type} simulation started successfully`);
        setAlertSeverity('success');
      } else {
        throw new Error('Simulation failed');
      }
    } catch (error) {
      setAlertMessage('Failed to start simulation');
      setAlertSeverity('error');
    } finally {
      setSimulationRunning(false);
      setAlertOpen(true);
    }
  };

  // Milestone 4.2: Memory Boundary Adjustment Handler
  const handleMemoryBoundaryAdjustment = async (newThreshold: number, newLimit: number) => {
    setBoundaryAdjustmentActive(true);
    try {
      const response = await fetch('/api/security/boundary-enforcement', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: 'adjust-boundaries',
          memoryThreshold: newThreshold,
          maxMemoryLimit: newLimit,
          applyToAllServices: true
        })
      });

      if (response.ok) {
        setAlertMessage(`Memory boundaries adjusted: ${newThreshold}% threshold, ${newLimit}MB limit`);
        setAlertSeverity('success');
      } else {
        throw new Error('Boundary adjustment failed');
      }
    } catch (error) {
      setAlertMessage('Failed to adjust memory boundaries');
      setAlertSeverity('error');
    } finally {
      setBoundaryAdjustmentActive(false);
      setAlertOpen(true);
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  if (memoryLoading && protectionLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (memoryError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load security data: {memoryError?.message || 'Unknown error'}
      </Alert>
    );
  }

  return (
    <Box
      className={className}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        maxWidth: '100%',
        overflow: 'hidden'
      }}
    >
      {/* Header - Enhanced Responsive */}
      <Box mb={{ xs: 2, sm: 2.5, md: 3 }}>
        <Typography
          variant="h4"
          gutterBottom
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: { xs: 0.5, sm: 1 },
            fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
            fontWeight: 'bold',
            flexWrap: { xs: 'wrap', sm: 'nowrap' }
          }}
        >
          <SecurityIcon
            color="primary"
            sx={{
              fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' },
              mb: { xs: 0.5, sm: 0 }
            }}
          />
          <Box component="span" sx={{
            display: { xs: 'block', sm: 'inline' },
            width: { xs: '100%', sm: 'auto' }
          }}>
            Security & Memory Safety Dashboard
          </Box>
        </Typography>
        <Typography
          variant="body1"
          color="textSecondary"
          sx={{
            fontSize: { xs: '0.875rem', sm: '1rem' },
            mt: { xs: 1, sm: 0 }
          }}
        >
          Real-time monitoring of {memoryData?.services.length || 0} protected services with attack prevention
        </Typography>
      </Box>

      {/* Summary Cards - Enhanced Responsive Grid with MetricCard */}
      <ResponsiveGrid
        columns={{ xs: 1, sm: 2, md: 2, lg: 4 }}
        gap={{ xs: 2, sm: 2.5, md: 3 }}
        sx={{ mb: { xs: 2, sm: 2.5, md: 3 } }}
      >
        <MetricCard
          value={memoryData?.summary.protectedServices || 0}
          label="Protected Services"
          valueColor="success.main"
          metricIcon={<ShieldIcon color="success" />}
          interactive
        />

        <MetricCard
          value={`${memoryData?.summary.totalMemoryUsage || 0} MB`}
          label="Total Memory"
          valueColor="primary.main"
          metricIcon={<MemoryIcon color="primary" />}
          interactive
        />

        <MetricCard
          value={memoryData?.summary.totalBoundaries || 0}
          label="Boundaries"
          valueColor="info.main"
          metricIcon={<SecurityIcon color="info" />}
          interactive
        />

        <MetricCard
          value={memoryData?.summary.totalViolations || 0}
          label="Violations"
          valueColor="warning.main"
          metricIcon={<SecurityIcon color="warning" />}
          interactive
        />
      </ResponsiveGrid>

      {/* Attack Simulation Controls - Enhanced Responsive */}
      <Card sx={{
        mb: { xs: 2, sm: 2.5, md: 3 },
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          boxShadow: 2
        }
      }}>
        <CardContent sx={{ p: { xs: 2, sm: 2.5, md: 3 } }}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: 'flex',
              alignItems: 'center',
              fontSize: { xs: '1rem', sm: '1.125rem', md: '1.25rem' },
              mb: { xs: 1.5, sm: 2 }
            }}
          >
            Security Simulation Controls
            <EducationalTooltip
              content={securityEducationalContent.attackSimulation}
              placement="right"
              size="small"
            />
          </Typography>
          <ResponsiveButtonGroup
            buttons={[
              {
                label: 'Memory Attack',
                variant: 'contained',
                color: 'error',
                loading: simulationRunning,
                icon: <PlayIcon />,
                onClick: () => handleAttackSimulation('memory-exhaustion')
              },
              {
                label: 'Buffer Overflow',
                variant: 'contained',
                color: 'warning',
                loading: simulationRunning,
                icon: <PlayIcon />,
                onClick: () => handleAttackSimulation('buffer-overflow')
              },
              {
                label: 'Stress Test',
                variant: 'contained',
                color: 'info',
                loading: simulationRunning,
                icon: <PlayIcon />,
                onClick: () => handleAttackSimulation('stress-test')
              },
              {
                label: 'Refresh Data',
                variant: 'outlined',
                icon: <RefreshIcon />,
                onClick: () => window.location.reload()
              }
            ]}
            maxButtonsPerRow={{ xs: 1, sm: 2, md: 4 }}
            spacing={{ xs: 1.5, sm: 2 }}
          />
        </CardContent>
      </Card>

      {/* Milestone 4.2: Memory Boundary Adjustment Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <TuneIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Memory Boundary Adjustment
            <EducationalTooltip
              content={securityEducationalContent.memoryProtection}
              placement="right"
              size="small"
            />
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
            <Box sx={{ flex: 1 }}>
              <Typography gutterBottom>Memory Threshold (%)</Typography>
              <Slider
                value={memoryThreshold}
                onChange={(_, newValue) => setMemoryThreshold(newValue as number)}
                onChangeCommitted={(_, newValue) => handleMemoryBoundaryAdjustment(newValue as number, maxMemoryLimit)}
                min={50}
                max={95}
                step={5}
                marks={[
                  { value: 50, label: '50%' },
                  { value: 75, label: '75%' },
                  { value: 90, label: '90%' }
                ]}
                valueLabelDisplay="on"
                disabled={boundaryAdjustmentActive}
              />
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography gutterBottom>Max Memory Limit (MB)</Typography>
              <Slider
                value={maxMemoryLimit}
                onChange={(_, newValue) => setMaxMemoryLimit(newValue as number)}
                onChangeCommitted={(_, newValue) => handleMemoryBoundaryAdjustment(memoryThreshold, newValue as number)}
                min={256}
                max={2048}
                step={64}
                marks={[
                  { value: 256, label: '256MB' },
                  { value: 512, label: '512MB' },
                  { value: 1024, label: '1GB' },
                  { value: 2048, label: '2GB' }
                ]}
                valueLabelDisplay="on"
                disabled={boundaryAdjustmentActive}
              />
            </Box>
          </Box>
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              label={`Current: ${memoryThreshold}% / ${maxMemoryLimit}MB`}
              color="primary"
              variant="outlined"
            />
            {boundaryAdjustmentActive && (
              <Box display="flex" alignItems="center" gap={1}>
                <CircularProgress size={16} />
                <Typography variant="body2" color="text.secondary">
                  Adjusting boundaries...
                </Typography>
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Milestone 4.2: Enhanced Feature Showcase Toggle */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <SettingsIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Enhanced Feature Showcase
            <EducationalTooltip
              content={foundationEducationalContent.baseTrackingService}
              placement="right"
              size="small"
            />
          </Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={enhancedFeaturesVisible}
                  onChange={(e) => setEnhancedFeaturesVisible(e.target.checked)}
                  color="primary"
                />
              }
              label="Show 35+ Additional M0 Components"
            />
            <Chip
              label={enhancedFeaturesVisible ? "Enhanced View Active" : "Standard View"}
              color={enhancedFeaturesVisible ? "success" : "default"}
              icon={enhancedFeaturesVisible ? <ShowIcon /> : <HideIcon />}
            />
          </Box>
          {enhancedFeaturesVisible && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'action.hover', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                Enhanced features now visible: Advanced memory protection patterns,
                enterprise-grade boundary enforcement, extended service inheritance chains,
                and additional 35+ M0 foundation components.
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Memory Usage" icon={<MemoryIcon />} />
          <Tab label="Component Health" icon={<ShieldIcon />} />
          <Tab label="Protection Status" icon={<SecurityIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && memoryData && (
        <Box>
          <MemoryUsageChart
            services={memoryData.services}
            selectedService={selectedService}
            showThresholds={true}
            height={400}
          />
        </Box>
      )}

      {activeTab === 1 && memoryData && (
        <ComponentHealthGrid
          services={memoryData.services}
          onServiceSelect={setSelectedService}
          selectedService={selectedService}
        />
      )}

      {activeTab === 2 && protectionData && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Protection Status Overview
            </Typography>
            <Typography variant="body1">
              Protection coverage: {protectionData.overallProtection.protectionCoverage}%
            </Typography>
            <Typography variant="body1">
              Average health score: {protectionData.overallProtection.averageHealthScore}%
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Alert Snackbar */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={() => setAlertOpen(false)}
      >
        <Alert severity={alertSeverity} onClose={() => setAlertOpen(false)}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}
