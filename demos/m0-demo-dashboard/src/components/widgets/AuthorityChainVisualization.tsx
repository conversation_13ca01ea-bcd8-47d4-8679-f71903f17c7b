/**
 * Authority Chain Visualization Widget
 * Purpose: Visual representation of governance hierarchy and authority levels
 * Features: Hierarchical display, validation status, permission mapping
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert
} from '@mui/material';
import {
  AccountTree as HierarchyIcon,
  CheckCircle as ValidIcon,
  Error as InvalidIcon,
  Schedule as PendingIcon,
  Info as InfoIcon,
  Security as PermissionIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import type { IAuthorityChain } from '../../types/governance.types';

interface AuthorityChainVisualizationProps {
  chain: IAuthorityChain[];
  validationStatus: 'valid' | 'invalid' | 'pending';
}

export default function AuthorityChainVisualization({
  chain,
  validationStatus
}: AuthorityChainVisualizationProps) {
  const theme = useTheme();
  const [selectedAuthority, setSelectedAuthority] = useState<IAuthorityChain | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  // Get status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return { 
          color: theme.palette.success.main, 
          icon: <ValidIcon />, 
          label: 'Active',
          bgColor: theme.palette.success.light + '20'
        };
      case 'inactive':
        return { 
          color: theme.palette.error.main, 
          icon: <InvalidIcon />, 
          label: 'Inactive',
          bgColor: theme.palette.error.light + '20'
        };
      case 'pending':
        return { 
          color: theme.palette.warning.main, 
          icon: <PendingIcon />, 
          label: 'Pending',
          bgColor: theme.palette.warning.light + '20'
        };
      default:
        return { 
          color: theme.palette.grey[500], 
          icon: <InfoIcon />, 
          label: status,
          bgColor: theme.palette.grey[100]
        };
    }
  };

  // Get validation status configuration
  const getValidationConfig = (status: string) => {
    switch (status) {
      case 'valid':
        return { color: 'success', icon: <ValidIcon />, message: 'Authority chain is valid' };
      case 'invalid':
        return { color: 'error', icon: <InvalidIcon />, message: 'Authority chain validation failed' };
      case 'pending':
        return { color: 'warning', icon: <PendingIcon />, message: 'Authority chain validation pending' };
      default:
        return { color: 'info', icon: <InfoIcon />, message: 'Unknown validation status' };
    }
  };

  // Get authority level color
  const getAuthorityLevelColor = (level: string) => {
    switch (level) {
      case 'E.Z.Consultancy':
        return theme.palette.primary.main;
      case 'M0':
        return theme.palette.secondary.main;
      case 'Operations':
        return theme.palette.info.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Sort chain by hierarchy (E.Z.Consultancy -> M0 -> Operations)
  const sortedChain = [...chain].sort((a, b) => {
    const order = { 'E.Z.Consultancy': 0, 'M0': 1, 'Operations': 2 };
    return (order[a.level as keyof typeof order] || 999) - (order[b.level as keyof typeof order] || 999);
  });

  // Handle authority click
  const handleAuthorityClick = (authority: IAuthorityChain) => {
    setSelectedAuthority(authority);
    setDetailsOpen(true);
  };

  const validationConfig = getValidationConfig(validationStatus);

  return (
    <>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6">
              Authority Chain Hierarchy
            </Typography>
            <Alert 
              severity={validationConfig.color as any} 
              icon={validationConfig.icon}
              sx={{ py: 0 }}
            >
              {validationConfig.message}
            </Alert>
          </Box>

          {/* Hierarchical Visualization */}
          <Box position="relative">
            {sortedChain.map((authority, index) => {
              const statusConfig = getStatusConfig(authority.status);
              const levelColor = getAuthorityLevelColor(authority.level);
              const isLast = index === sortedChain.length - 1;

              return (
                <Box key={authority.level} position="relative">
                  {/* Connection Line */}
                  {!isLast && (
                    <Box
                      position="absolute"
                      left="24px"
                      top="60px"
                      width="2px"
                      height="40px"
                      sx={{
                        backgroundColor: theme.palette.divider,
                        zIndex: 0
                      }}
                    />
                  )}

                  {/* Authority Node */}
                  <Box
                    onClick={() => handleAuthorityClick(authority)}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      mb: 2,
                      backgroundColor: statusConfig.bgColor,
                      border: 2,
                      borderColor: levelColor,
                      borderRadius: 2,
                      cursor: 'pointer',
                      position: 'relative',
                      zIndex: 1,
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                        transform: 'translateX(4px)',
                        transition: 'all 0.2s ease-in-out'
                      }
                    }}
                    style={{ marginLeft: `${index * 20}px` }}
                  >
                    {/* Status Icon */}
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        backgroundColor: levelColor,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mr: 2,
                        color: 'white'
                      }}
                    >
                      {statusConfig.icon}
                    </Box>

                    {/* Authority Info */}
                    <Box flex={1}>
                      <Typography variant="h6" sx={{ color: levelColor }}>
                        {authority.name}
                      </Typography>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        {authority.level}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip
                          label={authority.status}
                          size="small"
                          sx={{
                            backgroundColor: statusConfig.color,
                            color: 'white'
                          }}
                        />
                        {authority.validationRequired && (
                          <Chip
                            label="Validation Required"
                            size="small"
                            color="warning"
                            variant="outlined"
                          />
                        )}
                      </Box>
                    </Box>

                    {/* Permissions Count */}
                    <Box textAlign="center">
                      <Typography variant="h6" color="primary">
                        {authority.permissions.length}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Permissions
                      </Typography>
                    </Box>

                    {/* Info Button */}
                    <Tooltip title="View Details">
                      <IconButton size="small" sx={{ ml: 1 }}>
                        <InfoIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              );
            })}
          </Box>

          {/* Chain Summary */}
          <Box mt={3} p={2} sx={{ backgroundColor: theme.palette.grey[50], borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Chain Summary
            </Typography>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <Typography variant="body2">
                Total Authority Levels: {chain.length}
              </Typography>
              <Typography variant="body2">
                Active Levels: {chain.filter(a => a.status === 'active').length}
              </Typography>
              <Typography variant="body2">
                Total Permissions: {chain.reduce((sum, a) => sum + a.permissions.length, 0)}
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Authority Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedAuthority && (
          <>
            <DialogTitle>
              <Box display="flex" alignItems="center" gap={1}>
                <HierarchyIcon />
                {selectedAuthority.name}
                <Chip
                  label={selectedAuthority.status}
                  size="small"
                  color={getStatusConfig(selectedAuthority.status).color === theme.palette.success.main ? 'success' : 'error'}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box display="grid" gridTemplateColumns="1fr 1fr" gap={3}>
                {/* Authority Information */}
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Authority Information
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Level:</strong> {selectedAuthority.level}
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Status:</strong> {selectedAuthority.status}
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Validation Required:</strong> {selectedAuthority.validationRequired ? 'Yes' : 'No'}
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Last Validation:</strong> {new Date(selectedAuthority.lastValidation).toLocaleString()}
                  </Typography>
                  {selectedAuthority.parentLevel && (
                    <Typography variant="body2" paragraph>
                      <strong>Parent Level:</strong> {selectedAuthority.parentLevel}
                    </Typography>
                  )}
                </Box>

                {/* Permissions */}
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Permissions ({selectedAuthority.permissions.length})
                  </Typography>
                  <List dense>
                    {selectedAuthority.permissions.map((permission, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          <PermissionIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText primary={permission} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </Box>

              {/* Child Levels */}
              {selectedAuthority.childLevels.length > 0 && (
                <Box mt={3}>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="h6" gutterBottom>
                    Child Authority Levels
                  </Typography>
                  <Box display="flex" gap={1} flexWrap="wrap">
                    {selectedAuthority.childLevels.map((childLevel) => (
                      <Chip
                        key={childLevel}
                        label={childLevel}
                        variant="outlined"
                        color="secondary"
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsOpen(false)}>
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
}
