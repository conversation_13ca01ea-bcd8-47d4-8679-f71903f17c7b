/**
 * Foundation Overview Display Component
 * Purpose: Comprehensive foundation monitoring console with infrastructure, dependencies, and architecture
 * Features: Infrastructure status, dependency health, architecture metrics, real-time monitoring
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardHeader,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import Grid from '@mui/material/GridLegacy';
import {
  Architecture as ArchitectureIcon,
  Storage as InfrastructureIcon,
  AccountTree as DependenciesIcon,
  Refresh as RefreshIcon,
  CheckCircle as HealthyIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';
import type {
  IFoundationInfrastructureResponse,
  ISystemDependenciesResponse,
  IArchitectureOverviewResponse,
} from '../../types/foundation.types';

interface FoundationOverviewDisplayProps {
  infrastructureData?: IFoundationInfrastructureResponse;
  dependenciesData?: ISystemDependenciesResponse;
  architectureData?: IArchitectureOverviewResponse;
  loading?: boolean;
  error?: Error | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`foundation-tabpanel-${index}`}
      aria-labelledby={`foundation-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `foundation-tab-${index}`,
    'aria-controls': `foundation-tabpanel-${index}`,
  };
}

// Status color mapping
const getStatusColor = (status: string) => {
  switch (status) {
    case 'operational':
    case 'available':
    case 'active':
      return 'success';
    case 'degraded':
    case 'inactive':
      return 'warning';
    case 'maintenance':
    case 'deprecated':
      return 'info';
    case 'offline':
    case 'unavailable':
      return 'error';
    default:
      return 'default';
  }
};

// Status icon mapping
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'operational':
    case 'available':
    case 'active':
      return <HealthyIcon color="success" />;
    case 'degraded':
    case 'inactive':
      return <WarningIcon color="warning" />;
    case 'offline':
    case 'unavailable':
      return <ErrorIcon color="error" />;
    default:
      return <InfoIcon />;
  }
};

export default function FoundationOverviewDisplay({
  infrastructureData,
  dependenciesData,
  architectureData,
  loading = false,
  error = null,
}: FoundationOverviewDisplayProps) {
  const [mounted, setMounted] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);

  // Hydration safety
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle tab changes
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle accordion changes
  const handleAccordionChange = (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load foundation overview data: {error.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header Section */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
          🎯 Foundation Overview Dashboard
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Comprehensive foundation monitoring including infrastructure, dependencies, and architecture metrics
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Infrastructure Summary */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader
              title="Infrastructure"
              titleTypographyProps={{ variant: 'h6' }}
              action={
                <Tooltip title="Refresh infrastructure data">
                  <IconButton size="small">
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              }
            />
            <CardContent>
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                <Box>
                  <Typography variant="h3" color="primary" gutterBottom>
                    {infrastructureData?.summary?.total || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Total Infrastructure
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                      <Typography variant="body2">Overall Health</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {infrastructureData?.summary?.overallHealth?.toFixed(1) || '0.0'}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={infrastructureData?.summary?.overallHealth || 0}
                      color="success"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Dependencies Summary */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader
              title="Dependencies"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent>
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                <Box>
                  <Typography variant="h3" color="secondary" gutterBottom>
                    {dependenciesData?.summary?.available || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Available Dependencies
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={`${dependenciesData?.summary?.total || 0} Total`}
                      size="small"
                      variant="outlined"
                    />
                    <Chip
                      label={`${dependenciesData?.summary?.critical || 0} Critical`}
                      size="small"
                      variant="outlined"
                      color="error"
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Architecture Summary */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader
              title="Architecture"
              titleTypographyProps={{ variant: 'h6' }}
            />
            <CardContent>
              {loading ? (
                <CircularProgress size={24} />
              ) : (
                <Box>
                  <Typography variant="h3" color="success.main" gutterBottom>
                    {architectureData?.summary?.activeComponents || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Active Components
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={`${architectureData?.summary?.totalComponents || 0} Total`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    <Chip
                      label={`${architectureData?.summary?.totalConnections || 0} Connections`}
                      size="small"
                      variant="outlined"
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs Section */}
      <Paper elevation={1}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<InfrastructureIcon />}
            label="Infrastructure"
            {...a11yProps(0)}
            sx={{ minHeight: 72 }}
          />
          <Tab
            icon={<DependenciesIcon />}
            label="Dependencies"
            {...a11yProps(1)}
            sx={{ minHeight: 72 }}
          />
          <Tab
            icon={<ArchitectureIcon />}
            label="Architecture"
            {...a11yProps(2)}
            sx={{ minHeight: 72 }}
          />
        </Tabs>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          {/* Infrastructure Panel */}
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {infrastructureData?.infrastructure && infrastructureData.infrastructure.length > 0 ? (
                <Grid container spacing={2}>
                  {infrastructureData.infrastructure.map((infra) => (
                    <Grid item xs={12} md={6} lg={4} key={infra.infrastructureId}>
                      <Card>
                        <CardHeader
                          title={infra.infrastructureName}
                          titleTypographyProps={{ variant: 'h6' }}
                          action={
                            <Chip
                              label={infra.status.toUpperCase()}
                              size="small"
                              color={getStatusColor(infra.status) as any}
                              variant="outlined"
                            />
                          }
                        />
                        <CardContent>
                          <Box sx={{ mb: 2 }}>
                            <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
                              <Typography variant="body2">Health Score</Typography>
                              <Typography variant="body2" fontWeight="bold">
                                {infra.health.toFixed(1)}%
                              </Typography>
                            </Box>
                            <LinearProgress
                              variant="determinate"
                              value={infra.health}
                              color={infra.health >= 90 ? 'success' : infra.health >= 70 ? 'warning' : 'error'}
                              sx={{ height: 8, borderRadius: 4 }}
                            />
                          </Box>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Typography variant="body2">
                              Category: {infra.category}
                            </Typography>
                            <Typography variant="body2">
                              Uptime: {infra.uptime.toFixed(2)}%
                            </Typography>
                            <Typography variant="body2">
                              Version: {infra.version}
                            </Typography>
                            {infra.alerts.length > 0 && (
                              <Box sx={{ mt: 1 }}>
                                <Chip
                                  label={`${infra.alerts.length} alerts`}
                                  size="small"
                                  color="warning"
                                  variant="outlined"
                                />
                              </Box>
                            )}
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Alert severity="info">
                  No infrastructure data available. Check infrastructure monitoring configuration.
                </Alert>
              )}
            </Box>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          {/* Dependencies Panel */}
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {dependenciesData?.dependencies && dependenciesData.dependencies.length > 0 ? (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Status</TableCell>
                        <TableCell>Dependency Name</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Category</TableCell>
                        <TableCell>Health</TableCell>
                        <TableCell>Response Time</TableCell>
                        <TableCell>Critical</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {dependenciesData.dependencies.slice(0, 10).map((dependency) => (
                        <TableRow key={dependency.dependencyId}>
                          <TableCell>
                            <Box display="flex" alignItems="center" gap={1}>
                              {getStatusIcon(dependency.status)}
                              <Chip
                                label={dependency.status.toUpperCase()}
                                size="small"
                                color={getStatusColor(dependency.status) as any}
                                variant="outlined"
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {dependency.dependencyName}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              v{dependency.version}
                            </Typography>
                          </TableCell>
                          <TableCell>{dependency.type}</TableCell>
                          <TableCell>
                            <Chip
                              label={dependency.category}
                              size="small"
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2">
                                {dependency.health.toFixed(1)}%
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={dependency.health}
                                color={dependency.health >= 90 ? 'success' : dependency.health >= 70 ? 'warning' : 'error'}
                                sx={{ width: 60, height: 4 }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            {dependency.responseTime.toFixed(0)}ms
                          </TableCell>
                          <TableCell>
                            {dependency.critical ? (
                              <Chip
                                label="Critical"
                                size="small"
                                color="error"
                                variant="outlined"
                              />
                            ) : (
                              <Chip
                                label="Optional"
                                size="small"
                                variant="outlined"
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info">
                  No dependency data available. Check dependency monitoring configuration.
                </Alert>
              )}
            </Box>
          )}
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          {/* Architecture Panel */}
          {loading ? (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {architectureData?.components && architectureData.components.length > 0 ? (
                architectureData.components.slice(0, 8).map((component) => (
                  <Accordion
                    key={component.componentId}
                    expanded={expandedAccordion === component.componentId}
                    onChange={handleAccordionChange(component.componentId)}
                  >
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                        <Typography variant="h6">{component.componentName}</Typography>
                        <Chip
                          label={component.status.toUpperCase()}
                          size="small"
                          color={getStatusColor(component.status) as any}
                          variant="outlined"
                        />
                        <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                          <Chip
                            label={`${component.health.toFixed(1)}% health`}
                            size="small"
                            color="success"
                            variant="outlined"
                          />
                          <Chip
                            label={`${component.testCoverage.toFixed(1)}% coverage`}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle2" gutterBottom>
                            Component Details
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Typography variant="body2">
                              Type: {component.type}
                            </Typography>
                            <Typography variant="body2">
                              Layer: {component.layer}
                            </Typography>
                            <Typography variant="body2">
                              Version: {component.version}
                            </Typography>
                            <Typography variant="body2">
                              Complexity: {component.complexity.toFixed(1)}
                            </Typography>
                            <Typography variant="body2">
                              Maintainability: {component.maintainability.toFixed(1)}%
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography variant="subtitle2" gutterBottom>
                            Metrics
                          </Typography>
                          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                            <Typography variant="body2">
                              Lines of Code: {component.metrics.linesOfCode.toLocaleString()}
                            </Typography>
                            <Typography variant="body2">
                              Bugs: {component.metrics.bugCount}
                            </Typography>
                            <Typography variant="body2">
                              Vulnerabilities: {component.metrics.vulnerabilities}
                            </Typography>
                            <Typography variant="body2">
                              Performance: {component.metrics.performanceScore.toFixed(1)}
                            </Typography>
                            <Typography variant="body2">
                              Connections: {component.connections.length}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                ))
              ) : (
                <Alert severity="info">
                  No architecture data available. Check architecture monitoring configuration.
                </Alert>
              )}
            </Box>
          )}
        </TabPanel>
      </Paper>
    </Box>
  );
}
