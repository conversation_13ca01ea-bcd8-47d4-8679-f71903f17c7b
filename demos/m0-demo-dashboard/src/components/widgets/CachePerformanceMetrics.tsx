/**
 * Cache Performance Metrics Widget
 * Purpose: Display cache hit rates, memory usage, and performance statistics
 * Features: Real-time metrics, cache health indicators, performance trends
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import {
  Speed as PerformanceIcon,
  Memory as MemoryIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Info as InfoIcon,
  CheckCircle as HealthyIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import type { IAnalyticsPerformanceResponse, IAnalyticsCacheMetrics } from '../../types/tracking.types';

interface CachePerformanceMetricsProps {
  data?: IAnalyticsPerformanceResponse;
  loading?: boolean;
  error?: Error | null;
}

export default function CachePerformanceMetrics({ data, loading, error }: CachePerformanceMetricsProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <HealthyIcon color="success" />;
      case 'degraded':
        return <WarningIcon color="warning" />;
      case 'offline':
        return <ErrorIcon color="error" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'degraded': return 'warning';
      case 'offline': return 'error';
      default: return 'default';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms.toFixed(1)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load cache performance data: {error.message}
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress size={40} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading cache performance metrics...
        </Typography>
      </Box>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No cache performance data available
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 3 }}>
        <PerformanceIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
        Cache Performance Metrics
      </Typography>

      {/* Overall Performance Summary */}
      <Box display="flex" flexWrap="wrap" gap={3} mb={4}>
        <Box sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' }, minWidth: 0 }}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Average Hit Rate
                  </Typography>
                  <Typography variant="h4" component="div" color="success.main">
                    {data.overallPerformance.averageHitRate.toFixed(1)}%
                  </Typography>
                </Box>
                <TrendingUpIcon color="success" sx={{ fontSize: 40 }} />
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.overallPerformance.averageHitRate} 
                sx={{ mt: 1, height: 6, borderRadius: 3 }}
                color="success"
              />
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' }, minWidth: 0 }}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Avg Response Time
                  </Typography>
                  <Typography variant="h4" component="div" color="primary.main">
                    {data.overallPerformance.averageResponseTime.toFixed(1)}ms
                  </Typography>
                </Box>
                <PerformanceIcon color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' }, minWidth: 0 }}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Total Requests
                  </Typography>
                  <Typography variant="h4" component="div" color="info.main">
                    {data.overallPerformance.totalRequests.toLocaleString()}
                  </Typography>
                </Box>
                <TrendingUpIcon color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Box>

        <Box sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 12px)', md: '1 1 calc(25% - 18px)' }, minWidth: 0 }}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom variant="body2">
                    Memory Usage
                  </Typography>
                  <Typography variant="h4" component="div" color="warning.main">
                    {formatBytes(data.overallPerformance.totalMemoryUsage)}
                  </Typography>
                </Box>
                <MemoryIcon color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>

      {/* Health Score */}
      <Card elevation={2} sx={{ mb: 4 }}>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Typography variant="h6" component="h3">
              Overall Cache Health Score
            </Typography>
            <Chip 
              label={`${data.overallPerformance.healthScore.toFixed(1)}%`}
              color={data.overallPerformance.healthScore >= 90 ? 'success' : data.overallPerformance.healthScore >= 70 ? 'warning' : 'error'}
              variant="filled"
            />
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={data.overallPerformance.healthScore} 
            sx={{ height: 12, borderRadius: 6 }}
            color={data.overallPerformance.healthScore >= 90 ? 'success' : data.overallPerformance.healthScore >= 70 ? 'warning' : 'error'}
          />
        </CardContent>
      </Card>

      {/* Individual Cache Metrics Table */}
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" component="h3" gutterBottom>
            Individual Cache Performance
          </Typography>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Cache Name</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Hit Rate</TableCell>
                  <TableCell align="right">Response Time</TableCell>
                  <TableCell align="right">Memory Usage</TableCell>
                  <TableCell align="right">Keys</TableCell>
                  <TableCell align="right">Evictions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {data.caches.map((cache) => (
                  <TableRow key={cache.cacheId} hover>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        {getHealthIcon(cache.status)}
                        <Typography variant="body2" fontWeight="medium">
                          {cache.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={cache.type.toUpperCase()} 
                        size="small" 
                        variant="outlined"
                        color="primary"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={cache.status.toUpperCase()} 
                        size="small" 
                        color={getHealthColor(cache.status) as 'primary' | 'secondary' | 'error' | 'info' | 'warning' | 'success' | 'default'}
                        variant="filled"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Box display="flex" alignItems="center" justifyContent="flex-end" gap={1}>
                        <Typography variant="body2" fontWeight="bold">
                          {cache.hitRate.toFixed(1)}%
                        </Typography>
                        {cache.hitRate >= 90 ? (
                          <TrendingUpIcon color="success" fontSize="small" />
                        ) : cache.hitRate < 70 ? (
                          <TrendingDownIcon color="error" fontSize="small" />
                        ) : null}
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {formatDuration(cache.averageResponseTime)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Box>
                        <Typography variant="body2">
                          {formatBytes(cache.memoryUsage)}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          / {formatBytes(cache.maxMemoryUsage)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Box>
                        <Typography variant="body2">
                          {cache.keyCount.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          / {cache.maxKeys.toLocaleString()}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title={cache.lastEviction ? `Last: ${new Date(cache.lastEviction).toLocaleString()}` : 'No evictions'}>
                        <Typography variant="body2">
                          {cache.evictionCount.toLocaleString()}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Last Update Info */}
      <Paper elevation={1} sx={{ p: 2, mt: 3, bgcolor: 'grey.50' }}>
        <Typography variant="body2" color="text.secondary" textAlign="center">
          Last updated: {new Date(data.lastUpdate).toLocaleString()}
        </Typography>
      </Paper>
    </Box>
  );
}
