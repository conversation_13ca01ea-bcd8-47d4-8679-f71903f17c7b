/**
 * Governance Rules List Widget
 * Purpose: Display governance rules with status indicators and filtering
 * Features: Rule status, priority levels, category filtering, validation metrics
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  Gavel as RuleIcon,
  CheckCircle as ActiveIcon,
  Pause as InactiveIcon,
  Schedule as PendingIcon,
  Archive as DeprecatedIcon,
  Info as InfoIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import type { IGovernanceRule } from '../../types/governance.types';

interface GovernanceRulesListProps {
  rules: IGovernanceRule[];
  onRuleSelect?: (ruleId: string) => void;
  selectedRule?: string;
}

export default function GovernanceRulesList({
  rules,
  onRuleSelect,
  selectedRule
}: GovernanceRulesListProps) {
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedRuleDetails, setSelectedRuleDetails] = useState<IGovernanceRule | null>(null);
  const [filterOpen, setFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    category: '',
    authorityLevel: ''
  });

  // Get status color and icon
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return { color: 'success', icon: <ActiveIcon />, label: 'Active' };
      case 'inactive':
        return { color: 'default', icon: <InactiveIcon />, label: 'Inactive' };
      case 'pending':
        return { color: 'warning', icon: <PendingIcon />, label: 'Pending' };
      case 'deprecated':
        return { color: 'error', icon: <DeprecatedIcon />, label: 'Deprecated' };
      default:
        return { color: 'default', icon: <InfoIcon />, label: status };
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  // Get authority level color
  const getAuthorityColor = (level: string) => {
    switch (level) {
      case 'E.Z.Consultancy': return 'primary';
      case 'M0': return 'secondary';
      case 'Operations': return 'info';
      default: return 'default';
    }
  };

  // Filter rules based on current filters
  const filteredRules = rules.filter(rule => {
    return (
      (!filters.status || rule.status === filters.status) &&
      (!filters.priority || rule.priority === filters.priority) &&
      (!filters.category || rule.category === filters.category) &&
      (!filters.authorityLevel || rule.authorityLevel === filters.authorityLevel)
    );
  });

  // Handle rule click
  const handleRuleClick = (rule: IGovernanceRule) => {
    if (onRuleSelect) {
      onRuleSelect(rule.id);
    }
    setSelectedRuleDetails(rule);
    setDetailsOpen(true);
  };

  // Calculate success rate
  const getSuccessRate = (rule: IGovernanceRule) => {
    const total = rule.validationCount;
    const failures = rule.failureCount;
    const successes = total - failures;
    return total > 0 ? Math.round((successes / total) * 100) : 100;
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Governance Rules ({filteredRules.length} of {rules.length})
            </Typography>
            <Box>
              <Tooltip title="Filter Rules">
                <IconButton onClick={() => setFilterOpen(true)}>
                  <FilterIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          <List>
            {filteredRules.map((rule, index) => {
              const statusConfig = getStatusConfig(rule.status);
              const successRate = getSuccessRate(rule);
              const isSelected = selectedRule === rule.id;

              return (
                <React.Fragment key={rule.id}>
                  <ListItem
                    component="div"
                    onClick={() => handleRuleClick(rule)}
                    sx={{
                      border: isSelected ? 2 : 1,
                      borderColor: isSelected ? 'primary.main' : 'divider',
                      borderRadius: 1,
                      mb: 1,
                      cursor: 'pointer',
                      backgroundColor: isSelected ? 'action.selected' : 'transparent',
                      '&:hover': {
                        backgroundColor: 'action.hover'
                      }
                    }}
                  >
                    <ListItemIcon>
                      {statusConfig.icon}
                    </ListItemIcon>
                    <Box sx={{ flex: 1, ml: 2 }}>
                      {/* Primary content */}
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Typography variant="subtitle1" fontWeight="medium" component="span">
                          {rule.name}
                        </Typography>
                        <Chip
                          label={rule.status}
                          size="small"
                          color={statusConfig.color as any}
                          variant="outlined"
                        />
                        <Chip
                          label={rule.priority}
                          size="small"
                          color={getPriorityColor(rule.priority) as any}
                          variant="filled"
                        />
                      </Box>

                      {/* Secondary content */}
                      <Box>
                        <Typography variant="body2" color="textSecondary" gutterBottom component="div">
                          {rule.description}
                        </Typography>
                        <Box display="flex" alignItems="center" gap={2} mt={1}>
                          <Chip
                            label={rule.authorityLevel}
                            size="small"
                            color={getAuthorityColor(rule.authorityLevel) as any}
                            variant="outlined"
                          />
                          <Chip
                            label={rule.category}
                            size="small"
                            variant="outlined"
                          />
                          <Typography variant="caption" color="textSecondary" component="span">
                            Success Rate: {successRate}%
                          </Typography>
                        </Box>
                        <Box mt={1}>
                          <LinearProgress
                            variant="determinate"
                            value={successRate}
                            color={successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error'}
                            sx={{ height: 4, borderRadius: 2 }}
                          />
                        </Box>
                      </Box>
                    </Box>

                    {/* Validation metrics - positioned at the end */}
                    <Box display="flex" flexDirection="column" alignItems="center" sx={{ ml: 2 }}>
                      <Typography variant="caption" color="textSecondary">
                        Validations
                      </Typography>
                      <Typography variant="h6" color="primary">
                        {rule.validationCount}
                      </Typography>
                      {rule.failureCount > 0 && (
                        <Typography variant="caption" color="error">
                          {rule.failureCount} failures
                        </Typography>
                      )}
                    </Box>
                  </ListItem>
                  {index < filteredRules.length - 1 && <Divider />}
                </React.Fragment>
              );
            })}
          </List>

          {filteredRules.length === 0 && (
            <Box textAlign="center" py={4}>
              <Typography color="textSecondary">
                No rules match the current filters
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Rule Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedRuleDetails && (
          <>
            <DialogTitle>
              <Box display="flex" alignItems="center" gap={1}>
                <RuleIcon />
                {selectedRuleDetails.name}
                <Chip
                  label={selectedRuleDetails.status}
                  size="small"
                  color={getStatusConfig(selectedRuleDetails.status).color as any}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Rule Details
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Description:</strong> {selectedRuleDetails.description}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Category:</strong> {selectedRuleDetails.category}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Priority:</strong> {selectedRuleDetails.priority}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Authority Level:</strong> {selectedRuleDetails.authorityLevel}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Validation Metrics
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Total Validations:</strong> {selectedRuleDetails.validationCount}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Failures:</strong> {selectedRuleDetails.failureCount}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Success Rate:</strong> {getSuccessRate(selectedRuleDetails)}%
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Compliance Score:</strong> {selectedRuleDetails.complianceScore}%
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Last Validated:</strong> {new Date(selectedRuleDetails.lastValidated).toLocaleString()}
                  </Typography>
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsOpen(false)}>
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Filter Dialog */}
      <Dialog
        open={filterOpen}
        onClose={() => setFilterOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Filter Governance Rules</DialogTitle>
        <DialogContent>
          <Box display="grid" gap={2} mt={1}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="deprecated">Deprecated</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Priority</InputLabel>
              <Select
                value={filters.priority}
                onChange={(e) => setFilters({...filters, priority: e.target.value})}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="critical">Critical</MenuItem>
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="low">Low</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={filters.category}
                onChange={(e) => setFilters({...filters, category: e.target.value})}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="validation">Validation</MenuItem>
                <MenuItem value="compliance">Compliance</MenuItem>
                <MenuItem value="authority">Authority</MenuItem>
                <MenuItem value="cross-reference">Cross-Reference</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Authority Level</InputLabel>
              <Select
                value={filters.authorityLevel}
                onChange={(e) => setFilters({...filters, authorityLevel: e.target.value})}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="E.Z.Consultancy">E.Z. Consultancy</MenuItem>
                <MenuItem value="M0">M0</MenuItem>
                <MenuItem value="Operations">Operations</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFilters({ status: '', priority: '', category: '', authorityLevel: '' })}>
            Clear All
          </Button>
          <Button onClick={() => setFilterOpen(false)}>
            Apply Filters
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
