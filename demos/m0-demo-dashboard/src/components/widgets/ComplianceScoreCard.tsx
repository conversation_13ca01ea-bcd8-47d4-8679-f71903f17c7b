/**
 * Compliance Score Card Widget
 * Purpose: Display comprehensive compliance metrics and scoring
 * Features: Overall score, category breakdown, trend analysis, validation metrics
 */

'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendUpIcon,
  TrendingDown as TrendDownIcon,
  TrendingFlat as TrendFlatIcon,
  Assessment as ScoreIcon,
  CheckCircle as PassIcon,
  Error as FailIcon
} from '@mui/icons-material';
import {
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';
import { useTheme } from '@mui/material/styles';
import type { IComplianceMetrics } from '../../types/governance.types';

interface ComplianceScoreCardProps {
  metrics: IComplianceMetrics;
  timestamp: string;
}

export default function ComplianceScoreCard({
  metrics,
  timestamp
}: ComplianceScoreCardProps) {
  const theme = useTheme();

  // Get trend icon and color
  const getTrendConfig = (direction: string) => {
    switch (direction) {
      case 'up':
        return { icon: <TrendUpIcon />, color: theme.palette.success.main, label: 'Improving' };
      case 'down':
        return { icon: <TrendDownIcon />, color: theme.palette.error.main, label: 'Declining' };
      case 'stable':
        return { icon: <TrendFlatIcon />, color: theme.palette.info.main, label: 'Stable' };
      default:
        return { icon: <TrendFlatIcon />, color: theme.palette.grey[500], label: 'Unknown' };
    }
  };

  // Get score color based on percentage
  const getScoreColor = (score: number) => {
    if (score >= 90) return theme.palette.success.main;
    if (score >= 70) return theme.palette.warning.main;
    if (score >= 50) return theme.palette.info.main;
    return theme.palette.error.main;
  };

  // Prepare pie chart data
  const pieData = [
    { name: 'Passed', value: metrics.passedValidations, color: theme.palette.success.main },
    { name: 'Failed', value: metrics.failedValidations, color: theme.palette.error.main }
  ];

  // Prepare category scores data
  const categoryData = [
    { name: 'Validation', score: metrics.categoryScores.validation },
    { name: 'Compliance', score: metrics.categoryScores.compliance },
    { name: 'Authority', score: metrics.categoryScores.authority },
    { name: 'Cross-Ref', score: metrics.categoryScores.crossReference }
  ];

  const trendConfig = getTrendConfig(metrics.trendDirection);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            boxShadow: 2
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color }}
            >
              {entry.name}: {entry.value}
              {entry.dataKey === 'score' ? '%' : ''}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Compliance Score Dashboard
        </Typography>

        {/* Overall Score Section */}
        <Box mb={3}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Box display="flex" alignItems="center" gap={1}>
              <ScoreIcon color="primary" />
              <Typography variant="h4" sx={{ color: getScoreColor(metrics.overallScore) }}>
                {metrics.overallScore}%
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Box sx={{ color: trendConfig.color }}>
                {trendConfig.icon}
              </Box>
              <Typography variant="body2" sx={{ color: trendConfig.color }}>
                {trendConfig.label}
              </Typography>
            </Box>
          </Box>

          <LinearProgress
            variant="determinate"
            value={Math.min(metrics.overallScore, 100)}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: theme.palette.grey[200],
              '& .MuiLinearProgress-bar': {
                backgroundColor: getScoreColor(metrics.overallScore),
                borderRadius: 4
              }
            }}
          />

          <Box display="flex" justifyContent="space-between" mt={1}>
            <Typography variant="caption" color="textSecondary">
              Compliance Percentage: {metrics.compliancePercentage}%
            </Typography>
            <Typography variant="caption" color="textSecondary">
              Last Assessment: {new Date(metrics.lastAssessment).toLocaleDateString()}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Metrics Summary */}
        <Box 
          sx={{ 
            display: 'grid', 
            gridTemplateColumns: '1fr 1fr',
            gap: 2,
            mb: 3
          }}
        >
          <Box textAlign="center">
            <Typography variant="h5" color="primary.main">
              {metrics.totalRules}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Total Rules
            </Typography>
          </Box>
          <Box textAlign="center">
            <Typography variant="h5" color="success.main">
              {metrics.activeRules}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Active Rules
            </Typography>
          </Box>
        </Box>

        {/* Validation Results */}
        <Box mb={3}>
          <Typography variant="h6" gutterBottom>
            Validation Results
          </Typography>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Box display="flex" alignItems="center" gap={1}>
              <PassIcon color="success" />
              <Typography variant="body1">
                Passed: {metrics.passedValidations}
              </Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <FailIcon color="error" />
              <Typography variant="body1">
                Failed: {metrics.failedValidations}
              </Typography>
            </Box>
          </Box>

          {/* Pie Chart */}
          <Box height={200}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Category Scores */}
        <Box>
          <Typography variant="h6" gutterBottom>
            Category Scores
          </Typography>
          
          {/* Category Score Bars */}
          <Box mb={2}>
            {categoryData.map((category) => (
              <Box key={category.name} mb={2}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="body2" fontWeight="medium">
                    {category.name}
                  </Typography>
                  <Typography variant="body2" color="primary">
                    {category.score}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={category.score}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: theme.palette.grey[200],
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getScoreColor(category.score),
                      borderRadius: 3
                    }
                  }}
                />
              </Box>
            ))}
          </Box>

          {/* Category Bar Chart */}
          <Box height={200}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={categoryData}>
                <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                <XAxis 
                  dataKey="name" 
                  stroke={theme.palette.text.secondary}
                  fontSize={12}
                />
                <YAxis 
                  stroke={theme.palette.text.secondary}
                  fontSize={12}
                  domain={[0, 100]}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="score" 
                  fill={theme.palette.primary.main}
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </Box>

        {/* Footer */}
        <Box mt={3} pt={2} borderTop={1} borderColor="divider">
          <Box display="flex" justifyContent="between" alignItems="center">
            <Typography variant="caption" color="textSecondary">
              Last updated: {new Date(timestamp).toLocaleString()}
            </Typography>
            <Chip
              label={`${metrics.compliancePercentage}% Compliant`}
              color={metrics.compliancePercentage >= 90 ? 'success' : 'warning'}
              size="small"
            />
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
}
