/**
 * Component Status Grid Widget
 * Purpose: Grid layout showing status of all tracked components with color-coded health indicators
 * Features: Health status visualization, filtering, detailed component information
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  CircularProgress,
  Alert,
  Paper,
  Tooltip,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Dashboard as ComponentIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  CheckCircle as HealthyIcon,
  Warning as DegradedIcon,
  Error as OfflineIcon,
  Build as MaintenanceIcon,
  Info as InfoIcon,
  Speed as PerformanceIcon,
  Memory as MemoryIcon,
  Timeline as UptimeIcon
} from '@mui/icons-material';
import type { IComponentStatusResponse, IComponentStatus, IComponentAlert } from '../../types/tracking.types';

interface ComponentStatusGridProps {
  data?: IComponentStatusResponse;
  loading?: boolean;
  error?: Error | null;
}

export default function ComponentStatusGrid({ data, loading, error }: ComponentStatusGridProps) {
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedComponent, setSelectedComponent] = useState<IComponentStatus | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [filteredComponents, setFilteredComponents] = useState<IComponentStatus[]>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Filter components based on search and filters
  useEffect(() => {
    if (!data?.components) {
      setFilteredComponents([]);
      return;
    }

    let filtered = data.components;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(component =>
        component.componentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.componentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        component.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(component => component.category === categoryFilter);
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(component => component.status === statusFilter);
    }

    setFilteredComponents(filtered);
  }, [data, searchTerm, categoryFilter, statusFilter]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <HealthyIcon color="success" />;
      case 'degraded':
        return <DegradedIcon color="warning" />;
      case 'offline':
        return <OfflineIcon color="error" />;
      case 'maintenance':
        return <MaintenanceIcon color="info" />;
      default:
        return <InfoIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'success';
      case 'degraded': return 'warning';
      case 'offline': return 'error';
      case 'maintenance': return 'info';
      default: return 'default';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'governance': return 'primary';
      case 'tracking': return 'secondary';
      case 'security': return 'error';
      case 'integration': return 'info';
      case 'foundation': return 'warning';
      default: return 'default';
    }
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (24 * 60 * 60 * 1000));
    const hours = Math.floor((uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000));
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const handleComponentClick = (component: IComponentStatus) => {
    setSelectedComponent(component);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedComponent(null);
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load component status data: {error.message}
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress size={40} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading component status...
        </Typography>
      </Box>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No component status data available
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header with Summary */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          <ComponentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Component Status Grid
        </Typography>
        <Box display="flex" gap={1}>
          <Chip
            label={`${data?.summary?.operational || 0} Operational`}
            color="success"
            size="small"
            variant="outlined"
          />
          <Chip
            label={`${data?.summary?.degraded || 0} Degraded`}
            color="warning"
            size="small"
            variant="outlined"
          />
          <Chip
            label={`${data?.summary?.offline || 0} Offline`}
            color="error"
            size="small"
            variant="outlined"
          />
          <Chip
            label={`${data?.summary?.maintenance || 0} Maintenance`}
            color="info"
            size="small"
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Filters */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" flexWrap="wrap" gap={2} alignItems="center">
            <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 8px)' }, minWidth: 0 }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search components..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(25% - 12px)' }, minWidth: 0 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Category</InputLabel>
                <Select
                  value={categoryFilter}
                  label="Category"
                  onChange={(e) => setCategoryFilter(e.target.value)}
                >
                  <MenuItem value="all">All Categories</MenuItem>
                  <MenuItem value="governance">Governance</MenuItem>
                  <MenuItem value="tracking">Tracking</MenuItem>
                  <MenuItem value="security">Security</MenuItem>
                  <MenuItem value="integration">Integration</MenuItem>
                  <MenuItem value="foundation">Foundation</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(25% - 12px)' }, minWidth: 0 }}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="operational">Operational</MenuItem>
                  <MenuItem value="degraded">Degraded</MenuItem>
                  <MenuItem value="offline">Offline</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Component Grid */}
      <Box display="flex" flexWrap="wrap" gap={2}>
        {filteredComponents.map((component) => (
          <Box key={component.componentId} sx={{ flex: { xs: '1 1 100%', sm: '1 1 calc(50% - 8px)', md: '1 1 calc(33.333% - 11px)', lg: '1 1 calc(25% - 12px)' }, minWidth: 0 }}>
            <Card 
              elevation={2} 
              sx={{ 
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  elevation: 4,
                  transform: 'translateY(-2px)'
                }
              }}
              onClick={() => handleComponentClick(component)}
            >
              <CardContent sx={{ p: 2 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                  <Badge
                    badgeContent={component.alerts?.filter(a => !a.resolved).length || 0}
                    color="error"
                    invisible={(component.alerts?.filter(a => !a.resolved).length || 0) === 0}
                  >
                    <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                      {getStatusIcon(component.status || 'unknown')}
                    </Avatar>
                  </Badge>
                  <Chip
                    label={component.category?.toUpperCase() || 'UNKNOWN'}
                    size="small"
                    color={getCategoryColor(component.category || 'default') as 'primary' | 'secondary' | 'error' | 'info' | 'warning' | 'success' | 'default'}
                    variant="outlined"
                  />
                </Box>

                <Typography variant="subtitle2" component="h3" gutterBottom noWrap>
                  {component.componentName || 'Unknown Component'}
                </Typography>

                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  {component.componentId || 'unknown-id'}
                </Typography>

                <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                  <Chip
                    label={component.status?.toUpperCase() || 'UNKNOWN'}
                    size="small"
                    color={getStatusColor(component.status || 'default') as 'primary' | 'secondary' | 'error' | 'info' | 'warning' | 'success' | 'default'}
                    variant="filled"
                  />
                  <Typography variant="caption" color="text.secondary">
                    v{component.version || '0.0.0'}
                  </Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <Tooltip title="Health Score">
                    <Box display="flex" alignItems="center" gap={0.5}>
                      <PerformanceIcon fontSize="small" color="action" />
                      <Typography variant="caption">
                        {component.healthScore || 0}%
                      </Typography>
                    </Box>
                  </Tooltip>
                  <Tooltip title="Uptime">
                    <Box display="flex" alignItems="center" gap={0.5}>
                      <UptimeIcon fontSize="small" color="action" />
                      <Typography variant="caption">
                        {formatUptime(component.uptime || 0)}
                      </Typography>
                    </Box>
                  </Tooltip>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="caption" color="text.secondary">
                    {component.metrics?.requestsPerSecond?.toFixed(1) || '0.0'} req/s
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {component.metrics?.averageResponseTime?.toFixed(1) || '0.0'}ms
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Box>
        ))}
      </Box>

      {filteredComponents.length === 0 && (
        <Paper elevation={1} sx={{ p: 4, textAlign: 'center', mt: 3 }}>
          <Typography variant="body1" color="text.secondary">
            {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all' 
              ? 'No components match your filters' 
              : 'No components available'
            }
          </Typography>
        </Paper>
      )}

      {/* Component Details Dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleDialogClose}
        maxWidth="md"
        fullWidth
      >
        {selectedComponent && (
          <>
            <DialogTitle>
              <Box display="flex" alignItems="center" gap={2}>
                {getStatusIcon(selectedComponent.status)}
                <Box>
                  <Typography variant="h6">
                    {selectedComponent.componentName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {selectedComponent.componentId}
                  </Typography>
                </Box>
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box display="flex" flexWrap="wrap" gap={2}>
                <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 8px)' }, minWidth: 0 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Status Information
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText 
                        primary="Status" 
                        secondary={
                          <Chip 
                            label={selectedComponent.status.toUpperCase()} 
                            size="small" 
                            color={getStatusColor(selectedComponent.status) as 'primary' | 'secondary' | 'error' | 'info' | 'warning' | 'success' | 'default'}
                          />
                        } 
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Health Score" secondary={`${selectedComponent.healthScore}%`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Uptime" secondary={formatUptime(selectedComponent.uptime)} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Version" secondary={selectedComponent.version} />
                    </ListItem>
                  </List>
                </Box>
                <Box sx={{ flex: { xs: '1 1 100%', md: '1 1 calc(50% - 8px)' }, minWidth: 0 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Performance Metrics
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText primary="Requests/Second" secondary={selectedComponent.metrics.requestsPerSecond.toFixed(2)} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Avg Response Time" secondary={`${selectedComponent.metrics.averageResponseTime.toFixed(1)}ms`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Error Rate" secondary={`${selectedComponent.metrics.errorRate.toFixed(2)}%`} />
                    </ListItem>
                    <ListItem>
                      <ListItemText primary="Throughput" secondary={selectedComponent.metrics.throughput.toFixed(2)} />
                    </ListItem>
                  </List>
                </Box>
              </Box>
              
              {(selectedComponent.alerts?.length || 0) > 0 && (
                <Box mt={2}>
                  <Typography variant="subtitle2" gutterBottom>
                    Active Alerts
                  </Typography>
                  {selectedComponent.alerts?.filter(alert => !alert.resolved).map((alert) => (
                    <Alert 
                      key={alert.alertId} 
                      severity={alert.severity === 'critical' ? 'error' : alert.severity === 'high' ? 'warning' : 'info'}
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2">
                        {alert.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {new Date(alert.timestamp).toLocaleString()}
                      </Typography>
                    </Alert>
                  ))}
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleDialogClose}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Summary Footer */}
      <Paper elevation={1} sx={{ p: 2, mt: 3, bgcolor: 'grey.50' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">
            Showing {filteredComponents.length} of {data?.summary?.total || 0} components
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Overall Health: {data?.summary?.overallHealth?.toFixed(1) || '0.0'}%
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
}
