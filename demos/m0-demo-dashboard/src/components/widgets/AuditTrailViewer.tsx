/**
 * Audit Trail Viewer Widget
 * Purpose: Interactive audit log display with filtering and search
 * Features: Real-time audit entries, filtering, pagination, detailed view
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Pagination,
  Divider,
  Alert
} from '@mui/material';
import {
  History as AuditIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Search as SearchIcon,
  Visibility as ViewIcon,
  AccessTime as TimeIcon,
  Person as UserIcon,
  Business as ComponentIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import type { IAuditEntry } from '../../types/governance.types';

interface AuditTrailViewerProps {
  entries: IAuditEntry[];
  total: number;
}

export default function AuditTrailViewer({
  entries,
  total
}: AuditTrailViewerProps) {
  const theme = useTheme();
  const [selectedEntry, setSelectedEntry] = useState<IAuditEntry | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [authorityFilter, setAuthorityFilter] = useState('');
  const [componentFilter, setComponentFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const entriesPerPage = 10;

  // Get status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'success':
        return { 
          color: theme.palette.success.main, 
          icon: <SuccessIcon />, 
          label: 'Success',
          bgColor: theme.palette.success.light + '20'
        };
      case 'failure':
        return { 
          color: theme.palette.error.main, 
          icon: <ErrorIcon />, 
          label: 'Failure',
          bgColor: theme.palette.error.light + '20'
        };
      case 'warning':
        return { 
          color: theme.palette.warning.main, 
          icon: <WarningIcon />, 
          label: 'Warning',
          bgColor: theme.palette.warning.light + '20'
        };
      case 'info':
        return { 
          color: theme.palette.info.main, 
          icon: <InfoIcon />, 
          label: 'Info',
          bgColor: theme.palette.info.light + '20'
        };
      default:
        return { 
          color: theme.palette.grey[500], 
          icon: <InfoIcon />, 
          label: status,
          bgColor: theme.palette.grey[100]
        };
    }
  };

  // Get impact level color
  const getImpactColor = (level: string) => {
    switch (level) {
      case 'critical': return 'error';
      case 'high': return 'warning';
      case 'medium': return 'info';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  // Get authority level color
  const getAuthorityColor = (level: string) => {
    switch (level) {
      case 'E.Z.Consultancy': return 'primary';
      case 'M0': return 'secondary';
      case 'Operations': return 'info';
      default: return 'default';
    }
  };

  // Filter entries based on current filters
  const filteredEntries = entries.filter(entry => {
    const matchesSearch = !searchTerm || 
      entry.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.component.toLowerCase().includes(searchTerm.toLowerCase()) ||
      entry.details.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || entry.status === statusFilter;
    const matchesAuthority = !authorityFilter || entry.authorityLevel === authorityFilter;
    const matchesComponent = !componentFilter || entry.component.includes(componentFilter);

    return matchesSearch && matchesStatus && matchesAuthority && matchesComponent;
  });

  // Paginate entries
  const startIndex = (currentPage - 1) * entriesPerPage;
  const paginatedEntries = filteredEntries.slice(startIndex, startIndex + entriesPerPage);
  const totalPages = Math.ceil(filteredEntries.length / entriesPerPage);

  // Handle entry click
  const handleEntryClick = (entry: IAuditEntry) => {
    setSelectedEntry(entry);
    setDetailsOpen(true);
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString()
    };
  };

  return (
    <>
      <Card>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h6">
              Audit Trail ({filteredEntries.length} of {total} entries)
            </Typography>
            <Box display="flex" alignItems="center" gap={1}>
              <Tooltip title="Real-time updates every 5 seconds">
                <Chip
                  icon={<TimeIcon />}
                  label="Live"
                  color="success"
                  variant="outlined"
                  size="small"
                />
              </Tooltip>
            </Box>
          </Box>

          {/* Filters */}
          <Box 
            sx={{ 
              display: 'grid', 
              gridTemplateColumns: { xs: '1fr', sm: '2fr 1fr 1fr 1fr' },
              gap: 2,
              mb: 3
            }}
          >
            <TextField
              label="Search"
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              slotProps={{
                input: {
                  startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />
                }
              }}
            />
            <FormControl size="small">
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="success">Success</MenuItem>
                <MenuItem value="failure">Failure</MenuItem>
                <MenuItem value="warning">Warning</MenuItem>
                <MenuItem value="info">Info</MenuItem>
              </Select>
            </FormControl>
            <FormControl size="small">
              <InputLabel>Authority</InputLabel>
              <Select
                value={authorityFilter}
                onChange={(e) => setAuthorityFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="E.Z.Consultancy">E.Z. Consultancy</MenuItem>
                <MenuItem value="M0">M0</MenuItem>
                <MenuItem value="Operations">Operations</MenuItem>
              </Select>
            </FormControl>
            <FormControl size="small">
              <InputLabel>Component</InputLabel>
              <Select
                value={componentFilter}
                onChange={(e) => setComponentFilter(e.target.value)}
              >
                <MenuItem value="">All</MenuItem>
                <MenuItem value="governance">Governance</MenuItem>
                <MenuItem value="security">Security</MenuItem>
                <MenuItem value="validation">Validation</MenuItem>
                <MenuItem value="authority">Authority</MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Audit Entries List */}
          <List>
            {paginatedEntries.map((entry, index) => {
              const statusConfig = getStatusConfig(entry.status);
              const timestamp = formatTimestamp(entry.timestamp);

              return (
                <React.Fragment key={entry.id}>
                  <ListItem
                    component="div"
                    onClick={() => handleEntryClick(entry)}
                    sx={{
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1,
                      mb: 1,
                      cursor: 'pointer',
                      backgroundColor: statusConfig.bgColor,
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box sx={{ color: statusConfig.color }}>
                        {statusConfig.icon}
                      </Box>
                    </ListItemIcon>
                    <Box sx={{ flex: 1, ml: 2 }}>
                      {/* Primary content */}
                      <Box display="flex" alignItems="center" gap={1} mb={1}>
                        <Typography variant="subtitle1" fontWeight="medium" component="span">
                          {entry.action}
                        </Typography>
                        <Chip
                          label={entry.status}
                          size="small"
                          sx={{
                            backgroundColor: statusConfig.color,
                            color: 'white'
                          }}
                        />
                        <Chip
                          label={entry.impactLevel}
                          size="small"
                          color={getImpactColor(entry.impactLevel) as any}
                          variant="outlined"
                        />
                      </Box>

                      {/* Secondary content */}
                      <Box>
                        <Typography variant="body2" color="textSecondary" gutterBottom component="div">
                          {entry.details}
                        </Typography>
                        <Box display="flex" alignItems="center" gap={2} mt={1}>
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <ComponentIcon fontSize="small" />
                            <Typography variant="caption" component="span">
                              {entry.component}
                            </Typography>
                          </Box>
                          <Chip
                            label={entry.authorityLevel}
                            size="small"
                            color={getAuthorityColor(entry.authorityLevel) as any}
                            variant="outlined"
                          />
                          {entry.userId && (
                            <Box display="flex" alignItems="center" gap={0.5}>
                              <UserIcon fontSize="small" />
                              <Typography variant="caption" component="span">
                                {entry.userId}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </Box>
                    </Box>

                    {/* Timestamp and actions - positioned at the end */}
                    <Box textAlign="right" sx={{ ml: 2 }}>
                      <Typography variant="caption" color="textSecondary">
                        {timestamp.date}
                      </Typography>
                      <Typography variant="caption" display="block" color="textSecondary">
                        {timestamp.time}
                      </Typography>
                      <Tooltip title="View Details">
                        <IconButton size="small">
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItem>
                  {index < paginatedEntries.length - 1 && <Divider />}
                </React.Fragment>
              );
            })}
          </List>

          {filteredEntries.length === 0 && (
            <Box textAlign="center" py={4}>
              <Typography color="textSecondary">
                No audit entries match the current filters
              </Typography>
            </Box>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box display="flex" justifyContent="center" mt={3}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(_, page) => setCurrentPage(page)}
                color="primary"
              />
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Entry Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedEntry && (
          <>
            <DialogTitle>
              <Box display="flex" alignItems="center" gap={1}>
                <AuditIcon />
                Audit Entry Details
                <Chip
                  label={selectedEntry.status}
                  size="small"
                  color={getStatusConfig(selectedEntry.status).color === theme.palette.success.main ? 'success' : 'error'}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box display="grid" gridTemplateColumns="1fr 1fr" gap={3}>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Entry Information
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Action:</strong> {selectedEntry.action}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Component:</strong> {selectedEntry.component}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Status:</strong> {selectedEntry.status}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Impact Level:</strong> {selectedEntry.impactLevel}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Authority Level:</strong> {selectedEntry.authorityLevel}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Timestamp:</strong> {new Date(selectedEntry.timestamp).toLocaleString()}
                  </Typography>
                </Box>
                <Box>
                  <Typography variant="h6" gutterBottom>
                    Additional Details
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    <strong>Details:</strong>
                  </Typography>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    {selectedEntry.details}
                  </Alert>
                  {selectedEntry.userId && (
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      <strong>User ID:</strong> {selectedEntry.userId}
                    </Typography>
                  )}
                  {selectedEntry.ruleId && (
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      <strong>Rule ID:</strong> {selectedEntry.ruleId}
                    </Typography>
                  )}
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsOpen(false)}>
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
}
