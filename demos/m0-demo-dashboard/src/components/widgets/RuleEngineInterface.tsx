/**
 * Rule Engine Interface Widget
 * Purpose: Controls for governance rule management and testing
 * Features: Rule creation, validation testing, engine configuration
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Settings as EngineIcon,
  Add as AddIcon,
  PlayArrow as TestIcon,
  Refresh as RefreshIcon,
  Save as SaveIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as ValidIcon,
  Error as ErrorIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

interface RuleEngineInterfaceProps {
  selectedRule?: string;
  onOperationComplete?: (message: string) => void;
}

export default function RuleEngineInterface({
  selectedRule,
  onOperationComplete
}: RuleEngineInterfaceProps) {
  const theme = useTheme();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [operationRunning, setOperationRunning] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  
  // Form state for new rule creation
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    priority: 'medium',
    category: 'validation',
    authorityLevel: 'M0',
    ruleCode: ''
  });

  // Mock engine status
  const engineStatus = {
    status: 'operational',
    version: '2.1.0',
    rulesLoaded: 58,
    lastRestart: '2025-09-03T10:30:00Z',
    memoryUsage: '245MB',
    processingRate: '1,247 rules/sec'
  };

  // Mock recent operations
  const recentOperations = [
    { id: '1', action: 'Rule Validation', status: 'success', timestamp: '2025-09-03T17:25:00Z' },
    { id: '2', action: 'Engine Restart', status: 'success', timestamp: '2025-09-03T17:20:00Z' },
    { id: '3', action: 'Rule Creation', status: 'success', timestamp: '2025-09-03T17:15:00Z' },
    { id: '4', action: 'Authority Test', status: 'warning', timestamp: '2025-09-03T17:10:00Z' }
  ];

  // Handle rule creation
  const handleCreateRule = async () => {
    setOperationRunning(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setCreateDialogOpen(false);
      setNewRule({
        name: '',
        description: '',
        priority: 'medium',
        category: 'validation',
        authorityLevel: 'M0',
        ruleCode: ''
      });
      
      if (onOperationComplete) {
        onOperationComplete(`Rule "${newRule.name}" created successfully`);
      }
    } catch (error) {
      console.error('Failed to create rule:', error);
    } finally {
      setOperationRunning(false);
    }
  };

  // Handle rule testing
  const handleTestRule = async (testType: string) => {
    setOperationRunning(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock test results
      const mockResults = {
        testType,
        status: Math.random() > 0.3 ? 'success' : 'failure',
        executionTime: Math.floor(Math.random() * 500) + 50,
        rulesProcessed: Math.floor(Math.random() * 50) + 10,
        validationsPassed: Math.floor(Math.random() * 45) + 5,
        validationsFailed: Math.floor(Math.random() * 5),
        details: `${testType} completed with ${Math.random() > 0.5 ? 'no issues' : 'minor warnings'}`
      };
      
      setTestResults(mockResults);
      
      if (onOperationComplete) {
        onOperationComplete(`${testType} completed: ${mockResults.status}`);
      }
    } catch (error) {
      console.error('Failed to test rule:', error);
    } finally {
      setOperationRunning(false);
    }
  };

  // Handle engine operations
  const handleEngineOperation = async (operation: string) => {
    setOperationRunning(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (onOperationComplete) {
        onOperationComplete(`Engine ${operation} completed successfully`);
      }
    } catch (error) {
      console.error(`Failed to ${operation} engine:`, error);
    } finally {
      setOperationRunning(false);
    }
  };

  return (
    <>
      <Box display="grid" gridTemplateColumns={{ xs: '1fr', md: '1fr 1fr' }} gap={3}>
        {/* Engine Status */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Rule Engine Status
            </Typography>
            
            <Box mb={2}>
              <Chip
                label={engineStatus.status}
                color={engineStatus.status === 'operational' ? 'success' : 'error'}
                icon={engineStatus.status === 'operational' ? <ValidIcon /> : <ErrorIcon />}
              />
            </Box>

            <Box display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  Version
                </Typography>
                <Typography variant="h6">
                  {engineStatus.version}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  Rules Loaded
                </Typography>
                <Typography variant="h6">
                  {engineStatus.rulesLoaded}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  Memory Usage
                </Typography>
                <Typography variant="h6">
                  {engineStatus.memoryUsage}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="textSecondary">
                  Processing Rate
                </Typography>
                <Typography variant="h6">
                  {engineStatus.processingRate}
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box display="flex" gap={1} flexWrap="wrap">
              <Button
                variant="contained"
                color="primary"
                startIcon={operationRunning ? <CircularProgress size={16} color="inherit" /> : <RefreshIcon />}
                onClick={() => handleEngineOperation('restart')}
                disabled={operationRunning}
                size="small"
              >
                Restart Engine
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={() => handleEngineOperation('reload')}
                disabled={operationRunning}
                size="small"
              >
                Reload Rules
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Rule Operations */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Rule Operations
            </Typography>

            <Box display="flex" flexDirection="column" gap={2}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => setCreateDialogOpen(true)}
                fullWidth
              >
                Create New Rule
              </Button>

              <Button
                variant="contained"
                color="info"
                startIcon={<TestIcon />}
                onClick={() => setTestDialogOpen(true)}
                fullWidth
              >
                Test Rules
              </Button>

              <Button
                variant="outlined"
                startIcon={<CodeIcon />}
                onClick={() => handleEngineOperation('validate-syntax')}
                disabled={operationRunning}
                fullWidth
              >
                Validate Rule Syntax
              </Button>

              {selectedRule && (
                <Alert severity="info">
                  Selected Rule: {selectedRule}
                </Alert>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Recent Operations */}
        <Card sx={{ gridColumn: { xs: '1', md: '1 / -1' } }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Engine Operations
            </Typography>

            <List>
              {recentOperations.map((operation) => (
                <ListItem key={operation.id}>
                  <ListItemIcon>
                    {operation.status === 'success' ? (
                      <ValidIcon color="success" />
                    ) : operation.status === 'warning' ? (
                      <ErrorIcon color="warning" />
                    ) : (
                      <ErrorIcon color="error" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={operation.action}
                    secondary={new Date(operation.timestamp).toLocaleString()}
                  />
                  <Chip
                    label={operation.status}
                    size="small"
                    color={
                      operation.status === 'success' ? 'success' :
                      operation.status === 'warning' ? 'warning' : 'error'
                    }
                  />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Box>

      {/* Create Rule Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Create New Governance Rule</DialogTitle>
        <DialogContent>
          <Box display="grid" gap={2} mt={1}>
            <TextField
              label="Rule Name"
              fullWidth
              value={newRule.name}
              onChange={(e) => setNewRule({...newRule, name: e.target.value})}
            />
            <TextField
              label="Description"
              fullWidth
              multiline
              rows={3}
              value={newRule.description}
              onChange={(e) => setNewRule({...newRule, description: e.target.value})}
            />
            <Box display="grid" gridTemplateColumns="1fr 1fr 1fr" gap={2}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={newRule.priority}
                  onChange={(e) => setNewRule({...newRule, priority: e.target.value})}
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={newRule.category}
                  onChange={(e) => setNewRule({...newRule, category: e.target.value})}
                >
                  <MenuItem value="validation">Validation</MenuItem>
                  <MenuItem value="compliance">Compliance</MenuItem>
                  <MenuItem value="authority">Authority</MenuItem>
                  <MenuItem value="cross-reference">Cross-Reference</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth>
                <InputLabel>Authority Level</InputLabel>
                <Select
                  value={newRule.authorityLevel}
                  onChange={(e) => setNewRule({...newRule, authorityLevel: e.target.value})}
                >
                  <MenuItem value="E.Z.Consultancy">E.Z. Consultancy</MenuItem>
                  <MenuItem value="M0">M0</MenuItem>
                  <MenuItem value="Operations">Operations</MenuItem>
                </Select>
              </FormControl>
            </Box>
            <TextField
              label="Rule Code (Optional)"
              fullWidth
              multiline
              rows={4}
              value={newRule.ruleCode}
              onChange={(e) => setNewRule({...newRule, ruleCode: e.target.value})}
              placeholder="Enter rule logic or validation code..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateRule}
            variant="contained"
            disabled={operationRunning || !newRule.name || !newRule.description}
            startIcon={operationRunning ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
          >
            Create Rule
          </Button>
        </DialogActions>
      </Dialog>

      {/* Test Rules Dialog */}
      <Dialog
        open={testDialogOpen}
        onClose={() => setTestDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Rule Engine Testing</DialogTitle>
        <DialogContent>
          <Box display="grid" gap={2} mt={1}>
            <Typography variant="body1" gutterBottom>
              Select a test type to validate rule engine functionality:
            </Typography>
            
            <Box display="grid" gridTemplateColumns="1fr 1fr" gap={2}>
              <Button
                variant="outlined"
                onClick={() => handleTestRule('Syntax Validation')}
                disabled={operationRunning}
                startIcon={operationRunning ? <CircularProgress size={16} /> : <TestIcon />}
              >
                Syntax Validation
              </Button>
              <Button
                variant="outlined"
                onClick={() => handleTestRule('Authority Chain Test')}
                disabled={operationRunning}
                startIcon={operationRunning ? <CircularProgress size={16} /> : <TestIcon />}
              >
                Authority Chain Test
              </Button>
              <Button
                variant="outlined"
                onClick={() => handleTestRule('Cross-Reference Validation')}
                disabled={operationRunning}
                startIcon={operationRunning ? <CircularProgress size={16} /> : <TestIcon />}
              >
                Cross-Reference Test
              </Button>
              <Button
                variant="outlined"
                onClick={() => handleTestRule('Full Rule Validation')}
                disabled={operationRunning}
                startIcon={operationRunning ? <CircularProgress size={16} /> : <TestIcon />}
              >
                Full Validation
              </Button>
            </Box>

            {testResults && (
              <Box mt={3}>
                <Alert severity={testResults.status === 'success' ? 'success' : 'error'}>
                  <Typography variant="subtitle2" gutterBottom>
                    {testResults.testType} Results
                  </Typography>
                  <Typography variant="body2">
                    Status: {testResults.status}<br/>
                    Execution Time: {testResults.executionTime}ms<br/>
                    Rules Processed: {testResults.rulesProcessed}<br/>
                    Validations Passed: {testResults.validationsPassed}<br/>
                    Validations Failed: {testResults.validationsFailed}<br/>
                    Details: {testResults.details}
                  </Typography>
                </Alert>
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
