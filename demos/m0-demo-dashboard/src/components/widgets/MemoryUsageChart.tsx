/**
 * Memory Usage Chart Widget
 * Purpose: Real-time memory usage visualization for M0 security services
 * Features: Line charts, memory thresholds, service health indicators
 */

'use client';

import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  Area,
  AreaChart
} from 'recharts';
import { Card, CardContent, Typography, Box, Chip } from '@mui/material';
import Grid from '@mui/material/Grid';
import { useTheme } from '@mui/material/styles';
import type { IMemorySafetyMetrics, IMemoryDataPoint } from '../../types/security.types';

interface MemoryUsageChartProps {
  services: IMemorySafetyMetrics[];
  selectedService?: string;
  showThresholds?: boolean;
  height?: number;
}

export default function MemoryUsageChart({
  services,
  selectedService,
  showThresholds = true,
  height = 400
}: MemoryUsageChartProps) {
  const theme = useTheme();

  // Get the selected service or use the first one
  const service = selectedService 
    ? services.find(s => s.serviceId === selectedService) 
    : services[0];

  if (!service) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Memory Usage Chart
          </Typography>
          <Box display="flex" justifyContent="center" alignItems="center" height={height}>
            <Typography color="textSecondary">
              No service data available
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  // Prepare chart data from memory history
  const chartData = service.memoryHistory.map((point: IMemoryDataPoint) => ({
    time: new Date(point.timestamp).toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    }),
    memory: Math.round(point.memoryUsage),
    cpu: Math.round(point.cpuUsage),
    connections: point.activeConnections,
    requests: point.requestCount,
    errors: point.errorCount,
    threshold: service.memoryUsage.threshold,
    maximum: service.memoryUsage.maximum
  }));

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <Box
          sx={{
            backgroundColor: 'background.paper',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            p: 2,
            boxShadow: 2
          }}
        >
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry: any, index: number) => (
            <Typography
              key={index}
              variant="body2"
              sx={{ color: entry.color }}
            >
              {entry.name}: {entry.value}
              {entry.dataKey === 'memory' || entry.dataKey === 'threshold' || entry.dataKey === 'maximum' ? ' MB' : ''}
              {entry.dataKey === 'cpu' ? '%' : ''}
            </Typography>
          ))}
        </Box>
      );
    }
    return null;
  };

  // Status color based on memory usage
  const getStatusColor = (percentage: number) => {
    if (percentage >= 90) return theme.palette.error.main;
    if (percentage >= 75) return theme.palette.warning.main;
    if (percentage >= 50) return theme.palette.info.main;
    return theme.palette.success.main;
  };

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            Memory Usage - {service.serviceName}
          </Typography>
          <Box display="flex" gap={1}>
            <Chip
              label={service.status.toUpperCase()}
              color={service.status === 'protected' ? 'success' : 'warning'}
              size="small"
            />
            <Chip
              label={`${service.memoryUsage.percentage}%`}
              sx={{ 
                backgroundColor: getStatusColor(service.memoryUsage.percentage),
                color: 'white'
              }}
              size="small"
            />
          </Box>
        </Box>

        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 1fr 1fr',
            gap: 2,
            mb: 2
          }}
        >
          <Box>
            <Typography variant="body2" color="textSecondary">
              Current
            </Typography>
            <Typography variant="h6">
              {service.memoryUsage.current} MB
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="textSecondary">
              Threshold
            </Typography>
            <Typography variant="h6" color="warning.main">
              {service.memoryUsage.threshold} MB
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="textSecondary">
              Maximum
            </Typography>
            <Typography variant="h6" color="error.main">
              {service.memoryUsage.maximum} MB
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="textSecondary">
              Protection
            </Typography>
            <Typography variant="h6" color="success.main">
              {service.protectionLevel.toUpperCase()}
            </Typography>
          </Box>
        </Box>

        <ResponsiveContainer width="100%" height={height}>
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="memoryGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.8}/>
                <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
            <XAxis 
              dataKey="time" 
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <YAxis 
              stroke={theme.palette.text.secondary}
              fontSize={12}
              label={{ value: 'Memory (MB)', angle: -90, position: 'insideLeft' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* Memory usage area */}
            <Area
              type="monotone"
              dataKey="memory"
              stroke={theme.palette.primary.main}
              fillOpacity={1}
              fill="url(#memoryGradient)"
              name="Memory Usage"
              strokeWidth={2}
            />
            
            {/* CPU usage line */}
            <Line
              type="monotone"
              dataKey="cpu"
              stroke={theme.palette.secondary.main}
              strokeWidth={2}
              dot={false}
              name="CPU Usage (%)"
              yAxisId="right"
            />

            {/* Threshold reference lines */}
            {showThresholds && (
              <>
                <ReferenceLine
                  y={service.memoryUsage.threshold}
                  stroke={theme.palette.warning.main}
                  strokeDasharray="5 5"
                  label="Threshold"
                />
                <ReferenceLine
                  y={service.memoryUsage.maximum}
                  stroke={theme.palette.error.main}
                  strokeDasharray="5 5"
                  label="Maximum"
                />
              </>
            )}
          </AreaChart>
        </ResponsiveContainer>

        <Box mt={2}>
          <Typography variant="body2" color="textSecondary">
            Last updated: {new Date(service.lastMemoryCheck).toLocaleString()}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
}
