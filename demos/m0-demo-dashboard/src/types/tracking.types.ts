/**
 * Tracking Types for M0 Demo Dashboard
 * Purpose: Type definitions for tracking and monitoring data structures
 */

export interface ITrackingService {
  id: string;
  name: string;
  type: 'core' | 'audit' | 'realtime' | 'utils' | 'analytics' | 'session';
  status: 'active' | 'inactive' | 'degraded' | 'maintenance';
  version: string;
  healthScore: number;
  lastHealthCheck: string;
  uptime: number;
  requestCount: number;
  errorCount: number;
  averageResponseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  dependencies: string[];
  isEnhanced: boolean;
}

export interface IImplementationProgress {
  componentId: string;
  componentName: string;
  category: 'governance' | 'tracking' | 'security' | 'integration';
  plannedLOC: number;
  actualLOC: number;
  completionPercentage: number;
  status: 'not-started' | 'in-progress' | 'completed' | 'enhanced';
  lastUpdate: string;
  milestones: {
    design: boolean;
    implementation: boolean;
    testing: boolean;
    documentation: boolean;
    deployment: boolean;
  };
  qualityMetrics: {
    codeQuality: number;
    testCoverage: number;
    documentation: number;
    performance: number;
  };
}

export interface ISessionData {
  sessionId: string;
  userId?: string;
  startTime: string;
  endTime?: string;
  duration: number;
  activityCount: number;
  trackingType: 'core' | 'audit' | 'realtime' | 'utils';
  status: 'active' | 'completed' | 'terminated' | 'error';
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    location?: string;
    deviceType?: string;
  };
  events: ISessionEvent[];
}

export interface ISessionEvent {
  eventId: string;
  sessionId: string;
  timestamp: string;
  eventType: 'page-view' | 'action' | 'error' | 'performance' | 'custom';
  eventName: string;
  eventData: Record<string, unknown>;
  duration?: number;
  success: boolean;
}

export interface IAnalyticsCacheMetrics {
  cacheId: string;
  name: string;
  type: 'memory' | 'redis' | 'database' | 'file';
  status: 'healthy' | 'degraded' | 'offline';
  hitRate: number;
  missRate: number;
  totalRequests: number;
  totalHits: number;
  totalMisses: number;
  averageResponseTime: number;
  memoryUsage: number;
  maxMemoryUsage: number;
  evictionCount: number;
  lastEviction?: string;
  ttl: number;
  keyCount: number;
  maxKeys: number;
}

export interface IComponentStatus {
  componentId: string;
  componentName: string;
  category: 'governance' | 'tracking' | 'security' | 'integration' | 'foundation';
  status: 'operational' | 'degraded' | 'offline' | 'maintenance';
  healthScore: number;
  lastHealthCheck: string;
  uptime: number;
  version: string;
  dependencies: string[];
  dependents: string[];
  metrics: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
    throughput: number;
    latency: number;
  };
  alerts: IComponentAlert[];
}

export interface IComponentAlert {
  alertId: string;
  componentId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'performance' | 'error' | 'availability' | 'security' | 'resource';
  message: string;
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
  resolvedAt?: string;
}

export interface IOrchestrationMetrics {
  coordinatorId: string;
  name: string;
  status: 'active' | 'inactive' | 'error';
  managedComponents: number;
  activeConnections: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageProcessingTime: number;
  queueSize: number;
  maxQueueSize: number;
  lastActivity: string;
  coordinationEvents: ICoordinationEvent[];
}

export interface ICoordinationEvent {
  eventId: string;
  coordinatorId: string;
  timestamp: string;
  eventType: 'component-start' | 'component-stop' | 'health-check' | 'error' | 'coordination';
  componentId: string;
  status: 'success' | 'failure' | 'warning';
  details: string;
  duration: number;
}

// API Response Types
export interface ITrackingComponentsResponse {
  components: ITrackingService[];
  total: number;
  healthy: number;
  degraded: number;
  offline: number;
  lastUpdate: string;
}

export interface IImplementationProgressResponse {
  progress: IImplementationProgress[];
  summary: {
    totalComponents: number;
    completedComponents: number;
    enhancedComponents: number;
    totalLOC: number;
    actualLOC: number;
    overallCompletion: number;
    enhancementRate: number;
  };
  lastUpdate: string;
}

export interface ISessionTrackingResponse {
  sessions: ISessionData[];
  activeSessions: number;
  totalSessions: number;
  averageSessionDuration: number;
  totalEvents: number;
  lastUpdate: string;
}

export interface IAnalyticsPerformanceResponse {
  caches: IAnalyticsCacheMetrics[];
  overallPerformance: {
    averageHitRate: number;
    averageResponseTime: number;
    totalRequests: number;
    totalMemoryUsage: number;
    healthScore: number;
  };
  lastUpdate: string;
}

export interface IComponentStatusResponse {
  components: IComponentStatus[];
  summary: {
    total: number;
    operational: number;
    degraded: number;
    offline: number;
    maintenance: number;
    overallHealth: number;
  };
  alerts: IComponentAlert[];
  lastUpdate: string;
}

// Filter and Search Types
export interface ITrackingFilters {
  status?: 'active' | 'inactive' | 'degraded' | 'maintenance';
  type?: 'core' | 'audit' | 'realtime' | 'utils' | 'analytics' | 'session';
  category?: 'governance' | 'tracking' | 'security' | 'integration' | 'foundation';
  healthScore?: {
    min: number;
    max: number;
  };
  enhanced?: boolean;
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}
