/**
 * Governance Types for M0 Demo Dashboard
 * Purpose: Type definitions for governance-related data structures
 */

export interface IGovernanceRule {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'pending' | 'deprecated';
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: 'validation' | 'compliance' | 'authority' | 'cross-reference';
  authorityLevel: 'E.Z.Consultancy' | 'M0' | 'Operations';
  complianceScore: number;
  lastValidated: string;
  createdAt: string;
  updatedAt: string;
  validationCount: number;
  failureCount: number;
}

export interface IComplianceMetrics {
  overallScore: number;
  totalRules: number;
  activeRules: number;
  passedValidations: number;
  failedValidations: number;
  compliancePercentage: number;
  lastAssessment: string;
  trendDirection: 'up' | 'down' | 'stable';
  categoryScores: {
    validation: number;
    compliance: number;
    authority: number;
    crossReference: number;
  };
}

export interface IAuditEntry {
  id: string;
  timestamp: string;
  action: string;
  component: string;
  authorityLevel: 'E.Z.Consultancy' | 'M0' | 'Operations';
  status: 'success' | 'failure' | 'warning' | 'info';
  details: string;
  userId?: string;
  ruleId?: string;
  impactLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface IAuthorityChain {
  level: 'E.Z.Consultancy' | 'M0' | 'Operations';
  name: string;
  status: 'active' | 'inactive' | 'pending';
  permissions: string[];
  validationRequired: boolean;
  lastValidation: string;
  childLevels: string[];
  parentLevel?: string;
}

export interface IGTSKSystem {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'maintenance';
  version: string;
  lastUpdate: string;
  validationCount: number;
  successRate: number;
  components: string[];
  dependencies: string[];
}

export interface ICrossReference {
  id: string;
  sourceComponent: string;
  targetComponent: string;
  referenceType: 'dependency' | 'validation' | 'integration' | 'inheritance';
  status: 'valid' | 'invalid' | 'pending' | 'deprecated';
  lastValidated: string;
  validationFrequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
  criticalityLevel: 'low' | 'medium' | 'high' | 'critical';
  withinM0Scope: boolean;
}

export interface IGovernanceComponent {
  id: string;
  name: string;
  type: 'rule-engine' | 'validator' | 'authority-manager' | 'cross-reference' | 'audit-system';
  status: 'operational' | 'degraded' | 'offline' | 'maintenance';
  version: string;
  healthScore: number;
  lastHealthCheck: string;
  dependencies: string[];
  metrics: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
  };
}

// API Response Types
export interface IGovernanceRulesResponse {
  rules: IGovernanceRule[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface IComplianceResponse {
  metrics: IComplianceMetrics;
  timestamp: string;
  nextAssessment: string;
}

export interface IAuditTrailResponse {
  entries: IAuditEntry[];
  total: number;
  page: number;
  limit: number;
  filters: {
    startDate?: string;
    endDate?: string;
    authorityLevel?: string;
    status?: string;
    component?: string;
  };
}

export interface IAuthorityChainResponse {
  chain: IAuthorityChain[];
  validationStatus: 'valid' | 'invalid' | 'pending';
  lastValidation: string;
  nextValidation: string;
}

export interface ICrossReferenceResponse {
  references: ICrossReference[];
  total: number;
  validReferences: number;
  invalidReferences: number;
  pendingValidation: number;
  lastFullValidation: string;
}

// Form Types
export interface ICreateGovernanceRuleRequest {
  name: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: 'validation' | 'compliance' | 'authority' | 'cross-reference';
  authorityLevel: 'E.Z.Consultancy' | 'M0' | 'Operations';
}

export interface IUpdateGovernanceRuleRequest extends Partial<ICreateGovernanceRuleRequest> {
  id: string;
  status?: 'active' | 'inactive' | 'pending' | 'deprecated';
}

// Filter and Search Types
export interface IGovernanceFilters {
  status?: 'active' | 'inactive' | 'pending' | 'deprecated';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  category?: 'validation' | 'compliance' | 'authority' | 'cross-reference';
  authorityLevel?: 'E.Z.Consultancy' | 'M0' | 'Operations';
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}
