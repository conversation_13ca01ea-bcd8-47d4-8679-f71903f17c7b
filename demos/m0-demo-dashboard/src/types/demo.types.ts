/**
 * Demo-Specific Types for M0 Demo Dashboard
 * Purpose: Type definitions for demo application functionality and integration
 */

export interface IM0ComponentStatus {
  componentId: string;
  componentName: string;
  category: 'governance' | 'tracking' | 'security' | 'integration' | 'foundation';
  type: 'service' | 'utility' | 'manager' | 'coordinator' | 'validator';
  status: 'operational' | 'degraded' | 'offline' | 'maintenance';
  healthScore: number;
  version: string;
  lastHealthCheck: string;
  uptime: number;
  dependencies: string[];
  dependents: string[];
  isEnhanced: boolean;
  withinM0Scope: boolean;
  metrics: {
    requestsPerSecond: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}

export interface IHealthCheck {
  checkId: string;
  componentId: string;
  timestamp: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  responseTime: number;
  checks: {
    connectivity: boolean;
    memory: boolean;
    cpu: boolean;
    dependencies: boolean;
    functionality: boolean;
  };
  details: string;
  recommendations: string[];
}

export interface IM0FoundationCapability {
  capabilityId: string;
  name: string;
  description: string;
  category: 'governance' | 'tracking' | 'security' | 'integration' | 'extension';
  status: 'available' | 'partial' | 'unavailable' | 'deprecated';
  readinessLevel: 'M1' | 'M2' | 'M3+' | 'universal';
  interfaces: string[];
  extensionPoints: string[];
  dependencies: string[];
  documentation: {
    apiDocs: string;
    examples: string;
    tutorials: string;
  };
  compatibility: {
    backward: boolean;
    forward: boolean;
    crossPlatform: boolean;
  };
}

export interface IIntegrationTest {
  testId: string;
  name: string;
  type: 'unit' | 'integration' | 'system' | 'acceptance' | 'performance';
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
  category: 'governance-tracking' | 'security-governance' | 'tracking-security' | 'full-system';
  components: string[];
  startTime: string;
  endTime?: string;
  duration: number;
  results: {
    passed: number;
    failed: number;
    skipped: number;
    total: number;
    coverage: number;
  };
  logs: ITestLog[];
  assertions: ITestAssertion[];
}

export interface ITestLog {
  timestamp: string;
  level: 'debug' | 'info' | 'warning' | 'error';
  message: string;
  component?: string;
  testCase?: string;
  stackTrace?: string;
}

export interface ITestAssertion {
  assertionId: string;
  testId: string;
  description: string;
  expected: unknown;
  actual: unknown;
  status: 'passed' | 'failed';
  timestamp: string;
  duration: number;
}

export interface IDemoConfiguration {
  demoMode: boolean;
  realTimeUpdates: boolean;
  updateIntervals: {
    memory: number;
    governance: number;
    tracking: number;
    integration: number;
  };
  simulationSettings: {
    enableAttackSimulation: boolean;
    enableComponentFailure: boolean;
    enablePerformanceTesting: boolean;
    maxSimulationDuration: number;
  };
  displaySettings: {
    showEnhancedFeatures: boolean;
    showTechnicalDetails: boolean;
    showDebugInfo: boolean;
    animationsEnabled: boolean;
  };
  dataGeneration: {
    useRealisticData: boolean;
    dataVariation: 'low' | 'medium' | 'high';
    includeAnomalies: boolean;
    historicalDataDays: number;
  };
}

export interface IDashboardMetrics {
  dashboardId: string;
  name: string;
  category: 'security' | 'governance' | 'tracking' | 'integration' | 'foundation';
  metrics: {
    totalComponents: number;
    healthyComponents: number;
    degradedComponents: number;
    offlineComponents: number;
    averageResponseTime: number;
    errorRate: number;
    uptime: number;
  };
  lastUpdate: string;
  alerts: number;
  warnings: number;
  criticalIssues: number;
}

export interface ISimulationControl {
  controlId: string;
  name: string;
  type: 'attack' | 'failure' | 'performance' | 'load' | 'chaos';
  status: 'idle' | 'running' | 'completed' | 'failed';
  targetComponents: string[];
  parameters: Record<string, unknown>;
  results?: Record<string, unknown>;
  startTime?: string;
  endTime?: string;
  duration?: number;
}

export interface IEventCorrelation {
  correlationId: string;
  timestamp: string;
  sourceSystem: 'governance' | 'tracking' | 'security' | 'integration';
  targetSystem: 'governance' | 'tracking' | 'security' | 'integration';
  eventType: 'validation' | 'monitoring' | 'alert' | 'update' | 'sync';
  status: 'success' | 'failure' | 'warning' | 'info';
  message: string;
  details: Record<string, unknown>;
  relatedEvents: string[];
  withinM0Scope: boolean;
}

// API Response Types
export interface IM0ComponentStatusResponse {
  components: IM0ComponentStatus[];
  summary: {
    total: number;
    operational: number;
    degraded: number;
    offline: number;
    maintenance: number;
    enhanced: number;
    withinM0Scope: number;
  };
  lastUpdate: string;
}

export interface IHealthCheckResponse {
  checks: IHealthCheck[];
  overallHealth: {
    score: number;
    status: 'healthy' | 'warning' | 'critical';
    totalChecks: number;
    healthyChecks: number;
    warningChecks: number;
    criticalChecks: number;
  };
  lastUpdate: string;
}

export interface IM0FoundationStatusResponse {
  capabilities: IM0FoundationCapability[];
  readiness: {
    M1: number;
    M2: number;
    M3Plus: number;
    universal: number;
  };
  extensionPoints: number;
  interfaces: number;
  lastUpdate: string;
}

export interface IIntegrationTestResponse {
  tests: IIntegrationTest[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    running: number;
    pending: number;
    overallCoverage: number;
  };
  lastRun: string;
  nextScheduledRun: string;
}

export interface IEventCorrelationResponse {
  events: IEventCorrelation[];
  correlations: {
    governanceTracking: number;
    securityGovernance: number;
    trackingSecurity: number;
    fullSystem: number;
  };
  lastUpdate: string;
}

// Utility Types
export type DashboardType = 'security' | 'governance' | 'tracking' | 'integration' | 'foundation';
export type ComponentCategory = 'governance' | 'tracking' | 'security' | 'integration' | 'foundation';
export type HealthStatus = 'healthy' | 'warning' | 'critical' | 'unknown';
export type OperationalStatus = 'operational' | 'degraded' | 'offline' | 'maintenance';
export type TestStatus = 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
export type SimulationStatus = 'idle' | 'running' | 'completed' | 'failed';

// Demo State Management Types
export interface IDemoState {
  currentDashboard: DashboardType;
  realTimeUpdates: boolean;
  simulationsActive: ISimulationControl[];
  lastUpdate: string;
  configuration: IDemoConfiguration;
  alerts: number;
  warnings: number;
  criticalIssues: number;
}
