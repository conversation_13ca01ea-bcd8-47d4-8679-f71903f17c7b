/**
 * Integration Types for M0 Demo Dashboard
 * Purpose: Type definitions for integration testing and system health monitoring
 */

// Integration Test Result Types
export interface IIntegrationTestResult {
  testId: string;
  testName: string;
  testSuite: string;
  status: 'passed' | 'failed' | 'running' | 'pending' | 'skipped';
  startTime: string;
  endTime?: string;
  duration: number;
  description: string;
  category: 'unit' | 'integration' | 'e2e' | 'performance' | 'security';
  priority: 'low' | 'medium' | 'high' | 'critical';
  components: string[];
  assertions: ITestAssertion[];
  logs: ITestLog[];
  metrics: ITestMetrics;
  error?: ITestError;
}

export interface ITestAssertion {
  assertionId: string;
  description: string;
  expected: unknown;
  actual: unknown;
  passed: boolean;
  message?: string;
}

export interface ITestLog {
  logId: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  component?: string;
  metadata?: Record<string, unknown>;
}

export interface ITestMetrics {
  executionTime: number;
  memoryUsage: number;
  cpuUsage: number;
  networkRequests: number;
  databaseQueries: number;
  cacheHits: number;
  cacheMisses: number;
}

export interface ITestError {
  errorId: string;
  type: string;
  message: string;
  stack?: string;
  component?: string;
  timestamp: string;
}

// Test Suite Configuration Types
export interface ITestSuite {
  suiteId: string;
  suiteName: string;
  description: string;
  category: 'governance' | 'tracking' | 'security' | 'integration' | 'foundation';
  status: 'active' | 'inactive' | 'maintenance' | 'deprecated';
  version: string;
  configuration: ITestSuiteConfig;
  tests: ITestConfiguration[];
  dependencies: string[];
  schedule: ITestSchedule;
  lastRun?: string;
  nextRun?: string;
  statistics: ITestSuiteStatistics;
}

export interface ITestSuiteConfig {
  timeout: number;
  retries: number;
  parallel: boolean;
  maxConcurrency: number;
  environment: 'development' | 'staging' | 'production';
  tags: string[];
  setup?: string[];
  teardown?: string[];
}

export interface ITestConfiguration {
  testId: string;
  name: string;
  enabled: boolean;
  timeout: number;
  retries: number;
  tags: string[];
  parameters: Record<string, unknown>;
}

export interface ITestSchedule {
  enabled: boolean;
  cron?: string;
  interval?: number;
  triggers: ('manual' | 'scheduled' | 'webhook' | 'deployment')[];
}

export interface ITestSuiteStatistics {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  skippedTests: number;
  averageExecutionTime: number;
  successRate: number;
  lastSuccessfulRun?: string;
  consecutiveFailures: number;
}

// System Health Types
export interface ISystemHealthStatus {
  systemId: string;
  systemName: string;
  category: 'governance' | 'tracking' | 'security' | 'integration' | 'foundation';
  overallHealth: number;
  status: 'healthy' | 'degraded' | 'critical' | 'offline';
  lastCheck: string;
  components: IHealthComponent[];
  dependencies: IHealthDependency[];
  metrics: ISystemMetrics;
  alerts: IHealthAlert[];
  uptime: number;
  version: string;
}

export interface IHealthComponent {
  componentId: string;
  componentName: string;
  type: 'service' | 'database' | 'cache' | 'queue' | 'external';
  health: number;
  status: 'healthy' | 'degraded' | 'critical' | 'offline';
  responseTime: number;
  errorRate: number;
  lastCheck: string;
  checks: IHealthCheck[];
}

export interface IHealthCheck {
  checkId: string;
  checkName: string;
  type: 'ping' | 'query' | 'api' | 'memory' | 'disk' | 'custom';
  status: 'passed' | 'failed' | 'warning';
  value: number;
  threshold: number;
  unit: string;
  message?: string;
  timestamp: string;
}

export interface IHealthDependency {
  dependencyId: string;
  dependencyName: string;
  type: 'internal' | 'external';
  status: 'available' | 'unavailable' | 'degraded';
  responseTime: number;
  lastCheck: string;
  critical: boolean;
}

export interface ISystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  requests: number;
  errors: number;
  latency: number;
  throughput: number;
}

export interface IHealthAlert {
  alertId: string;
  severity: 'info' | 'warning' | 'critical';
  title: string;
  message: string;
  component: string;
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
}

// API Response Types
export interface IIntegrationTestResultsResponse {
  results: IIntegrationTestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    running: number;
    pending: number;
    skipped: number;
    successRate: number;
    averageExecutionTime: number;
  };
  filters: {
    suites: string[];
    categories: string[];
    statuses: string[];
    priorities: string[];
  };
  lastUpdate: string;
}

export interface ITestSuitesResponse {
  suites: ITestSuite[];
  summary: {
    total: number;
    active: number;
    inactive: number;
    maintenance: number;
    totalTests: number;
    averageSuccessRate: number;
  };
  lastUpdate: string;
}

export interface ISystemHealthResponse {
  systems: ISystemHealthStatus[];
  summary: {
    total: number;
    healthy: number;
    degraded: number;
    critical: number;
    offline: number;
    overallHealth: number;
    totalAlerts: number;
    criticalAlerts: number;
  };
  correlations: ISystemCorrelation[];
  lastUpdate: string;
}

export interface ISystemCorrelation {
  correlationId: string;
  systems: string[];
  type: 'dependency' | 'performance' | 'error' | 'resource';
  strength: number;
  description: string;
  impact: 'low' | 'medium' | 'high';
  timestamp: string;
}

// Filter Types
export interface IIntegrationFilters {
  testSuites?: string[];
  categories?: string[];
  statuses?: string[];
  priorities?: string[];
  components?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}
