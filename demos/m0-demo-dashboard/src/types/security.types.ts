/**
 * Security Types for M0 Demo Dashboard
 * Purpose: Type definitions for security and memory safety data structures
 */

export interface IMemorySafetyMetrics {
  serviceId: string;
  serviceName: string;
  type: 'BaseTrackingService' | 'MemorySafeResourceManager' | 'Enhanced' | 'Utility';
  status: 'protected' | 'vulnerable' | 'monitoring' | 'offline';
  memoryUsage: {
    current: number;
    maximum: number;
    threshold: number;
    percentage: number;
  };
  boundaryEnforcement: {
    enabled: boolean;
    boundaryCount: number;
    violationCount: number;
    lastViolation?: string;
  };
  protectionLevel: 'basic' | 'enhanced' | 'enterprise' | 'maximum';
  lastMemoryCheck: string;
  memoryHistory: IMemoryDataPoint[];
  alerts: ISecurityAlert[];
}

export interface IMemoryDataPoint {
  timestamp: string;
  memoryUsage: number;
  cpuUsage: number;
  activeConnections: number;
  requestCount: number;
  errorCount: number;
}

export interface ISystemSecurity {
  overallStatus: 'secure' | 'warning' | 'critical' | 'compromised';
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  protectedServices: number;
  vulnerableServices: number;
  totalServices: number;
  lastSecurityScan: string;
  nextSecurityScan: string;
  securityScore: number;
  complianceStatus: {
    owasp: boolean;
    iso27001: boolean;
    gdpr: boolean;
    custom: boolean;
  };
  activeThreats: IThreatDetection[];
  mitigatedThreats: number;
  totalThreats: number;
}

export interface IThreatDetection {
  threatId: string;
  type: 'memory-exhaustion' | 'buffer-overflow' | 'injection' | 'dos' | 'unauthorized-access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'mitigating' | 'mitigated' | 'false-positive';
  targetService: string;
  detectionTime: string;
  mitigationTime?: string;
  description: string;
  attackVector: string;
  sourceIP?: string;
  userAgent?: string;
  mitigationStrategy: string;
  impactAssessment: {
    confidentiality: 'none' | 'low' | 'medium' | 'high';
    integrity: 'none' | 'low' | 'medium' | 'high';
    availability: 'none' | 'low' | 'medium' | 'high';
  };
}

export interface IAttackSimulation {
  simulationId: string;
  name: string;
  type: 'memory-exhaustion' | 'buffer-overflow' | 'stress-test' | 'penetration-test';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  targetServices: string[];
  startTime: string;
  endTime?: string;
  duration: number;
  parameters: {
    intensity: 'low' | 'medium' | 'high' | 'maximum';
    duration: number;
    targetMemory: number;
    concurrentRequests: number;
    payloadSize: number;
  };
  results: {
    successful: boolean;
    protectionTriggered: boolean;
    mitigationTime: number;
    impactLevel: 'none' | 'minimal' | 'moderate' | 'severe';
    servicesAffected: number;
    memoryPeakUsage: number;
    responseTimeImpact: number;
  };
  logs: ISimulationLog[];
}

export interface ISimulationLog {
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  serviceId?: string;
  memoryUsage?: number;
  responseTime?: number;
  errorCode?: string;
}

export interface ISecurityAlert {
  alertId: string;
  serviceId: string;
  type: 'memory-threshold' | 'boundary-violation' | 'attack-detected' | 'service-degraded' | 'protection-disabled';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'acknowledged' | 'resolved' | 'false-positive';
  timestamp: string;
  message: string;
  details: string;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
  actions: string[];
  relatedAlerts: string[];
}

export interface IMemoryBoundaryConfig {
  serviceId: string;
  serviceName: string;
  boundaries: {
    maxMemoryMB: number;
    warningThresholdMB: number;
    criticalThresholdMB: number;
    maxConnections: number;
    maxRequestsPerSecond: number;
    timeoutSeconds: number;
  };
  enforcement: {
    enabled: boolean;
    strictMode: boolean;
    autoCleanup: boolean;
    emergencyShutdown: boolean;
  };
  monitoring: {
    checkIntervalSeconds: number;
    historyRetentionHours: number;
    alertingEnabled: boolean;
    reportingEnabled: boolean;
  };
  lastUpdated: string;
  updatedBy: string;
}

export interface IBaseTrackingServiceStatus {
  serviceId: string;
  serviceName: string;
  inheritanceLevel: 'BaseTrackingService' | 'MemorySafeResourceManager' | 'Enhanced';
  protectionFeatures: {
    memoryProtection: boolean;
    resourceManagement: boolean;
    automaticCleanup: boolean;
    boundaryEnforcement: boolean;
    attackPrevention: boolean;
  };
  status: 'active' | 'inactive' | 'degraded' | 'error';
  lastHealthCheck: string;
  protectionMetrics: {
    memoryLeaksPrevented: number;
    attacksBlocked: number;
    resourcesManaged: number;
    cleanupOperations: number;
    boundaryViolationsPrevented: number;
  };
  configuration: {
    maxMemoryMB: number;
    cleanupIntervalSeconds: number;
    monitoringEnabled: boolean;
    alertingEnabled: boolean;
  };
}

// API Response Types
export interface IMemoryUsageResponse {
  services: IMemorySafetyMetrics[];
  summary: {
    totalServices: number;
    protectedServices: number;
    vulnerableServices: number;
    averageMemoryUsage: number;
    totalMemoryUsage: number;
    totalBoundaries: number;
    totalViolations: number;
  };
  lastUpdate: string;
}

export interface IAttackSimulationResponse {
  simulations: IAttackSimulation[];
  activeSimulations: number;
  completedSimulations: number;
  successfulAttacks: number;
  blockedAttacks: number;
  lastSimulation?: string;
}

export interface IProtectionStatusResponse {
  services: IBaseTrackingServiceStatus[];
  overallProtection: {
    protectedServices: number;
    totalServices: number;
    protectionCoverage: number;
    averageHealthScore: number;
  };
  lastUpdate: string;
}

export interface IBoundaryEnforcementResponse {
  configurations: IMemoryBoundaryConfig[];
  enforcement: {
    totalBoundaries: number;
    activeBoundaries: number;
    violations: number;
    lastViolation?: string;
  };
  lastUpdate: string;
}

// Filter and Search Types
export interface ISecurityFilters {
  status?: 'protected' | 'vulnerable' | 'monitoring' | 'offline';
  type?: 'BaseTrackingService' | 'MemorySafeResourceManager' | 'Enhanced' | 'Utility';
  protectionLevel?: 'basic' | 'enhanced' | 'enterprise' | 'maximum';
  threatLevel?: 'low' | 'medium' | 'high' | 'critical';
  memoryUsage?: {
    min: number;
    max: number;
  };
  search?: string;
  dateRange?: {
    start: string;
    end: string;
  };
}
