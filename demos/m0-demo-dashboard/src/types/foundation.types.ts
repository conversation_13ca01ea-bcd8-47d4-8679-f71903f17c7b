/**
 * Foundation Types for M0 Demo Dashboard
 * Purpose: Type definitions for foundation infrastructure, dependencies, and architecture monitoring
 */

// Foundation Infrastructure Types
export interface IFoundationInfrastructure {
  infrastructureId: string;
  infrastructureName: string;
  category: 'core' | 'database' | 'cache' | 'messaging' | 'storage' | 'network';
  status: 'operational' | 'degraded' | 'maintenance' | 'offline';
  health: number;
  uptime: number;
  version: string;
  lastCheck: string;
  metrics: IInfrastructureMetrics;
  configuration: IInfrastructureConfig;
  alerts: IInfrastructureAlert[];
  dependencies: string[];
}

export interface IInfrastructureMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  connections: number;
  throughput: number;
  latency: number;
  errorRate: number;
  availability: number;
}

export interface IInfrastructureConfig {
  maxConnections: number;
  memoryLimit: number;
  diskQuota: number;
  timeout: number;
  retries: number;
  backupEnabled: boolean;
  monitoringEnabled: boolean;
  autoScaling: boolean;
}

export interface IInfrastructureAlert {
  alertId: string;
  severity: 'info' | 'warning' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  acknowledged: boolean;
  resolved: boolean;
  component: string;
}

// System Dependencies Types
export interface ISystemDependency {
  dependencyId: string;
  dependencyName: string;
  type: 'internal' | 'external' | 'third-party';
  category: 'database' | 'api' | 'service' | 'library' | 'infrastructure';
  status: 'available' | 'unavailable' | 'degraded' | 'unknown';
  health: number;
  version: string;
  critical: boolean;
  responseTime: number;
  lastCheck: string;
  checks: IDependencyCheck[];
  metadata: IDependencyMetadata;
  relationships: IDependencyRelationship[];
}

export interface IDependencyCheck {
  checkId: string;
  checkName: string;
  type: 'ping' | 'health' | 'version' | 'connectivity' | 'authentication';
  status: 'passed' | 'failed' | 'warning' | 'skipped';
  value: number | string;
  threshold?: number | string;
  unit?: string;
  message?: string;
  timestamp: string;
  duration: number;
}

export interface IDependencyMetadata {
  description: string;
  documentation: string;
  maintainer: string;
  supportLevel: 'full' | 'limited' | 'community' | 'deprecated';
  licenseType: string;
  securityRating: 'high' | 'medium' | 'low' | 'unknown';
  updateFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

export interface IDependencyRelationship {
  relationshipId: string;
  relatedDependency: string;
  relationshipType: 'depends-on' | 'provides-to' | 'integrates-with' | 'replaces';
  strength: number;
  bidirectional: boolean;
  description: string;
}

// Architecture Overview Types
export interface IArchitectureComponent {
  componentId: string;
  componentName: string;
  type: 'service' | 'module' | 'library' | 'interface' | 'database' | 'queue';
  layer: 'presentation' | 'business' | 'data' | 'infrastructure' | 'integration';
  status: 'active' | 'inactive' | 'deprecated' | 'planned';
  health: number;
  complexity: number;
  maintainability: number;
  testCoverage: number;
  documentation: number;
  version: string;
  lastUpdate: string;
  metrics: IComponentMetrics;
  connections: IComponentConnection[];
  responsibilities: string[];
}

export interface IComponentMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  technicalDebt: number;
  bugCount: number;
  vulnerabilities: number;
  performanceScore: number;
  reliabilityScore: number;
  securityScore: number;
}

export interface IComponentConnection {
  connectionId: string;
  targetComponent: string;
  connectionType: 'calls' | 'imports' | 'extends' | 'implements' | 'uses' | 'provides';
  strength: number;
  frequency: number;
  latency?: number;
  errorRate?: number;
  description: string;
}

export interface IArchitecturePattern {
  patternId: string;
  patternName: string;
  type: 'design' | 'architectural' | 'integration' | 'security' | 'performance';
  description: string;
  components: string[];
  benefits: string[];
  tradeoffs: string[];
  compliance: number;
  usage: number;
}

// API Response Types
export interface IFoundationInfrastructureResponse {
  infrastructure: IFoundationInfrastructure[];
  summary: {
    total: number;
    operational: number;
    degraded: number;
    maintenance: number;
    offline: number;
    overallHealth: number;
    totalAlerts: number;
    criticalAlerts: number;
    averageUptime: number;
  };
  categories: {
    [key: string]: {
      count: number;
      health: number;
      status: string;
    };
  };
  lastUpdate: string;
}

export interface ISystemDependenciesResponse {
  dependencies: ISystemDependency[];
  summary: {
    total: number;
    available: number;
    unavailable: number;
    degraded: number;
    unknown: number;
    critical: number;
    overallHealth: number;
    averageResponseTime: number;
  };
  categories: {
    [key: string]: {
      count: number;
      health: number;
      availability: number;
    };
  };
  relationships: IDependencyRelationship[];
  lastUpdate: string;
}

export interface IArchitectureOverviewResponse {
  components: IArchitectureComponent[];
  patterns: IArchitecturePattern[];
  summary: {
    totalComponents: number;
    activeComponents: number;
    inactiveComponents: number;
    deprecatedComponents: number;
    averageHealth: number;
    averageComplexity: number;
    averageMaintainability: number;
    averageTestCoverage: number;
    totalConnections: number;
  };
  layers: {
    [key: string]: {
      componentCount: number;
      health: number;
      complexity: number;
    };
  };
  metrics: {
    totalLinesOfCode: number;
    averageCyclomaticComplexity: number;
    totalTechnicalDebt: number;
    totalBugs: number;
    totalVulnerabilities: number;
    overallPerformanceScore: number;
    overallReliabilityScore: number;
    overallSecurityScore: number;
  };
  lastUpdate: string;
}

// Filter Types
export interface IFoundationFilters {
  categories?: string[];
  statuses?: string[];
  types?: string[];
  layers?: string[];
  healthRange?: {
    min: number;
    max: number;
  };
  complexityRange?: {
    min: number;
    max: number;
  };
  dateRange?: {
    start: string;
    end: string;
  };
}
