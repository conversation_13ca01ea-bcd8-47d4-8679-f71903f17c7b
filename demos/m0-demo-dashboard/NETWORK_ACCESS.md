# M0 Demo Dashboard - Network Access Configuration

## 🌐 Network Access Overview

The M0 Demo Dashboard is now configured for **Local Area Network (LAN) access**, enabling collaborative development and demonstration across multiple workstations on the same network.

## 🚀 Quick Start

### For Development Team Members

**Primary Network URL**: `http://***********:3000`

**Available Dashboard Sections**:
- **Overview**: `http://***********:3000/`
- **Security Dashboard**: `http://***********:3000/security`
- **Governance Panel**: `http://***********:3000/governance`
- **Tracking Monitor**: `http://***********:3000/tracking`
- **Integration Console**: `http://***********:3000/integration`
- **Foundation Status**: `http://***********:3000/foundation`

## 🛠️ Server Configuration

### Starting the Network-Accessible Server

```bash
# Option 1: LAN-optimized development server
npm run dev:lan

# Option 2: Network development server (explicit port)
npm run dev:network

# Option 3: Production server with network access
npm run start:lan
```

### Server Binding Details

- **Hostname**: `0.0.0.0` (binds to all network interfaces)
- **Port**: `3000`
- **Local Access**: `http://localhost:3000`
- **Network Access**: `http://***********:3000`

## 📡 API Endpoints (Network Accessible)

All API endpoints are accessible over the network with CORS enabled:

### Security APIs
- **Memory Usage**: `http://***********:3000/api/security/memory-usage`
- **Protection Status**: `http://***********:3000/api/security/protection-status`
- **Boundary Enforcement**: `http://***********:3000/api/security/boundary-enforcement`
- **Attack Simulation**: `http://***********:3000/api/security/attack-simulation` (POST)

### Other APIs
- **Governance**: `http://***********:3000/api/governance/*`
- **Tracking**: `http://***********:3000/api/tracking/*`
- **Integration**: `http://***********:3000/api/integration/*`

## 🔧 Network Configuration Details

### Next.js Configuration (`next.config.ts`)

```typescript
// Network access configuration for LAN development
async headers() {
  return [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'Access-Control-Allow-Origin',
          value: '*',
        },
        {
          key: 'Access-Control-Allow-Methods',
          value: 'GET, POST, PUT, DELETE, OPTIONS',
        },
        {
          key: 'Access-Control-Allow-Headers',
          value: 'Content-Type, Authorization',
        },
      ],
    },
  ];
}
```

### Package.json Scripts

```json
{
  "scripts": {
    "dev": "next dev --turbopack",
    "dev:lan": "next dev --turbopack --hostname 0.0.0.0",
    "dev:network": "next dev --turbopack --hostname 0.0.0.0 --port 3000",
    "start:lan": "next start --hostname 0.0.0.0"
  }
}
```

## 🔒 Firewall Configuration

### Linux (Ubuntu/Debian)
```bash
# Allow port 3000 through UFW firewall
sudo ufw allow 3000/tcp

# Check firewall status
sudo ufw status
```

### Windows
```powershell
# Allow port 3000 through Windows Firewall
New-NetFirewallRule -DisplayName "M0 Dashboard" -Direction Inbound -Port 3000 -Protocol TCP -Action Allow
```

### macOS
```bash
# Add firewall rule (if firewall is enabled)
sudo pfctl -f /etc/pf.conf
```

## 🧪 Testing Network Access

### From Command Line
```bash
# Test main dashboard
curl -I http://***********:3000

# Test security dashboard
curl -I http://***********:3000/security

# Test API endpoint
curl http://***********:3000/api/security/memory-usage
```

### From Browser
1. Open browser on any device on the same network
2. Navigate to `http://***********:3000`
3. Verify all dashboard sections load correctly
4. Test real-time data updates (should refresh every 5 seconds)
5. Test interactive features (attack simulation, navigation)

## 📱 Device Compatibility

### Tested Devices
- ✅ **Desktop Browsers**: Chrome, Firefox, Safari, Edge
- ✅ **Mobile Browsers**: iOS Safari, Android Chrome
- ✅ **Tablet Browsers**: iPad Safari, Android Chrome
- ✅ **Development Tools**: Postman, curl, wget

### Responsive Design
- **Desktop**: Full dashboard with all features
- **Tablet**: Responsive layout with touch-friendly controls
- **Mobile**: Optimized mobile view with collapsible navigation

## 🚨 Security Considerations

### Development Environment Only
⚠️ **Important**: This configuration is intended for **development and demonstration** purposes only.

### Security Measures
- **CORS Enabled**: Allows cross-origin requests for development
- **No Authentication**: Open access for team collaboration
- **Local Network Only**: Not exposed to the internet
- **Firewall Protected**: Only accessible within LAN

### Production Deployment
For production deployment, additional security measures required:
- Authentication and authorization
- HTTPS/TLS encryption
- Restricted CORS policies
- Rate limiting
- Security headers

## 🔍 Troubleshooting

### Common Issues

#### Cannot Access from Other Devices
1. **Check Network Connection**: Ensure devices are on same network
2. **Verify IP Address**: Confirm host IP with `hostname -I`
3. **Test Firewall**: Temporarily disable firewall to test
4. **Check Server Binding**: Ensure server started with `--hostname 0.0.0.0`

#### API Endpoints Not Working
1. **CORS Issues**: Check browser console for CORS errors
2. **Network Latency**: API responses may take 2-4 seconds (demo delay)
3. **Server Logs**: Check terminal for API request logs

#### Real-time Updates Not Working
1. **Network Stability**: Ensure stable network connection
2. **Browser Cache**: Clear browser cache and reload
3. **WebSocket Issues**: Check browser developer tools network tab

### Debug Commands
```bash
# Check server is listening on all interfaces
netstat -tlnp | grep :3000

# Test network connectivity
ping ***********

# Check firewall rules
sudo ufw status verbose

# Monitor server logs
npm run dev:lan | grep -E "(GET|POST|ERROR)"
```

## 📊 Performance Considerations

### Network Optimization
- **Compression Enabled**: Gzip compression for faster loading
- **Image Optimization**: WebP and AVIF formats supported
- **Bundle Optimization**: Material-UI package imports optimized
- **Caching**: Browser caching enabled for static assets

### Expected Performance
- **Initial Load**: 2-5 seconds (depending on network speed)
- **API Responses**: 2-4 seconds (includes demo simulation delay)
- **Real-time Updates**: 5-second intervals
- **Navigation**: <1 second between pages

## 📞 Support

### For Technical Issues
1. Check this documentation first
2. Review server logs in terminal
3. Test with curl commands provided above
4. Contact development team with specific error messages

### For Feature Requests
- Security dashboard enhancements
- Additional API endpoints
- Mobile optimization improvements
- Performance optimizations

## 🛠️ Quick Setup Script

For automated firewall configuration, use the provided script:

```bash
# Make script executable
chmod +x scripts/configure-firewall.sh

# Test current connectivity
./scripts/configure-firewall.sh test

# Enable firewall access (if needed)
./scripts/configure-firewall.sh enable

# Check firewall status
./scripts/configure-firewall.sh status
```

## ✅ Verification Checklist

### Server Configuration ✅
- [x] Next.js configured with `--hostname 0.0.0.0`
- [x] CORS headers enabled for cross-origin requests
- [x] Network scripts added to package.json
- [x] Server binding to all network interfaces

### Network Access ✅
- [x] Dashboard accessible at `http://***********:3000`
- [x] All API endpoints responding over network
- [x] Real-time updates working (5-second intervals)
- [x] Attack simulation functional over network

### Security Dashboard ✅
- [x] Memory usage charts loading
- [x] Component health grid displaying 22+ services
- [x] Interactive features working (tabs, buttons)
- [x] Attack simulation controls functional

### Cross-Device Compatibility ✅
- [x] Desktop browsers (Chrome, Firefox, Safari, Edge)
- [x] Mobile browsers (iOS Safari, Android Chrome)
- [x] Tablet browsers (iPad Safari, Android Chrome)
- [x] API testing tools (curl, Postman)

---

**Last Updated**: 2025-09-03
**Version**: M0 Demo Dashboard v0.1.0
**Network Configuration**: LAN Access Enabled
**Host IP**: ***********:3000
**Status**: ✅ Fully Operational for Team Collaboration
