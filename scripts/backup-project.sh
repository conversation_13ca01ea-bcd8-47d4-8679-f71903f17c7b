#!/bin/bash

# Backup script for Open Architecture Framework project
# Excludes temporary, generated, and environment-specific files

# Set colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to handle errors
handle_error() {
    print_message "$RED" "Error: $1"
    exit 1
}

# Set backup directory and timestamp
BACKUP_DIR="../backups/oa-prod-backup"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="oa-prod_${TIMESTAMP}"

# Check if directory parameter is provided
if [ $# -eq 1 ]; then
    SOURCE_DIR="$1"
    if [ ! -d "$SOURCE_DIR" ]; then
        handle_error "Directory '$SOURCE_DIR' does not exist"
    fi
    # Update backup name to include directory name
    DIR_NAME=$(basename "$SOURCE_DIR")
    BACKUP_NAME="oa-prod_${DIR_NAME}_${TIMESTAMP}"
else
    SOURCE_DIR="."
fi

# Create backup directory if it doesn't exist
print_message "$YELLOW" "Creating backup directory if it doesn't exist..."
mkdir -p "${BACKUP_DIR}" || handle_error "Failed to create backup directory"

# Create temporary exclude file
EXCLUDE_FILE=$(mktemp) || handle_error "Failed to create temporary exclude file"
cat > "${EXCLUDE_FILE}" << 'EOF'
# Temporary and Generated Files
temp/
./dist/**
./dist/
./build/**
./.cache/
./coverage/
coverage/**
coverage/
./.jest-cache/**
./.jest-cache/
./.my-docs/**
./.my-docs/
./ref/**
./ref/
./docs/**
./docs/
./demos/m0-demo-dashboard/.next/**
./demos/m0-demo-dashboard/.next/
./demos/m0-demo-dashboard/.node_modules/**
./demos/m0-demo-dashboard/.node_modules/
./next/**
./next/
./backup/**
./backup/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*
**/.DS_Store
.next/
out/
.nuxt/
.output/
.vercel/
.serverless/

# Node.js dependencies
**/node_modules/
node_modules/
.npm/
.yarn/
.pnp.*
.yarn-integrity

# Environment and config files
.env*
!.env.example
!.env.template
.eslintcache
**/.tsbuildinfo
**/*.tsbuildinfo

# Development Environment Files
.vscode/
.idea/

# Generated Documentation and References
docs/
.my-docs/
ref/
backup/
backups/

# Memory Bank (if present)
docs/memory_bank/*.md
docs/memory_bank/.mb-config.json
docs/memory_bank/.backups/
!docs/memory_bank/README.md
!docs/memory_bank/MEMORY_BANK_SYSTEM.md
!docs/memory_bank/.mb-rules
EOF

# Create backup using tar with exclude file
print_message "$YELLOW" "Creating backup of Open Architecture Framework project..."
print_message "$YELLOW" "Source directory: $SOURCE_DIR"

if ! tar --exclude-from="${EXCLUDE_FILE}" \
    --exclude="node_modules" \
    --exclude="*/node_modules" \
    --exclude="**/node_modules" \
    --exclude="server/node_modules" \
    --exclude="client/node_modules" \
    --exclude="shared/node_modules" \
    --exclude="dist" \
    --exclude="*/dist" \
    --exclude="**/dist" \
    --exclude="coverage" \
    --exclude="*/coverage" \
    --exclude="**/coverage" \
    --exclude=".jest-cache" \
    --exclude="*/.jest-cache" \
    --exclude="**/.jest-cache" \
    --exclude=".my-docs" \
    --exclude="*/.my-docs" \
    --exclude="**/.my-docs" \
    --exclude="ref" \
    --exclude="*/ref" \
    --exclude="**/ref" \
    --exclude="docs" \
    --exclude="*/docs" \
    --exclude="**/docs" \
    --exclude="backup" \
    --exclude="*/backup" \
    --exclude="**/backup" \
    --exclude="backups" \
    --exclude="*/backups" \
    --exclude="**/backups" \
    -czf "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" \
    -C "$SOURCE_DIR" .; then
    rm "${EXCLUDE_FILE}"
    handle_error "Failed to create backup archive"
fi

# Clean up temporary exclude file
rm "${EXCLUDE_FILE}"

# Verify backup
if [ -f "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" ]; then
    BACKUP_SIZE=$(du -h "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" | cut -f1)
    print_message "$GREEN" "Backup completed successfully!"
    print_message "$GREEN" "Backup file: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    print_message "$GREEN" "Size: ${BACKUP_SIZE}"
else
    handle_error "Backup file was not created"
fi

exit 0 
