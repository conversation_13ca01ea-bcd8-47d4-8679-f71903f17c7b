/**
 * @file Notification Interfaces
 * @filepath shared/src/interfaces/tracking/notification-interfaces.ts
 * @task-id M-TSK-01.SUB-04.1.INT-01
 * @component notification-interfaces
 * @reference foundation-context.INTERFACES.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Interface-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Notification interfaces module providing:
 * - Interface definitions for notification system components
 * - Contract specifications for notification services
 * - Type-safe interface definitions for notification operations
 * - API specifications for notification integration
 * - Enterprise-grade interface contracts for notification systems
 * - Comprehensive interface definitions for notification workflows
 * - Integration interfaces for notification channels and templates
 * - Performance-optimized interface definitions for notification processing
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-008-notification-interfaces-architecture
 * @governance-dcr DCR-foundation-008-notification-interfaces-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/governance/notification-types
 * @enables server/src/platform/tracking/core-managers/NotificationManager
 * @enables server/src/platform/governance/automation-processing/NotificationProcessor
 * @related-contexts foundation-context, notification-context, interface-definitions-context
 * @governance-impact framework-foundation, notification-system, interface-contracts
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/notification-context/interfaces/notification-interfaces.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial notification interfaces implementation
 * v1.1.0 (2025-07-28) - Added comprehensive interface definitions for notification system
 */

import {
  TNotificationConfig,
  TNotificationChannel,
  TNotificationTemplate,
  TNotificationEvent,
  TNotificationResult,
  TNotificationStatus
} from '../../types/platform/governance/notification-types';

/**
 * Rule notification system interface
 * Manages rule-based notifications and alerts
 */
export interface IGovernanceRuleNotificationSystem {
  /**
   * Send a notification
   * @param event - Notification event
   * @param config - Notification configuration
   * @param accessToken - Optional access token
   */
  sendNotification(
    event: TNotificationEvent,
    config: TNotificationConfig,
    accessToken?: string
  ): Promise<TNotificationResult>;

  /**
   * Update notification template
   * @param template - Notification template
   * @param accessToken - Optional access token
   */
  updateTemplate(
    template: TNotificationTemplate,
    accessToken?: string
  ): Promise<void>;

  /**
   * Configure notification channel
   * @param channel - Notification channel
   * @param accessToken - Optional access token
   */
  configureChannel(
    channel: TNotificationChannel,
    accessToken?: string
  ): Promise<void>;

  /**
   * Get notification status
   * @param eventId - Event identifier
   * @param accessToken - Optional access token
   */
  getNotificationStatus(
    eventId: string,
    accessToken?: string
  ): Promise<TNotificationStatus>;
} 