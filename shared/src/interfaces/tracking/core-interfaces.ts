/**
 * @file Core Tracking Management Interfaces
 * @filepath shared/src/interfaces/tracking/core-interfaces.ts
 * @task-id T-TSK-03.SUB-03.2.IMP-01A
 * @component tracking-core-interfaces
 * @reference foundation-context.INTERFACE.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/tracking/tracking-management-types
 * @enables server/src/platform/tracking/core-managers
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/interfaces/core-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { 
  TValidationResult, 
  TTrackingConfig,
  TManagerStatus,
  TManagerConfig,
  TFileOperationResult,
  TRealTimeEvent,
  TDashboardData,
  TManagerMetrics
} from '../../types/tracking/tracking-management-types';

import {
  TRuleExecutionMetrics,
  TComplianceMetrics,
  TRulePerformanceMetrics,
  TSystemMetrics,
  TMetricsDashboard,
  TExportFormat,
  TExportResult
} from '../../types/platform/governance/governance-types';
import { TTimeRange } from '../../types/platform/tracking/tracking-types';

/**
 * Tracking data type
 */
export type TTrackingData = {
  /** Component identifier */
  componentId: string;
  
  /** Operation type */
  operation: string;
  
  /** Operation data */
  data: Record<string, unknown>;
  
  /** Operation metadata */
  metadata?: Record<string, unknown>;
  
  /** Operation timestamp */
  timestamp: Date;
};

// ============================================================================
// CORE MANAGEMENT INTERFACES
// ============================================================================

/**
 * Tracking Manager Interface
 * Central coordination interface for all tracking operations
 */
export interface ITrackingManager {
  /**
   * Initialize the tracking manager
   * @param config - Manager configuration
   */
  initialize(config?: Partial<TManagerConfig>): Promise<void>;

  /**
   * Start tracking operations
   */
  start(): Promise<void>;

  /**
   * Stop tracking operations
   */
  stop(): Promise<void>;

  /**
   * Get manager status
   */
  getStatus(): Promise<TManagerStatus>;

  /**
   * Process tracking data
   * @param data - Tracking data to process
   */
  processTracking(data: TTrackingData): Promise<TValidationResult>;

  /**
   * Get tracking metrics
   */
  getMetrics(): Promise<TManagerMetrics>;

  /**
   * Shutdown the manager
   */
  shutdown(): Promise<void>;
}

/**
 * File Manager Interface
 * File operations interface for tracking data management
 */
export interface IFileManager {
  /**
   * Initialize file manager
   * @param config - File manager configuration
   */
  initialize(config?: Partial<TManagerConfig>): Promise<void>;

  /**
   * Read tracking file
   * @param filePath - Path to the file
   */
  readFile(filePath: string): Promise<TFileOperationResult>;

  /**
   * Write tracking file
   * @param filePath - Path to the file
   * @param data - Data to write
   */
  writeFile(filePath: string, data: any): Promise<TFileOperationResult>;

  /**
   * Delete tracking file
   * @param filePath - Path to the file
   */
  deleteFile(filePath: string): Promise<TFileOperationResult>;

  /**
   * List files in directory
   * @param directoryPath - Path to the directory
   */
  listFiles(directoryPath: string): Promise<string[]>;

  /**
   * Ensure directory exists
   * @param directoryPath - Path to the directory
   */
  ensureDirectory(directoryPath: string): Promise<void>;

  /**
   * Get file stats
   * @param filePath - Path to the file
   */
  getFileStats(filePath: string): Promise<any>;
}

/**
 * Real Time Manager Interface
 * Real-time operations interface for tracking updates
 */
export interface IRealTimeManager {
  /**
   * Initialize real-time manager
   * @param config - Real-time manager configuration
   */
  initialize(config?: Partial<TManagerConfig>): Promise<void>;

  /**
   * Start real-time services
   */
  startRealTime(): Promise<void>;

  /**
   * Stop real-time services
   */
  stopRealTime(): Promise<void>;

  /**
   * Subscribe to real-time events
   * @param eventType - Type of event to subscribe to
   * @param callback - Callback function for events
   */
  subscribe(eventType: string, callback: (event: TRealTimeEvent) => void): Promise<string>;

  /**
   * Unsubscribe from real-time events
   * @param subscriptionId - Subscription identifier
   */
  unsubscribe(subscriptionId: string): Promise<void>;

  /**
   * Broadcast real-time event
   * @param event - Event to broadcast
   */
  broadcast(event: TRealTimeEvent): Promise<void>;

  /**
   * Get active connections count
   */
  getConnectionsCount(): Promise<number>;
}

/**
 * Dashboard Manager Interface
 * Dashboard operations interface for tracking visualization
 */
export interface IDashboardManager {
  /**
   * Initialize dashboard manager
   * @param config - Dashboard manager configuration
   */
  initialize(config?: Partial<TManagerConfig>): Promise<void>;

  /**
   * Get dashboard data
   * @param dashboardId - Dashboard identifier
   */
  getDashboardData(dashboardId: string): Promise<TDashboardData>;

  /**
   * Update dashboard data
   * @param dashboardId - Dashboard identifier
   * @param data - Updated data
   */
  updateDashboardData(dashboardId: string, data: Partial<TDashboardData>): Promise<void>;

  /**
   * Create new dashboard
   * @param config - Dashboard configuration
   */
  createDashboard(config: any): Promise<string>;

  /**
   * Delete dashboard
   * @param dashboardId - Dashboard identifier
   */
  deleteDashboard(dashboardId: string): Promise<void>;

  /**
   * Get dashboard list
   */
  getDashboardList(): Promise<string[]>;

  /**
   * Export dashboard data
   * @param dashboardId - Dashboard identifier
   * @param format - Export format
   */
  exportDashboard(dashboardId: string, format: 'json' | 'csv' | 'pdf'): Promise<any>;
}

/**
 * Management Service Interface
 * Base interface for all management services
 */
export interface IManagementService {
  /**
   * Initialize the service
   */
  initialize(): Promise<void>;

  /**
   * Get service health status
   */
  getHealth(): Promise<any>;

  /**
   * Get service metrics
   */
  getMetrics(): Promise<any>;

  /**
   * Shutdown the service
   */
  shutdown(): Promise<void>;
}

// ============================================================================
// CACHE AND PERFORMANCE INTERFACES
// ============================================================================

/**
 * Cache Manager Interface
 * Provides caching capabilities with security and performance features
 */
export interface ICacheManager {
  /**
   * Get cached value
   * @param key - Cache key
   * @param accessToken - Optional access token for security
   */
  get(key: string, accessToken?: string): Promise<any>;

  /**
   * Set cached value
   * @param key - Cache key
   * @param value - Value to cache
   * @param options - Caching options
   */
  set(key: string, value: any, options?: {
    ttl?: number;
    securityLevel?: string;
    accessToken?: string;
    encrypt?: boolean;
  }): Promise<void>;

  /**
   * Delete cached value
   * @param key - Cache key
   * @param accessToken - Optional access token for security
   */
  delete(key: string, accessToken?: string): Promise<boolean>;

  /**
   * Clear all cached values
   * @param accessToken - Optional access token for security
   */
  clear(accessToken?: string): Promise<void>;

  /**
   * Get cache statistics
   */
  getStatistics(): Promise<any>;
}

/**
 * Performance Service Interface
 * Provides performance monitoring and optimization capabilities
 */
export interface IPerformanceService {
  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): Promise<any>;

  /**
   * Optimize performance
   */
  optimize(): Promise<void>;
}

/**
 * Performance Optimizer Interface
 * Interface for performance optimization operations
 */
export interface IPerformanceOptimizer {
  /**
   * Analyze performance
   * @param ruleId - Rule identifier
   * @param options - Analysis options
   */
  analyzePerformance(ruleId: string, options?: {
    strategy?: string;
    accessToken?: string;
    securityLevel?: string;
  }): Promise<any>;

  /**
   * Optimize rule performance
   * @param ruleId - Rule identifier
   * @param strategy - Optimization strategy
   * @param options - Optimization options
   */
  optimizeRule(ruleId: string, strategy: string, options?: {
    accessToken?: string;
    securityLevel?: string;
    maxExecutionTime?: number;
  }): Promise<any>;

  /**
   * Get optimization recommendations
   * @param ruleId - Rule identifier
   * @param accessToken - Optional access token
   */
  getOptimizationRecommendations(ruleId: string, accessToken?: string): Promise<any>;
}

/**
 * Optimization Service Interface
 * Interface for optimization service operations
 */
export interface IOptimizationService {
  /**
   * Get performance metrics
   */
  getPerformanceMetrics(): Promise<any>;

  /**
   * Monitor optimization
   * @param sessionId - Optimization session identifier
   * @param accessToken - Optional access token
   */
  monitorOptimization(sessionId: string, accessToken?: string): Promise<any>;
}

/**
 * Rule metrics collector interface
 * Collects and manages rule metrics
 */
export interface IGovernanceRuleMetricsCollector {
  /**
   * Record rule execution metric
   * @param ruleId - Rule identifier
   * @param executionData - Execution metrics
   */
  recordRuleExecution(ruleId: string, executionData: TRuleExecutionMetrics): Promise<void>;

  /**
   * Record compliance metric
   * @param complianceData - Compliance metrics
   */
  recordComplianceMetric(complianceData: TComplianceMetrics): Promise<void>;

  /**
   * Get rule performance metrics
   * @param ruleId - Rule identifier
   * @param timeRange - Time range for metrics
   */
  getRulePerformanceMetrics(
    ruleId: string,
    timeRange: TTimeRange
  ): Promise<TRulePerformanceMetrics>;

  /**
   * Get system metrics
   * @param timeRange - Time range for metrics
   */
  getSystemMetrics(timeRange: TTimeRange): Promise<TSystemMetrics>;

  /**
   * Generate metrics dashboard
   */
  generateMetricsDashboard(): Promise<TMetricsDashboard>;

  /**
   * Export metrics data
   * @param format - Export format
   * @param timeRange - Time range
   */
  exportMetrics(format: TExportFormat, timeRange: TTimeRange): Promise<TExportResult>;
} 