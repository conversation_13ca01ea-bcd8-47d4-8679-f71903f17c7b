/**
 * @file Tracking Management Interfaces
 * @filepath shared/src/interfaces/tracking/tracking-interfaces.ts
 * @task-id T-TSK-03.SUB-03.2.IMP-01
 * @component tracking-tracking-interfaces
 * @reference foundation-context.INTERFACE.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @enables server/src/platform/tracking/core-managers
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/interfaces/tracking-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

// Re-export core interfaces
export * from './core-interfaces';

// ============================================================================
// SERVICE INTERFACES
// ============================================================================

/**
 * File Service Interface
 * File system operations interface
 */
export interface IFileService {
  /**
   * Check if file exists
   * @param filePath - Path to check
   */
  exists(filePath: string): Promise<boolean>;

  /**
   * Get file permissions
   * @param filePath - Path to check
   */
  getPermissions(filePath: string): Promise<any>;

  /**
   * Set file permissions
   * @param filePath - Path to modify
   * @param permissions - Permissions to set
   */
  setPermissions(filePath: string, permissions: any): Promise<void>;

  /**
   * Watch file for changes
   * @param filePath - Path to watch
   * @param callback - Callback for changes
   */
  watchFile(filePath: string, callback: (event: any) => void): Promise<string>;

  /**
   * Stop watching file
   * @param watchId - Watch identifier
   */
  stopWatching(watchId: string): Promise<void>;
}

/**
 * Realtime Service Interface
 * Real-time communication interface
 */
export interface IRealtimeService {
  /**
   * Establish connection
   * @param clientId - Client identifier
   */
  connect(clientId: string): Promise<void>;

  /**
   * Disconnect client
   * @param clientId - Client identifier
   */
  disconnect(clientId: string): Promise<void>;

  /**
   * Send message to client
   * @param clientId - Client identifier
   * @param message - Message to send
   */
  sendMessage(clientId: string, message: any): Promise<void>;

  /**
   * Broadcast message to all clients
   * @param message - Message to broadcast
   */
  broadcastMessage(message: any): Promise<void>;

  /**
   * Get connected clients
   */
  getConnectedClients(): Promise<string[]>;
}

/**
 * UI Service Interface
 * User interface operations interface
 */
export interface IUIService {
  /**
   * Render UI component
   * @param componentId - Component identifier
   * @param data - Component data
   */
  renderComponent(componentId: string, data: any): Promise<any>;

  /**
   * Update UI component
   * @param componentId - Component identifier
   * @param updates - Updates to apply
   */
  updateComponent(componentId: string, updates: any): Promise<void>;

  /**
   * Get UI state
   * @param componentId - Component identifier
   */
  getUIState(componentId: string): Promise<any>;

  /**
   * Set UI state
   * @param componentId - Component identifier
   * @param state - State to set
   */
  setUIState(componentId: string, state: any): Promise<void>;
}

// ============================================================================
// UTILITY INTERFACES
// ============================================================================

/**
 * Interface Definition Interface
 * Meta-interface for interface definitions
 */
export interface IInterfaceDefinition {
  /**
   * Get interface name
   */
  getInterfaceName(): string;

  /**
   * Get interface version
   */
  getInterfaceVersion(): string;

  /**
   * Validate interface implementation
   * @param implementation - Implementation to validate
   */
  validateImplementation(implementation: any): boolean;
}

/**
 * Utilities Interface
 * Core utilities interface
 */
export interface IUtilities {
  /**
   * Get utility name
   */
  getUtilityName(): string;

  /**
   * Execute utility operation
   * @param operation - Operation to execute
   * @param params - Operation parameters
   */
  executeOperation(operation: string, params?: any): Promise<any>;

  /**
   * Get available operations
   */
  getAvailableOperations(): string[];
}

/**
 * Utility Service Interface
 * Utility operations interface
 */
export interface IUtilityService {
  /**
   * Get utility name
   */
  getUtilityName(): string;

  /**
   * Execute utility operation
   * @param operation - Operation to execute
   * @param params - Operation parameters
   */
  executeOperation(operation: string, params?: any): Promise<any>;

  /**
   * Get available operations
   */
  getAvailableOperations(): string[];
}

/**
 * Helper Service Interface
 * Helper operations interface
 */
export interface IHelperService {
  /**
   * Get helper name
   */
  getHelperName(): string;

  /**
   * Provide help for operation
   * @param operation - Operation to get help for
   */
  getHelp(operation: string): Promise<any>;

  /**
   * Execute helper operation
   * @param operation - Operation to execute
   * @param context - Operation context
   */
  executeHelper(operation: string, context?: any): Promise<any>;
}

/**
 * Configuration Service Interface
 * Configuration management interface
 */
export interface IConfigurationService {
  /**
   * Get configuration value
   * @param key - Configuration key
   */
  getConfig(key: string): Promise<any>;

  /**
   * Set configuration value
   * @param key - Configuration key
   * @param value - Configuration value
   */
  setConfig(key: string, value: any): Promise<void>;

  /**
   * Get all configuration
   */
  getAllConfig(): Promise<any>;

  /**
   * Validate configuration
   * @param config - Configuration to validate
   */
  validateConfig(config: any): Promise<boolean>;
}