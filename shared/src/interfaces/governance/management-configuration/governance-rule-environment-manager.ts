/**
 * @file Governance Rule Environment Manager Interfaces
 * @filepath shared/src/interfaces/governance/management-configuration/governance-rule-environment-manager.ts
 * @reference G-TSK-07.SUB-07.1.IMP-04
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-07-05
 * @modified 2025-07-05
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-007-management-administration-architecture
 * @governance-dcr DCR-foundation-006-security-integration-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/governance/rule-management-types
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleEnvironmentManager
 * @related-contexts foundation-context, governance-context
 * @governance-impact framework-foundation, environment-management
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/interfaces/governance-rule-environment-manager.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import {
  TValidationResult,
  TMetrics,
  TAuthorityData
} from '../../../types/platform/tracking/tracking-types';

// ============================================================================
// ENVIRONMENT MANAGER CORE INTERFACES
// ============================================================================

/**
 * Environment Manager Interface
 * Core interface for governance rule environment management
 */
export interface IGovernanceRuleEnvironmentManager {
  initialize(): Promise<void>;
  createEnvironment(environmentConfig: IEnvironmentConfiguration): Promise<string>;
  updateEnvironment(environmentId: string, updates: Partial<IEnvironmentConfiguration>): Promise<void>;
  deleteEnvironment(environmentId: string): Promise<void>;
  getEnvironment(environmentId: string): Promise<IEnvironmentConfiguration | null>;
  listEnvironments(filter?: IEnvironmentFilter): Promise<IEnvironmentConfiguration[]>;
  validateEnvironment(environmentConfig: IEnvironmentConfiguration): Promise<TValidationResult>;
  deployEnvironment(environmentId: string, deploymentOptions?: IDeploymentOptions): Promise<IDeploymentResult>;
  startEnvironment(environmentId: string): Promise<void>;
  stopEnvironment(environmentId: string): Promise<void>;
  getEnvironmentStatus(environmentId: string): Promise<IEnvironmentStatus>;
  getEnvironmentMetrics(environmentId: string): Promise<IEnvironmentMetrics>;
  getHealth(): Promise<any>;
  getMetrics(): Promise<TMetrics>;
  shutdown(): Promise<void>;
}

/**
 * Environment Configuration Interface
 */
export interface IEnvironmentConfiguration {
  id: string;
  name: string;
  description?: string;
  type: 'development' | 'testing' | 'staging' | 'production' | 'sandbox';
  status: 'creating' | 'active' | 'inactive' | 'deploying' | 'failed' | 'maintenance';
  version: string;
  configuration: {
    resources: IResourceConfiguration;
    network: INetworkConfiguration;
    security: ISecurityConfiguration;
    monitoring: IMonitoringConfiguration;
  };
  variables: Record<string, string>;
  secrets: Record<string, string>;
  tags: Record<string, string>;
  metadata: {
    createdAt: Date;
    modifiedAt: Date;
    createdBy: string;
    modifiedBy: string;
    owner: string;
    authority: TAuthorityData;
  };
}

/**
 * Resource Configuration Interface
 */
export interface IResourceConfiguration {
  cpu: {
    limit: number;
    request: number;
    architecture: 'x86_64' | 'arm64' | 'mixed';
  };
  memory: {
    limit: number;
    request: number;
    type: 'standard' | 'high-memory' | 'optimized';
  };
  storage: {
    limit: number;
    type: 'ssd' | 'hdd' | 'nvme';
    class: 'standard' | 'premium' | 'archive';
  };
  network: {
    bandwidth: number;
    type: 'standard' | 'premium' | 'dedicated';
  };
}

/**
 * Network Configuration Interface
 */
export interface INetworkConfiguration {
  vpc: {
    id: string;
    cidr: string;
    dns: {
      servers: string[];
      searchDomains: string[];
    };
  };
  subnets: {
    public: string[];
    private: string[];
  };
  securityGroups: {
    ingress: ISecurityRule[];
    egress: ISecurityRule[];
  };
}

/**
 * Security Rule Interface
 */
export interface ISecurityRule {
  type: 'allow' | 'deny';
  protocol: 'tcp' | 'udp' | 'icmp' | 'all';
  ports: {
    from: number;
    to: number;
  };
  source: string;
  description?: string;
}

/**
 * Security Configuration Interface
 */
export interface ISecurityConfiguration {
  authentication: {
    type: 'basic' | 'oauth' | 'saml' | 'ldap';
    provider?: string;
    config: Record<string, any>;
  };
  authorization: {
    type: 'rbac' | 'abac' | 'custom';
    policies: Record<string, any>;
  };
  encryption: {
    atRest: {
      enabled: boolean;
      algorithm: string;
      keyManagement: 'managed' | 'customer';
    };
    inTransit: {
      enabled: boolean;
      tlsVersion: string;
    };
  };
}

/**
 * Monitoring Configuration Interface
 */
export interface IMonitoringConfiguration {
  metrics: {
    enabled: boolean;
    interval: number;
    retention: number;
    exporters: string[];
  };
  logging: {
    enabled: boolean;
    level: 'debug' | 'info' | 'warn' | 'error';
    retention: number;
    destinations: string[];
  };
  alerting: {
    enabled: boolean;
    rules: IAlertRule[];
    destinations: string[];
  };
}

/**
 * Alert Rule Interface
 */
export interface IAlertRule {
  name: string;
  description?: string;
  condition: string;
  severity: 'info' | 'warning' | 'critical';
  threshold: number;
  duration: number;
  labels: Record<string, string>;
}

/**
 * Environment Filter Interface
 */
export interface IEnvironmentFilter {
  type?: string[];
  status?: string[];
  owner?: string;
  tags?: Record<string, string>;
  createdRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Deployment Options Interface
 */
export interface IDeploymentOptions {
  strategy: 'blue-green' | 'rolling' | 'canary' | 'recreate';
  timeout: number;
  rollbackOnFailure: boolean;
  preDeploymentHooks?: string[];
  postDeploymentHooks?: string[];
}

/**
 * Deployment Result Interface
 */
export interface IDeploymentResult {
  deploymentId: string;
  status: 'success' | 'failure' | 'in-progress' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  logs: string[];
  errors: string[];
  artifacts: string[];
}

/**
 * Environment Status Interface
 */
export interface IEnvironmentStatus {
  environmentId: string;
  status: 'creating' | 'active' | 'inactive' | 'deploying' | 'failed' | 'maintenance';
  health: 'healthy' | 'unhealthy' | 'degraded' | 'unknown';
  uptime: number;
  lastCheck: Date;
  instances: {
    total: number;
    running: number;
    failed: number;
    pending: number;
  };
  resources: {
    cpu: {
      usage: number;
      limit: number;
    };
    memory: {
      usage: number;
      limit: number;
    };
    storage: {
      usage: number;
      limit: number;
    };
  };
}

/**
 * Environment Metrics Interface
 */
export interface IEnvironmentMetrics {
  environmentId: string;
  timestamp: Date;
  performance: {
    cpu: {
      usage: number;
      loadAverage: number[];
      cores: number;
    };
    memory: {
      usage: number;
      used: number;
      total: number;
      available: number;
    };
    storage: {
      usage: number;
      used: number;
      total: number;
      available: number;
    };
    network: {
      in: number;
      out: number;
      connections: number;
    };
  };
  application: {
    requests: number;
    errors: number;
    responseTime: number;
    throughput: number;
  };
}

/**
 * Environment Service Interface
 */
export interface IEnvironmentService {
  initialize(): Promise<void>;
  getHealth(): Promise<any>;
  getMetrics(): Promise<TMetrics>;
  shutdown(): Promise<void>;
}

/**
 * Management Service Interface
 */
export interface IManagementService {
  initialize(): Promise<void>;
  getHealth(): Promise<any>;
  getMetrics(): Promise<TMetrics>;
  shutdown(): Promise<void>;
} 