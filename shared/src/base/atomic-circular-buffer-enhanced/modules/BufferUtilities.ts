/**
 * @file BufferUtilities
 * @filepath shared/src/base/atomic-circular-buffer-enhanced/modules/BufferUtilities.ts
 * @task-id M-TSK-01.SUB-01.2.ENH-01.MOD-06
 * @component buffer-utilities
 * @reference foundation-context.MEMORY-SAFETY.007.MOD-06
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Module
 * @created 2025-07-29 09:30:00 +03
 * @modified 2025-07-29 09:30:00 +03
 *
 * @description
 * Buffer utilities module providing:
 * - Common helper functions and validation utilities
 * - Data transformation and serialization helpers
 * - Performance optimization utilities
 * - Error handling and recovery mechanisms
 * - Type guards and validation functions
 * - Integration utilities for all buffer modules
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * @task-compliance M-TSK-01.SUB-01.2.ENH-01.MOD-06
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/utils/ResilientTiming
 * @depends-on shared/src/base/utils/ResilientMetrics
 * @integrates-with shared/src/base/AtomicCircularBufferEnhanced
 * @integrates-with all buffer modules
 * @enables buffer-utilities
 * @enables validation-helpers
 * @related-contexts foundation-context, memory-safety-context, enhancement-context
 * @governance-impact framework-foundation, enhanced-atomic-operations
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-module-enhanced
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @enhancement-phase phase-1
 * @backward-compatibility 100%
 * @performance-requirements <1ms-utility-operations
 * @documentation docs/governance/contexts/foundation-context/modules/buffer-utilities.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   enhancement-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-29) - Initial modular extraction from AtomicCircularBufferEnhanced.ts
 * v1.0.0 (2025-07-29) - Added comprehensive utility functions and validation helpers
 * v1.0.0 (2025-07-29) - Implemented performance-optimized helper operations
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';
import { ResilientTimer } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';

// ============================================================================
// SECTION 1: UTILITY INTERFACES & TYPES (Lines 1-80)
// AI Context: "Utility interfaces and helper type definitions"
// ============================================================================

/**
 * Validation result interface
 */
export interface IValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Data transformation options
 */
export interface ITransformOptions {
  preserveTypes: boolean;
  includeMetadata: boolean;
  compressData: boolean;
}

/**
 * Performance measurement result
 */
export interface IPerformanceMeasurement {
  operationName: string;
  duration: number;
  reliable: boolean;
  timestamp: Date;
}

// ============================================================================
// SECTION 2: BUFFER UTILITIES CLASS (Lines 81-150)
// AI Context: "Main utilities class with helper functions and validation"
// ============================================================================

/**
 * Buffer utilities with comprehensive helper functions
 * 
 * Provides enterprise-grade utility functions with:
 * - Data validation and transformation helpers
 * - Performance measurement and optimization utilities
 * - Error handling and recovery mechanisms
 * - Type guards and validation functions
 */
export class BufferUtilities extends MemorySafeResourceManager implements ILoggingService {
  private _logger: SimpleLogger;
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  constructor() {
    super();
    this._logger = new SimpleLogger('BufferUtilities');
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Initialize utilities with resilient timing
   */
  protected async doInitialize(): Promise<void> {
    this._initializeSync();
    this.logInfo('BufferUtilities initialized with resilient timing (async)');
  }

  /**
   * ✅ JEST COMPATIBILITY: Synchronous initialization for constructor usage
   */
  public initializeSync(): void {
    this._initializeSync();
    this.logInfo('BufferUtilities initialized with resilient timing (sync)');
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Core initialization logic
   */
  private _initializeSync(): void {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: 1000, // 1 second max for utility operations
      unreliableThreshold: 3,
      estimateBaseline: 1 // 1ms baseline for utility operations
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['validation', 1],
        ['transformation', 2],
        ['serialization', 3],
        ['typeGuard', 0.5]
      ])
    });
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Shutdown utilities
   */
  protected async doShutdown(): Promise<void> {
    this.logInfo('BufferUtilities shutdown completed');
  }

  // ============================================================================
  // SECTION 3: VALIDATION UTILITIES (Lines 151-220)
  // AI Context: "Data validation and type guard utilities"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Validate buffer key with resilient timing
   */
  public validateKey(key: any): IValidationResult {
    // ✅ CRITICAL FIX: Check if resilient timer is initialized before using it
    const validationContext = this._resilientTimer ? this._resilientTimer.start() : null;
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if key is defined
      if (key === null || key === undefined) {
        errors.push('Key cannot be null or undefined');
      }

      // Check key type
      if (typeof key !== 'string' && typeof key !== 'number') {
        errors.push('Key must be a string or number');
      }

      // Check key length for strings
      if (typeof key === 'string') {
        if (key.length === 0) {
          errors.push('Key cannot be empty string');
        }
        if (key.length > 1000) {
          warnings.push('Key is very long, may impact performance');
        }
      }

      // Check key range for numbers
      if (typeof key === 'number') {
        if (!Number.isFinite(key)) {
          errors.push('Numeric key must be finite');
        }
        if (key < 0) {
          warnings.push('Negative numeric keys may cause sorting issues');
        }
      }

      // ✅ CRITICAL FIX: Only record timing if timing context exists
      if (validationContext) {
        const timingResult = validationContext.end();
        this._metricsCollector?.recordTiming('validation', timingResult);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      if (validationContext) {
        const timingResult = validationContext.end();
        this._metricsCollector?.recordTiming('validationError', timingResult);
      }
      this.logError('Key validation failed', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Validate buffer value with resilient timing
   */
  public validateValue<T>(value: T): IValidationResult {
    // ✅ CRITICAL FIX: Check if resilient timer is initialized before using it
    const validationContext = this._resilientTimer ? this._resilientTimer.start() : null;
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if value is defined (null is allowed)
      if (value === undefined) {
        warnings.push('Undefined values may cause issues in some operations');
      }

      // Check for circular references
      try {
        JSON.stringify(value);
      } catch (jsonError) {
        if (jsonError instanceof Error && jsonError.message.includes('circular')) {
          errors.push('Value contains circular references');
        } else {
          warnings.push('Value may not be serializable');
        }
      }

      // Check value size (approximate)
      try {
        const serialized = JSON.stringify(value);
        if (serialized.length > 1000000) { // 1MB
          warnings.push('Value is very large, may impact performance');
        }
      } catch {
        // Ignore serialization errors for size check
      }

      // ✅ CRITICAL FIX: Only record timing if timing context exists
      if (validationContext) {
        const timingResult = validationContext.end();
        this._metricsCollector?.recordTiming('validation', timingResult);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      if (validationContext) {
        const timingResult = validationContext.end();
        this._metricsCollector?.recordTiming('validationError', timingResult);
      }
      this.logError('Value validation failed', error);
      throw error;
    }
  }

  // ============================================================================
  // SECTION 4: TRANSFORMATION UTILITIES (Lines 221-290)
  // AI Context: "Data transformation and serialization utilities"
  // ============================================================================

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Deep clone object
   */
  public deepClone<T>(obj: T): T {
    const cloneContext = this._resilientTimer.start();

    try {
      // Handle primitive types
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }

      // Handle Date objects
      if (obj instanceof Date) {
        return new Date(obj.getTime()) as unknown as T;
      }

      // Handle Arrays
      if (Array.isArray(obj)) {
        return obj.map(item => this.deepClone(item)) as unknown as T;
      }

      // Handle Objects
      const cloned = {} as T;
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          (cloned as any)[key] = this.deepClone((obj as any)[key]);
        }
      }

      const timingResult = cloneContext.end();
      this._metricsCollector.recordTiming('transformation', timingResult);

      return cloned;

    } catch (error) {
      cloneContext.end();
      this.logError('Deep clone failed', error);
      throw error;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Safe JSON serialization
   */
  public safeStringify(obj: any, options: ITransformOptions = { preserveTypes: true, includeMetadata: false, compressData: false }): string {
    const serializationContext = this._resilientTimer.start();

    try {
      const replacer = options.preserveTypes ? (key: string, value: any) => {
        if (value instanceof Date) {
          return { __type: 'Date', value: value.toISOString() };
        }
        if (value instanceof Map) {
          return { __type: 'Map', value: Array.from(value.entries()) };
        }
        if (value instanceof Set) {
          return { __type: 'Set', value: Array.from(value) };
        }
        return value;
      } : undefined;

      const result = JSON.stringify(obj, replacer, options.compressData ? 0 : 2);

      const timingResult = serializationContext.end();
      this._metricsCollector.recordTiming('serialization', timingResult);

      return result;

    } catch (error) {
      serializationContext.end();
      this.logError('Safe stringify failed', error);
      return '{}';
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Safe JSON parsing
   */
  public safeParse<T>(jsonString: string, defaultValue: T): T {
    const parseContext = this._resilientTimer.start();

    try {
      const parsed = JSON.parse(jsonString, (key: string, value: any) => {
        if (value && typeof value === 'object' && value.__type) {
          switch (value.__type) {
            case 'Date':
              return new Date(value.value);
            case 'Map':
              return new Map(value.value);
            case 'Set':
              return new Set(value.value);
          }
        }
        return value;
      });

      const timingResult = parseContext.end();
      this._metricsCollector.recordTiming('serialization', timingResult);

      return parsed;

    } catch (error) {
      parseContext.end();
      this.logError('Safe parse failed', error);
      return defaultValue;
    }
  }

  /**
   * ✅ ANTI-SIMPLIFICATION COMPLIANT: Type guard for buffer key
   */
  public isValidBufferKey(key: any): key is string | number {
    const typeGuardContext = this._resilientTimer.start();

    try {
      const result = (typeof key === 'string' || typeof key === 'number') && 
                    key !== null && 
                    key !== undefined &&
                    (typeof key !== 'string' || key.length > 0) &&
                    (typeof key !== 'number' || Number.isFinite(key));

      const timingResult = typeGuardContext.end();
      this._metricsCollector.recordTiming('typeGuard', timingResult);

      return result;

    } catch (error) {
      typeGuardContext.end();
      this.logError('Type guard failed', error);
      return false;
    }
  }

  // Logging interface implementation
  logInfo(message: string, details?: Record<string, unknown>): void {
    this._logger.logInfo(message, details);
  }

  logError(message: string, error: unknown, details?: Record<string, unknown>): void {
    this._logger.logError(message, error, details);
  }

  logDebug(message: string, details?: Record<string, unknown>): void {
    this._logger.logDebug(message, details);
  }

  logWarning(message: string, details?: Record<string, unknown>): void {
    this._logger.logWarning(message, details);
  }
}
