/**
 * @file Phase Integration
 * @filepath shared/src/base/timer-coordination/modules/PhaseIntegration.ts
 * @task-id M-TSK-01.SUB-02.2.MOD-02
 * @component phase-integration
 * @reference foundation-context.TIMER-COORDINATION.004
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context timer-coordination-context
 * @category Timer-Coordination-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Phase integration module providing:
 * - Enterprise Phase 1 & 2 integration management
 * - Integration with AtomicCircularBufferEnhanced for memory-safe operations
 * - Integration with EventHandlerRegistryEnhanced for event coordination
 * - Phase coordination with dependency management
 * - Event management with enterprise-grade reliability
 * - Memory-safe phase operations with automatic cleanup
 * - Performance optimization with <1ms integration overhead
 * - Integration with TimerCoordinationServiceEnhanced for coordinated phases
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-011-phase-integration-architecture
 * @governance-dcr DCR-foundation-011-phase-integration-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/EventHandlerRegistryEnhanced
 * @enables shared/src/base/TimerCoordinationServiceEnhanced
 * @enables shared/src/base/timer-coordination/modules/AdvancedScheduler
 * @related-contexts timer-coordination-context, foundation-context, memory-safety-context
 * @governance-impact framework-foundation, timer-coordination, phase-integration
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type timer-coordination-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/timer-coordination-context/modules/PhaseIntegration.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial phase integration implementation with enterprise coordination
 * v1.1.0 (2025-07-28) - Added event management and memory-safe phase operations
 */

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import { 
  ResilientTimer
} from '../../utils/ResilientTiming';

import { 
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

// Import base dependencies
import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { AtomicCircularBufferEnhanced } from '../../AtomicCircularBufferEnhanced';
import { EventHandlerRegistryEnhanced } from '../../EventHandlerRegistryEnhanced';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';

// Import type definitions
import { 
  ITimerGroup,
  ITimerCoordinationServiceEnhancedConfig
} from '../types/TimerTypes';

import { 
  PERFORMANCE_REQUIREMENTS,
  createResilientTimer,
  createResilientMetricsCollector
} from './TimerConfiguration';

// ============================================================================
// SECTION 1: PHASE INTEGRATION INTERFACES
// AI Context: "Phase integration event and buffer interfaces"
// ============================================================================

export interface ITimerEvent {
  timerId: string;
  serviceId: string;
  eventType: 'created' | 'executed' | 'failed' | 'removed';
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

export interface IPhaseIntegrationMetrics {
  phase1BufferOperations: number;
  phase2EventEmissions: number;
  bufferUtilization: number;
  eventSuccessRate: number;
  lastIntegrationCheck: Date;
}

// ============================================================================
// SECTION 2: PHASE INTEGRATION MANAGER CLASS
// AI Context: "Enterprise phase integration with resilient timing infrastructure"
// ============================================================================

export class PhaseIntegrationManager extends MemorySafeResourceManager implements ILoggingService {
  
  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  private _logger: SimpleLogger;
  private _config: ITimerCoordinationServiceEnhancedConfig;
  
  // Phase integration components
  private _timerEventBuffer?: AtomicCircularBufferEnhanced<ITimerEvent>;
  private _eventRegistry?: EventHandlerRegistryEnhanced;
  
  // Integration metrics
  private _integrationMetrics: IPhaseIntegrationMetrics;
  
  constructor(config: ITimerCoordinationServiceEnhancedConfig) {
    super({
      maxIntervals: 25,
      maxTimeouts: 15,
      maxCacheSize: 2 * 1024 * 1024, // 2MB for phase integration
      memoryThresholdMB: 50,
      cleanupIntervalMs: 300000 // 5 minutes
    });
    
    this._logger = new SimpleLogger('PhaseIntegrationManager');
    this._config = config;
    this._integrationMetrics = {
      phase1BufferOperations: 0,
      phase2EventEmissions: 0,
      bufferUtilization: 0,
      eventSuccessRate: 1.0,
      lastIntegrationCheck: new Date()
    };

    // Initialize resilient timing infrastructure immediately
    this._resilientTimer = createResilientTimer();
    this._metricsCollector = createResilientMetricsCollector();
  }
  
  // ============================================================================
  // SECTION 3: LIFECYCLE MANAGEMENT WITH RESILIENT TIMING
  // AI Context: "Memory-safe initialization and cleanup with timing infrastructure"
  // ============================================================================
  
  protected async doInitialize(): Promise<void> {
    // Resilient timing infrastructure already initialized in constructor

    // Initialize phase integration components
    await this._initializePhaseIntegration();

    this.logInfo('PhaseIntegrationManager initialized with resilient timing infrastructure', {
      phase1BufferEnabled: this._config.integration.phase1BufferEnabled,
      phase2EventEnabled: this._config.integration.phase2EventEnabled,
      resilientTimingEnabled: true
    });
  }
  
  protected async doShutdown(): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context for shutdown
    const shutdownContext = this._resilientTimer.start();
    
    try {
      // Shutdown phase integration
      await this._shutdownPhaseIntegration();
      
      // Record successful shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('phase_integration_operations', shutdownResult);
      
      this.logInfo('PhaseIntegrationManager shutdown completed', {
        shutdownTime: `${shutdownResult.duration}ms`
      });
      
    } catch (error) {
      // Record failed shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('phase_integration_operations_failed', shutdownResult);
      throw this._enhanceErrorContext(error, { operation: 'shutdown' });
    }
  }
  
  // ============================================================================
  // SECTION 4: PHASE INTEGRATION OPERATIONS WITH RESILIENT TIMING
  // AI Context: "Phase 1 buffer and Phase 2 event integration with timing measurement"
  // ============================================================================
  
  public async initializePhaseIntegration(): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context for initialization
    const initContext = this._resilientTimer.start();
    
    try {
      await this._initializePhaseIntegration();
      
      // Record successful initialization timing
      const initResult = initContext.end();
      this._metricsCollector.recordTiming('phase_integration_operations', initResult);
      
      // Validate performance requirement
      if (initResult.reliable && initResult.duration > PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS) {
        this.logWarning('Phase integration initialization exceeded performance requirement', {
          duration: `${initResult.duration}ms`,
          requirement: `${PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS}ms`
        });
      }
      
    } catch (error) {
      // Record failed initialization timing
      const initResult = initContext.end();
      this._metricsCollector.recordTiming('phase_integration_operations_failed', initResult);
      throw this._enhanceErrorContext(error, { operation: 'initializePhaseIntegration' });
    }
  }
  
  public emitTimerGroupEvent(eventType: string, group: ITimerGroup): void {
    // CONTEXT-BASED TIMING - Create timing context for event emission
    const eventContext = this._resilientTimer.start();
    
    try {
      if (this._eventRegistry && this._config.integration.phase2EventEnabled) {
        this._eventRegistry.emitEvent(`timer-group-${eventType}`, {
          groupId: group.groupId,
          timerCount: group.timers.size,
          status: group.status,
          timestamp: new Date()
        });
        
        // Update metrics
        this._integrationMetrics.phase2EventEmissions++;
        this._integrationMetrics.lastIntegrationCheck = new Date();
        
        // Record successful event emission timing
        const eventResult = eventContext.end();
        this._metricsCollector.recordTiming('phase_integration_operations', eventResult);
        
        this.logDebug('Timer group event emitted successfully', {
          eventType,
          groupId: group.groupId,
          operationTime: `${eventResult.duration}ms`
        });
      }
      
    } catch (error) {
      // Record failed event emission timing
      const eventResult = eventContext.end();
      this._metricsCollector.recordTiming('phase_integration_operations_failed', eventResult);
      
      this.logError('Failed to emit timer group event', error, { 
        eventType, 
        groupId: group.groupId,
        operationTime: `${eventResult.duration}ms`
      });
    }
  }
  
  public async bufferTimerEvent(event: ITimerEvent): Promise<boolean> {
    // CONTEXT-BASED TIMING - Create timing context for buffer operation
    const bufferContext = this._resilientTimer.start();
    
    try {
      if (this._timerEventBuffer && this._config.integration.phase1BufferEnabled) {
        await this._timerEventBuffer.addItem(event.timerId, event);
        const success = true;

        // Update metrics
        this._integrationMetrics.phase1BufferOperations++;
        this._integrationMetrics.bufferUtilization = this._timerEventBuffer.getSize() / this._config.integration.bufferSize;
        this._integrationMetrics.lastIntegrationCheck = new Date();
        
        // Record successful buffer operation timing
        const bufferResult = bufferContext.end();
        this._metricsCollector.recordTiming('phase_integration_operations', bufferResult);
        
        this.logDebug('Timer event buffered', {
          timerId: event.timerId,
          eventType: event.eventType,
          success,
          bufferUtilization: this._integrationMetrics.bufferUtilization,
          operationTime: `${bufferResult.duration}ms`
        });
        
        return success;
      }
      
      return false;
      
    } catch (error) {
      // Record failed buffer operation timing
      const bufferResult = bufferContext.end();
      this._metricsCollector.recordTiming('phase_integration_operations_failed', bufferResult);
      
      this.logError('Failed to buffer timer event', error, {
        timerId: event.timerId,
        eventType: event.eventType,
        operationTime: `${bufferResult.duration}ms`
      });
      
      return false;
    }
  }
  
  public getIntegrationMetrics(): IPhaseIntegrationMetrics {
    return { ...this._integrationMetrics };
  }
  
  public isPhase1Enabled(): boolean {
    return this._config.integration.phase1BufferEnabled && !!this._timerEventBuffer;
  }
  
  public isPhase2Enabled(): boolean {
    return this._config.integration.phase2EventEnabled && !!this._eventRegistry;
  }

  // ============================================================================
  // SECTION 5: PRIVATE HELPER METHODS WITH ENHANCED ERROR CONTEXT
  // AI Context: "Phase integration utilities and initialization methods"
  // ============================================================================

  private async _initializePhaseIntegration(): Promise<void> {
    try {
      // Initialize Phase 1 integration (AtomicCircularBufferEnhanced)
      if (this._config.integration.phase1BufferEnabled) {
        this._timerEventBuffer = new AtomicCircularBufferEnhanced<ITimerEvent>(
          this._config.integration.bufferSize,
          {
            evictionPolicy: 'lru',
            autoCompaction: true,
            compactionThreshold: 0.3
          }
        );
        await this._timerEventBuffer.initialize();
        this.logInfo('Phase 1 integration initialized (AtomicCircularBufferEnhanced)', {
          bufferSize: this._config.integration.bufferSize
        });
      }

      // Initialize Phase 2 integration (EventHandlerRegistryEnhanced)
      if (this._config.integration.phase2EventEnabled) {
        this._eventRegistry = new EventHandlerRegistryEnhanced();
        await this._eventRegistry.initialize();
        this.logInfo('Phase 2 integration initialized (EventHandlerRegistryEnhanced)');
      }

    } catch (error) {
      this.logError('Phase integration initialization failed', error);
      // Don't throw - allow service to work without phase integration
    }
  }

  private async _shutdownPhaseIntegration(): Promise<void> {
    try {
      if (this._timerEventBuffer) {
        await this._timerEventBuffer.shutdown();
        this._timerEventBuffer = undefined;
        this.logInfo('Phase 1 integration shutdown completed');
      }

      if (this._eventRegistry) {
        await this._eventRegistry.shutdown();
        this._eventRegistry = undefined;
        this.logInfo('Phase 2 integration shutdown completed');
      }

    } catch (error) {
      this.logError('Phase integration shutdown error', error);
    }

    this.logInfo('Phase integration shutdown completed');
  }

  private _enhanceErrorContext(error: any, context: any): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    enhancedError.message = `${enhancedError.message} | Context: ${JSON.stringify(context)}`;
    return enhancedError;
  }

  // ILoggingService implementation
  logInfo(message: string, data?: any): void {
    this._logger.logInfo(message, data);
  }

  logWarning(message: string, data?: any): void {
    this._logger.logWarning(message, data);
  }

  logError(message: string, error?: any, data?: any): void {
    this._logger.logError(message, error, data);
  }

  logDebug(message: string, data?: any): void {
    this._logger.logDebug(message, data);
  }
}
