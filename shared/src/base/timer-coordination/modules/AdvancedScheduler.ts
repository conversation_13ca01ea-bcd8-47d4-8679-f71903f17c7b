/**
 * @file Advanced Scheduler
 * @filepath shared/src/base/timer-coordination/modules/AdvancedScheduler.ts
 * @task-id M-TSK-01.SUB-02.2.MOD-01
 * @component advanced-scheduler
 * @reference foundation-context.TIMER-COORDINATION.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context timer-coordination-context
 * @category Timer-Coordination-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Advanced scheduler module providing:
 * - Enterprise scheduling and cron management capabilities
 * - Conditional scheduling with complex rule evaluation
 * - Priority-based scheduling with resource optimization
 * - Advanced scheduling algorithms with timing precision
 * - Enterprise validation for scheduling configurations
 * - Memory-safe scheduling operations with automatic cleanup
 * - Performance optimization with <2ms scheduling overhead
 * - Integration with TimerCoordinationServiceEnhanced for coordinated operations
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-011-advanced-scheduler-architecture
 * @governance-dcr DCR-foundation-011-advanced-scheduler-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/utils/ResilientTiming
 * @enables shared/src/base/TimerCoordinationServiceEnhanced
 * @enables shared/src/base/timer-coordination/modules/PhaseIntegration
 * @related-contexts timer-coordination-context, foundation-context
 * @governance-impact framework-foundation, timer-coordination, advanced-scheduling
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type timer-coordination-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/timer-coordination-context/modules/AdvancedScheduler.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial advanced scheduler implementation with cron management
 * v1.1.0 (2025-07-28) - Added conditional and priority-based scheduling capabilities
 */

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import { 
  ResilientTimer
} from '../../utils/ResilientTiming';

import { 
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

// Import base dependencies
import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { TimerCoordinationService } from '../../TimerCoordinationService';
import { SimpleLogger, ILoggingService } from '../../LoggingMixin';

// Import type definitions
import { 
  IAdvancedTimerScheduling,
  IRecurringTimerConfig,
  ITimerSchedule,
  IScheduledTimer,
  ITimerCoordinationServiceEnhancedConfig
} from '../types/TimerTypes';

import { 
  PERFORMANCE_REQUIREMENTS,
  createResilientTimer,
  createResilientMetricsCollector
} from './TimerConfiguration';

import { TimerUtilities, CronExpressionParser } from './TimerUtilities';

// ============================================================================
// SECTION 1: ADVANCED SCHEDULER CLASS
// AI Context: "Enterprise scheduling with resilient timing infrastructure"
// ============================================================================

export class AdvancedScheduler extends MemorySafeResourceManager implements ILoggingService, IAdvancedTimerScheduling {
  
  // RESILIENT TIMING INFRASTRUCTURE - Dual-field pattern
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;
  
  private _logger: SimpleLogger;
  private _baseTimerService: TimerCoordinationService;
  private _utilities: TimerUtilities;
  private _cronParser: CronExpressionParser;
  private _config: ITimerCoordinationServiceEnhancedConfig;
  
  // Advanced scheduling state
  private _scheduledTimers = new Map<string, IScheduledTimer>();
  private _priorityQueue: Array<{priority: number, timerId: string, callback: () => void}> = [];
  
  constructor(
    baseTimerService: TimerCoordinationService, 
    utilities: TimerUtilities,
    config: ITimerCoordinationServiceEnhancedConfig
  ) {
    super({
      maxIntervals: 200,
      maxTimeouts: 100,
      maxCacheSize: 5 * 1024 * 1024, // 5MB for scheduling
      memoryThresholdMB: 100,
      cleanupIntervalMs: 300000 // 5 minutes
    });
    
    this._logger = new SimpleLogger('AdvancedScheduler');
    this._baseTimerService = baseTimerService;
    this._utilities = utilities;
    this._config = config;
    this._cronParser = new CronExpressionParser();

    // Initialize resilient timing infrastructure immediately
    this._resilientTimer = createResilientTimer();
    this._metricsCollector = createResilientMetricsCollector();
  }
  
  // ============================================================================
  // SECTION 2: LIFECYCLE MANAGEMENT WITH RESILIENT TIMING
  // AI Context: "Memory-safe initialization and cleanup with timing infrastructure"
  // ============================================================================
  
  protected async doInitialize(): Promise<void> {
    // Resilient timing infrastructure already initialized in constructor
    this.logInfo('AdvancedScheduler initialized with resilient timing infrastructure', {
      schedulingEnabled: this._config.scheduling.cronParsingEnabled,
      priorityEnabled: this._config.scheduling.prioritySchedulingEnabled,
      resilientTimingEnabled: true
    });
  }
  
  protected async doShutdown(): Promise<void> {
    // CONTEXT-BASED TIMING - Create timing context for shutdown
    const shutdownContext = this._resilientTimer.start();
    
    try {
      // Clear all scheduled timers
      const scheduledEntries = Array.from(this._scheduledTimers.entries());
      for (let i = 0; i < scheduledEntries.length; i++) {
        const [_timerId, scheduledTimer] = scheduledEntries[i];
        if (scheduledTimer.monitorTimerId) {
          this._baseTimerService.removeCoordinatedTimer(scheduledTimer.monitorTimerId);
        }
      }
      
      this._scheduledTimers.clear();
      this._priorityQueue.length = 0;
      
      // Record successful shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', shutdownResult);

      this.logInfo('AdvancedScheduler shutdown completed', {
        shutdownTime: `${shutdownResult.duration}ms`,
        timersCleared: this._scheduledTimers.size
      });

    } catch (error) {
      // Record failed shutdown timing
      const shutdownResult = shutdownContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', shutdownResult);
      throw this._enhanceErrorContext(error, { operation: 'shutdown' });
    }
  }
  
  // ============================================================================
  // SECTION 3: RECURRING TIMER SCHEDULING WITH RESILIENT TIMING
  // AI Context: "Enterprise recurring timer scheduling with comprehensive features"
  // ============================================================================
  
  public scheduleRecurringTimer(config: IRecurringTimerConfig): string {
    // CONTEXT-BASED TIMING - Create timing context per prompt requirements
    const schedulingContext = this._resilientTimer.start();
    
    try {
      const operationId = this._utilities.generateOperationId();
      const timerId = config.timerId || this._utilities.generateTimerId();
      const compositeId = `${config.serviceId}:${timerId}`;
      
      // Validation
      this._utilities.validateRecurringTimerConfig(config);
      
      // Calculate next execution with jitter if enabled
      const nextExecution = this._utilities.calculateNextExecution(config.schedule);
      
      const scheduledTimer: IScheduledTimer = {
        id: compositeId,
        config,
        executionCount: 0,
        nextExecution,
        status: 'scheduled',
        retryCount: 0,
        errors: [],
        performanceMetrics: {
          averageExecutionTime: 0,
          totalExecutionTime: 0,
          minExecutionTime: Infinity,
          maxExecutionTime: 0,
          successRate: 1.0,
          lastPerformanceUpdate: new Date()
        }
      };
      
      this._scheduledTimers.set(compositeId, scheduledTimer);
      
      // Create monitoring timer using base service
      const monitorId = this._baseTimerService.createCoordinatedInterval(
        () => this._processScheduledTimer(compositeId),
        1000, // Check every second for precision
        'timer-scheduler-enhanced',
        `monitor-${timerId}`
      );
      
      scheduledTimer.monitorTimerId = monitorId;
      
      // Record successful scheduling timing
      const schedulingResult = schedulingContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', schedulingResult);
      
      // Validate performance requirement (<10ms)
      if (schedulingResult.reliable && schedulingResult.duration > PERFORMANCE_REQUIREMENTS.SCHEDULING_MAX_MS) {
        this.logWarning('Recurring timer scheduling exceeded performance requirement', {
          compositeId,
          duration: `${schedulingResult.duration}ms`,
          requirement: `${PERFORMANCE_REQUIREMENTS.SCHEDULING_MAX_MS}ms`
        });
      }

      this.logInfo('Recurring timer scheduled successfully', {
        compositeId,
        schedule: config.schedule,
        maxExecutions: config.maxExecutions,
        priority: config.priority,
        nextExecution: scheduledTimer.nextExecution,
        operationTime: `${schedulingResult.duration}ms`,
        performanceCompliant: schedulingResult.duration <= PERFORMANCE_REQUIREMENTS.SCHEDULING_MAX_MS,
        operationId
      });

      return compositeId;

    } catch (error) {
      // Record failed scheduling timing
      const schedulingResult = schedulingContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', schedulingResult);

      this.logError('Recurring timer scheduling failed', error, {
        serviceId: config.serviceId,
        timerId: config.timerId,
        operationTime: `${schedulingResult.duration}ms`
      });

      throw this._enhanceErrorContext(error, {
        operation: 'scheduleRecurringTimer',
        config
      });
    }
  }
  
  // ============================================================================
  // SECTION 4: CRON TIMER SCHEDULING WITH RESILIENT TIMING
  // AI Context: "Enterprise cron timer scheduling with validation"
  // ============================================================================
  
  public scheduleCronTimer(
    cronExpression: string,
    callback: () => void,
    serviceId: string,
    timerId?: string
  ): string {
    // CONTEXT-BASED TIMING - Create timing context for cron scheduling
    const cronContext = this._resilientTimer.start();
    
    try {
      const operationId = this._utilities.generateOperationId();
      
      // Validate cron expression
      this._utilities.validateCronExpression(cronExpression);
      
      const schedule: ITimerSchedule = {
        type: 'cron',
        value: cronExpression,
        jitterMs: this._config.scheduling.jitterEnabled ? this._config.scheduling.maxJitterMs : 0
      };
      
      const result = this.scheduleRecurringTimer({
        callback,
        schedule,
        serviceId,
        timerId,
        metadata: {
          type: 'cron',
          cronExpression,
          operationId
        }
      });
      
      // Record successful cron scheduling timing
      const cronResult = cronContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', cronResult);
      
      this.logInfo('Cron timer scheduled successfully', {
        cronExpression,
        serviceId,
        timerId: result,
        operationTime: `${cronResult.duration}ms`,
        operationId
      });
      
      return result;
      
    } catch (error) {
      // Record failed cron scheduling timing
      const cronResult = cronContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', cronResult);
      
      this.logError('Cron timer scheduling failed', error, {
        cronExpression,
        serviceId,
        timerId,
        operationTime: `${cronResult.duration}ms`
      });
      
      throw this._enhanceErrorContext(error, {
        operation: 'scheduleCronTimer',
        cronExpression,
        serviceId
      });
    }
  }

  // ============================================================================
  // SECTION 5: CONDITIONAL & PRIORITY SCHEDULING WITH RESILIENT TIMING
  // AI Context: "Enterprise conditional and priority timer scheduling"
  // ============================================================================

  public scheduleConditionalTimer(
    condition: () => boolean,
    callback: () => void,
    checkInterval: number,
    serviceId: string,
    timerId?: string
  ): string {
    // CONTEXT-BASED TIMING - Create timing context for conditional scheduling
    const conditionalContext = this._resilientTimer.start();

    try {
      const operationId = this._utilities.generateOperationId();
      const finalTimerId = timerId || this._utilities.generateTimerId();
      const compositeId = `${serviceId}:${finalTimerId}`;

      // Validation
      this._utilities.validateConditionalTimerPreconditions(condition, checkInterval);

      const conditionalCallback = () => {
        const executionContext = this._resilientTimer.start();
        try {
          if (condition()) {
            callback();
            // Remove the conditional timer after successful execution
            this._baseTimerService.removeCoordinatedTimer(compositeId);

            const executionResult = executionContext.end();
            this._metricsCollector.recordTiming('scheduling_operations', executionResult);

            this.logInfo('Conditional timer condition met and executed', {
              compositeId,
              executionTime: `${executionResult.duration}ms`
            });
          }
        } catch (error) {
          const executionResult = executionContext.end();
          this._metricsCollector.recordTiming('scheduling_operations_failed', executionResult);

          this.logError('Conditional timer error', error, {
            compositeId,
            executionTime: `${executionResult.duration}ms`
          });
        }
      };

      const result = this._baseTimerService.createCoordinatedInterval(
        conditionalCallback,
        checkInterval,
        serviceId,
        finalTimerId
      );

      // Record successful conditional scheduling timing
      const conditionalResult = conditionalContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', conditionalResult);

      this.logInfo('Conditional timer scheduled successfully', {
        compositeId: result,
        checkInterval,
        operationTime: `${conditionalResult.duration}ms`,
        operationId
      });

      return result;

    } catch (error) {
      // Record failed conditional scheduling timing
      const conditionalResult = conditionalContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', conditionalResult);

      this.logError('Conditional timer scheduling failed', error, {
        serviceId,
        timerId,
        checkInterval,
        operationTime: `${conditionalResult.duration}ms`
      });

      throw this._enhanceErrorContext(error, {
        operation: 'scheduleConditionalTimer',
        serviceId,
        checkInterval
      });
    }
  }

  public scheduleDelayedTimer(
    callback: () => void,
    delayMs: number,
    serviceId: string,
    timerId?: string
  ): string {
    // CONTEXT-BASED TIMING - Create timing context for delayed scheduling
    const delayedContext = this._resilientTimer.start();

    try {
      const operationId = this._utilities.generateOperationId();
      const finalTimerId = timerId || this._utilities.generateTimerId();
      const compositeId = `${serviceId}:${finalTimerId}`;

      // Validation
      this._utilities.validateDelayedTimerPreconditions(delayMs);

      const delayedCallback = () => {
        const executionContext = this._resilientTimer.start();
        try {
          callback();
          // Remove the timer after execution (one-time execution)
          this._baseTimerService.removeCoordinatedTimer(compositeId);

          const executionResult = executionContext.end();
          this._metricsCollector.recordTiming('scheduling_operations', executionResult);

          this.logInfo('Delayed timer executed successfully', {
            compositeId,
            executionTime: `${executionResult.duration}ms`
          });
        } catch (error) {
          const executionResult = executionContext.end();
          this._metricsCollector.recordTiming('scheduling_operations_failed', executionResult);

          this.logError('Delayed timer execution error', error, {
            compositeId,
            executionTime: `${executionResult.duration}ms`
          });
        }
      };

      // Use base service for actual timer creation
      const result = this._baseTimerService.createCoordinatedInterval(
        delayedCallback,
        delayMs,
        serviceId,
        finalTimerId
      );

      // Record successful delayed scheduling timing
      const delayedResult = delayedContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', delayedResult);

      this.logInfo('Delayed timer scheduled successfully', {
        compositeId: result,
        delayMs,
        scheduledExecution: new Date(Date.now() + delayMs),
        operationTime: `${delayedResult.duration}ms`,
        operationId
      });

      return result;

    } catch (error) {
      // Record failed delayed scheduling timing
      const delayedResult = delayedContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', delayedResult);

      this.logError('Delayed timer scheduling failed', error, {
        serviceId,
        timerId,
        delayMs,
        operationTime: `${delayedResult.duration}ms`
      });

      throw this._enhanceErrorContext(error, {
        operation: 'scheduleDelayedTimer',
        serviceId,
        delayMs
      });
    }
  }

  public schedulePriorityTimer(
    callback: () => void,
    priority: number,
    intervalMs: number,
    serviceId: string,
    timerId?: string
  ): string {
    // CONTEXT-BASED TIMING - Create timing context for priority scheduling
    const priorityContext = this._resilientTimer.start();

    try {
      const operationId = this._utilities.generateOperationId();
      const finalTimerId = timerId || this._utilities.generateTimerId();
      const compositeId = `${serviceId}:${finalTimerId}`;

      // Validation
      this._utilities.validatePriorityTimerPreconditions(priority, intervalMs);

      // Add to priority queue
      this._priorityQueue.push({
        priority,
        timerId: compositeId,
        callback
      });

      // Sort priority queue (higher priority first)
      this._priorityQueue.sort((a, b) => b.priority - a.priority);

      // Create timer with priority-aware execution
      const priorityCallback = () => {
        const queuePosition = this._priorityQueue.findIndex(item => item.timerId === compositeId);
        if (queuePosition !== -1) {
          const executionContext = this._resilientTimer.start();
          try {
            callback();

            const executionResult = executionContext.end();
            this._metricsCollector.recordTiming('scheduling_operations', executionResult);

            this.logInfo('Priority timer executed', {
              compositeId,
              priority,
              queuePosition,
              executionTime: `${executionResult.duration}ms`
            });
          } catch (error) {
            const executionResult = executionContext.end();
            this._metricsCollector.recordTiming('scheduling_operations_failed', executionResult);

            this.logError('Priority timer execution error', error, {
              compositeId,
              priority,
              queuePosition
            });
          }
        }
      };

      const result = this._baseTimerService.createCoordinatedInterval(
        priorityCallback,
        intervalMs,
        serviceId,
        finalTimerId
      );

      // Record successful priority scheduling timing
      const priorityResult = priorityContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', priorityResult);

      this.logInfo('Priority timer scheduled successfully', {
        compositeId: result,
        priority,
        intervalMs,
        queueSize: this._priorityQueue.length,
        operationTime: `${priorityResult.duration}ms`,
        operationId
      });

      return result;

    } catch (error) {
      // Record failed priority scheduling timing
      const priorityResult = priorityContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', priorityResult);

      this.logError('Priority timer scheduling failed', error, {
        serviceId,
        timerId,
        priority,
        intervalMs,
        operationTime: `${priorityResult.duration}ms`
      });

      throw this._enhanceErrorContext(error, {
        operation: 'schedulePriorityTimer',
        serviceId,
        priority,
        intervalMs
      });
    }
  }

  // ============================================================================
  // SECTION 6: PRIVATE HELPER METHODS WITH ENHANCED ERROR CONTEXT
  // AI Context: "Scheduled timer processing and utility methods"
  // ============================================================================

  private _processScheduledTimer(compositeId: string): void {
    const scheduledTimer = this._scheduledTimers.get(compositeId);
    if (!scheduledTimer || scheduledTimer.status !== 'scheduled') {
      return;
    }

    const now = new Date();
    if (now >= scheduledTimer.nextExecution) {
      // Execute the timer
      this._executeScheduledTimer(scheduledTimer);

      // Calculate next execution if recurring
      if (!scheduledTimer.config.maxExecutions ||
          scheduledTimer.executionCount < scheduledTimer.config.maxExecutions) {
        scheduledTimer.nextExecution = this._utilities.calculateNextExecution(
          scheduledTimer.config.schedule,
          now
        );
      } else {
        scheduledTimer.status = 'completed';
        if (scheduledTimer.config.onComplete) {
          scheduledTimer.config.onComplete();
        }
      }
    }
  }

  private _executeScheduledTimer(scheduledTimer: IScheduledTimer): void {
    // CONTEXT-BASED TIMING - Create timing context for execution
    const executionContext = this._resilientTimer.start();
    scheduledTimer.status = 'running';

    try {
      scheduledTimer.config.callback();
      scheduledTimer.executionCount++;
      scheduledTimer.lastExecution = new Date();
      scheduledTimer.status = 'scheduled';

      // Update performance metrics
      const executionResult = executionContext.end();
      this._metricsCollector.recordTiming('scheduling_operations', executionResult);
      this._utilities.updateTimerPerformanceMetrics(scheduledTimer, executionResult.duration, true);

    } catch (error) {
      const executionResult = executionContext.end();
      this._metricsCollector.recordTiming('scheduling_operations_failed', executionResult);

      scheduledTimer.errors.push(error instanceof Error ? error : new Error(String(error)));
      scheduledTimer.status = 'failed';

      // Update performance metrics
      this._utilities.updateTimerPerformanceMetrics(scheduledTimer, executionResult.duration, false);

      if (scheduledTimer.config.onError) {
        scheduledTimer.config.onError(error instanceof Error ? error : new Error(String(error)));
      }

      this.logError('Scheduled timer execution failed', error, {
        timerId: scheduledTimer.id,
        executionCount: scheduledTimer.executionCount
      });
    }
  }

  private _enhanceErrorContext(error: any, context: any): Error {
    const enhancedError = error instanceof Error ? error : new Error(String(error));
    enhancedError.message = `${enhancedError.message} | Context: ${JSON.stringify(context)}`;
    return enhancedError;
  }

  // ILoggingService implementation
  logInfo(message: string, data?: any): void {
    this._logger.logInfo(message, data);
  }

  logWarning(message: string, data?: any): void {
    this._logger.logWarning(message, data);
  }

  logError(message: string, error?: any, data?: any): void {
    this._logger.logError(message, error, data);
  }

  logDebug(message: string, data?: any): void {
    this._logger.logDebug(message, data);
  }
}
