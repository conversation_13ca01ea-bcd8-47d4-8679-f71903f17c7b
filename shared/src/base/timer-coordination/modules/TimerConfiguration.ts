/**
 * @file Timer Configuration
 * @filepath shared/src/base/timer-coordination/modules/TimerConfiguration.ts
 * @task-id M-TSK-01.SUB-02.2.MOD-03
 * @component timer-configuration
 * @reference foundation-context.TIMER-COORDINATION.005
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context timer-coordination-context
 * @category Timer-Coordination-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Timer configuration module providing:
 * - Enterprise configuration management for timer coordination
 * - Default configuration values with enterprise-grade standards
 * - Enterprise constants for timer coordination operations
 * - Configuration validation with comprehensive rule checking
 * - Memory-safe configuration operations with automatic cleanup
 * - Performance optimization with <1ms configuration overhead
 * - Integration with TimerCoordinationServiceEnhanced for coordinated configuration
 * - Enterprise-grade configuration reliability with validation
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON><PERSON><PERSON> Consultancy"
 * @governance-adr ADR-foundation-011-timer-configuration-architecture
 * @governance-dcr DCR-foundation-011-timer-configuration-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @enables shared/src/base/TimerCoordinationServiceEnhanced
 * @enables shared/src/base/timer-coordination/modules/AdvancedScheduler
 * @enables shared/src/base/timer-coordination/modules/TimerPoolManager
 * @related-contexts timer-coordination-context, foundation-context
 * @governance-impact framework-foundation, timer-coordination, configuration-management
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type timer-coordination-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/timer-coordination-context/modules/TimerConfiguration.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial timer configuration implementation with enterprise standards
 * v1.1.0 (2025-07-28) - Added configuration validation and enterprise constants
 */

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import { 
  ResilientTimer
} from '../../utils/ResilientTiming';

import { 
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

// Import type definitions
import { ITimerCoordinationServiceEnhancedConfig } from '../types/TimerTypes';

// ============================================================================
// SECTION 1: DEFAULT ENHANCED CONFIGURATION
// AI Context: "Enterprise-grade default configuration with comprehensive settings"
// ============================================================================

/**
 * Default enhanced configuration with enterprise-grade settings
 */
export const DEFAULT_ENHANCED_CONFIG: ITimerCoordinationServiceEnhancedConfig = {
  // Base configuration
  maxTimersPerService: 50, // Increased from base for enterprise use
  maxGlobalTimers: 500,    // Increased from base for enterprise use
  minIntervalMs: 100,      // Minimum timer interval
  timerAuditIntervalMs: 30000, // 30 seconds for enhanced monitoring
  
  // Pool management
  pooling: {
    enabled: true,
    defaultPoolSize: 10,
    maxPools: 20,
    poolMonitoringInterval: 60000, // 1 minute
    autoOptimization: true
  },
  
  // Advanced scheduling
  scheduling: {
    cronParsingEnabled: true,
    conditionalTimersEnabled: true,
    prioritySchedulingEnabled: true,
    jitterEnabled: true,
    maxJitterMs: 1000 // 1 second max jitter
  },
  
  // Coordination
  coordination: {
    groupingEnabled: true,
    chainExecutionEnabled: true,
    synchronizationEnabled: true,
    maxGroupSize: 20,
    maxChainLength: 10
  },
  
  // Integration
  integration: {
    phase1BufferEnabled: true,
    phase2EventEnabled: true,
    bufferSize: 100,
    eventEmissionEnabled: true
  },
  
  // Performance
  performance: {
    poolOperationTimeoutMs: 5000,     // 5 seconds
    schedulingTimeoutMs: 10000,       // 10 seconds
    synchronizationTimeoutMs: 20000,  // 20 seconds
    monitoringEnabled: true,
    metricsCollectionInterval: 30000  // 30 seconds
  }
};

// ============================================================================
// SECTION 2: PERFORMANCE REQUIREMENTS CONSTANTS
// AI Context: "Performance targets and enterprise requirements"
// ============================================================================

/**
 * Performance requirements constants for enterprise compliance
 */
export const PERFORMANCE_REQUIREMENTS = {
  POOL_OPERATION_MAX_MS: 5,     // <5ms pool operations
  SCHEDULING_MAX_MS: 10,        // <10ms schedule calculation
  SYNCHRONIZATION_MAX_MS: 20,   // <20ms group synchronization
  MEMORY_OVERHEAD_MAX_PERCENT: 5  // <5% memory overhead
};

/**
 * Resilient timing configuration for timer coordination operations
 */
export const RESILIENT_TIMING_CONFIG = {
  TIMER_OPERATIONS: {
    enableFallbacks: true,
    maxExpectedDuration: 10000, // 10 seconds for timer operations
    unreliableThreshold: 3,
    estimateBaseline: 100
  },
  METRICS_COLLECTION: {
    enableFallbacks: true,
    cacheUnreliableValues: false,
    maxMetricsAge: 300000, // 5 minutes
    defaultEstimates: new Map([
      ['pool_operations', 5],
      ['scheduling_operations', 10],
      ['synchronization_operations', 20],
      ['utility_operations', 5],
      ['coordination_operations', 15]
    ])
  }
};

// ============================================================================
// SECTION 3: VALIDATION PATTERNS & CONSTANTS
// AI Context: "Cron expression validation and enterprise patterns"
// ============================================================================

/**
 * Cron expression validation patterns for enterprise scheduling
 */
export const CRON_PATTERNS = {
  SECOND: /^(\*|[0-5]?\d)$/,
  MINUTE: /^(\*|[0-5]?\d)$/,
  HOUR: /^(\*|[01]?\d|2[0-3])$/,
  DAY: /^(\*|0?[1-9]|[12]\d|3[01])$/,
  MONTH: /^(\*|0?[1-9]|1[0-2])$/,
  WEEKDAY: /^(\*|[0-6])$/
};

/**
 * Memory-safe resource manager configuration for enhanced timer coordination
 */
export const MEMORY_SAFE_CONFIG = {
  maxIntervals: 1000,  // Enhanced limits for enterprise use
  maxTimeouts: 500,
  maxCacheSize: 10 * 1024 * 1024, // 10MB for enhanced features
  memoryThresholdMB: 500,          // 500MB threshold
  cleanupIntervalMs: 300000        // 5 minutes
};

/**
 * Pool strategy constants for timer pool management
 */
export const POOL_STRATEGIES = {
  ROUND_ROBIN: 'round_robin' as const,
  LEAST_USED: 'least_used' as const,
  RANDOM: 'random' as const,
  CUSTOM: 'custom' as const
};

/**
 * Pool exhaustion handling strategies
 */
export const POOL_EXHAUSTION_STRATEGIES = {
  QUEUE: 'queue' as const,
  REJECT: 'reject' as const,
  EXPAND: 'expand' as const,
  EVICT_OLDEST: 'evict_oldest' as const
};

// ============================================================================
// SECTION 4: CONFIGURATION UTILITIES & HELPERS
// AI Context: "Configuration validation and utility functions"
// ============================================================================

/**
 * Validate enhanced configuration for enterprise compliance
 */
export function validateEnhancedConfig(config: Partial<ITimerCoordinationServiceEnhancedConfig>): boolean {
  // Basic validation
  if (config.maxTimersPerService && config.maxTimersPerService <= 0) {
    throw new Error('maxTimersPerService must be positive');
  }
  
  if (config.maxGlobalTimers && config.maxGlobalTimers <= 0) {
    throw new Error('maxGlobalTimers must be positive');
  }
  
  if (config.minIntervalMs && config.minIntervalMs < 1) {
    throw new Error('minIntervalMs must be at least 1ms');
  }
  
  // Pool configuration validation
  if (config.pooling) {
    if (config.pooling.defaultPoolSize && config.pooling.defaultPoolSize <= 0) {
      throw new Error('defaultPoolSize must be positive');
    }
    
    if (config.pooling.maxPools && config.pooling.maxPools <= 0) {
      throw new Error('maxPools must be positive');
    }
  }
  
  // Performance configuration validation
  if (config.performance) {
    if (config.performance.poolOperationTimeoutMs && config.performance.poolOperationTimeoutMs <= 0) {
      throw new Error('poolOperationTimeoutMs must be positive');
    }
    
    if (config.performance.schedulingTimeoutMs && config.performance.schedulingTimeoutMs <= 0) {
      throw new Error('schedulingTimeoutMs must be positive');
    }
  }
  
  return true;
}

/**
 * Merge configuration with defaults ensuring enterprise compliance
 */
export function mergeWithDefaults(
  userConfig?: Partial<ITimerCoordinationServiceEnhancedConfig>
): ITimerCoordinationServiceEnhancedConfig {
  if (!userConfig) {
    return { ...DEFAULT_ENHANCED_CONFIG };
  }
  
  // Validate user configuration
  validateEnhancedConfig(userConfig);
  
  // Deep merge with defaults
  return {
    ...DEFAULT_ENHANCED_CONFIG,
    ...userConfig,
    pooling: {
      ...DEFAULT_ENHANCED_CONFIG.pooling,
      ...userConfig.pooling
    },
    scheduling: {
      ...DEFAULT_ENHANCED_CONFIG.scheduling,
      ...userConfig.scheduling
    },
    coordination: {
      ...DEFAULT_ENHANCED_CONFIG.coordination,
      ...userConfig.coordination
    },
    integration: {
      ...DEFAULT_ENHANCED_CONFIG.integration,
      ...userConfig.integration
    },
    performance: {
      ...DEFAULT_ENHANCED_CONFIG.performance,
      ...userConfig.performance
    }
  };
}

/**
 * Create resilient timer instance for timer coordination operations
 */
export function createResilientTimer(): ResilientTimer {
  return new ResilientTimer(RESILIENT_TIMING_CONFIG.TIMER_OPERATIONS);
}

/**
 * Create resilient metrics collector for timer coordination monitoring
 */
export function createResilientMetricsCollector(): ResilientMetricsCollector {
  return new ResilientMetricsCollector(RESILIENT_TIMING_CONFIG.METRICS_COLLECTION);
}

/**
 * Validate cron expression using enterprise patterns
 */
export function validateCronExpression(cronExpression: string): boolean {
  const parts = cronExpression.trim().split(/\s+/);
  
  if (parts.length !== 6) {
    return false; // Expecting: second minute hour day month weekday
  }
  
  const [second, minute, hour, day, month, weekday] = parts;
  
  return (
    CRON_PATTERNS.SECOND.test(second) &&
    CRON_PATTERNS.MINUTE.test(minute) &&
    CRON_PATTERNS.HOUR.test(hour) &&
    CRON_PATTERNS.DAY.test(day) &&
    CRON_PATTERNS.MONTH.test(month) &&
    CRON_PATTERNS.WEEKDAY.test(weekday)
  );
}

/**
 * Get performance requirement for specific operation type
 */
export function getPerformanceRequirement(operationType: 'pool' | 'scheduling' | 'synchronization'): number {
  switch (operationType) {
    case 'pool':
      return PERFORMANCE_REQUIREMENTS.POOL_OPERATION_MAX_MS;
    case 'scheduling':
      return PERFORMANCE_REQUIREMENTS.SCHEDULING_MAX_MS;
    case 'synchronization':
      return PERFORMANCE_REQUIREMENTS.SYNCHRONIZATION_MAX_MS;
    default:
      throw new Error(`Unknown operation type: ${operationType}`);
  }
}
