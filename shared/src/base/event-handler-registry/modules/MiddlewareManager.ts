/**
 * @file Middleware Manager
 * @filepath shared/src/base/event-handler-registry/modules/MiddlewareManager.ts
 * @task-id M-TSK-01.SUB-01.2.ENH-02
 * @component middleware-manager
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety
 * @created 2025-07-27 18:30:00 +03
 * @modified 2025-09-04 16:30:00 +03
 *
 * @description
 * Enterprise-grade middleware management module for EventHandlerRegistryEnhanced providing:
 * - Priority-based middleware execution with resilient timing integration and performance monitoring
 * - Complex middleware chain management with enterprise-grade error handling and recovery
 * - Handler execution with middleware hooks and comprehensive timing measurement
 * - Memory-safe resource management with automatic cleanup and boundary enforcement
 * - Anti-Simplification Policy compliance with comprehensive middleware coverage
 * - Integration with ResilientTimer and ResilientMetricsCollector for enterprise standards
 * - Foundation module supporting event handler registry middleware across framework
 * - Production-ready middleware operations with comprehensive execution capabilities
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level module-authority
 * @authority-validator "President & CEO, E<PERSON><PERSON><PERSON> Consultancy"
 * @governance-adr ADR-foundation-002-event-registry-architecture
 * @governance-dcr DCR-foundation-002-event-registry-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/utils/ResilientTiming
 * @depends-on shared/src/base/utils/ResilientMetrics
 * @depends-on shared/src/base/event-handler-registry/types/EventTypes
 * @enables shared/src/base/event-handler-registry/EventHandlerRegistryEnhanced
 * @enables server/src/platform/tracking/core-trackers/EventTrackingService
 * @related-contexts foundation-context, memory-safety-context
 * @governance-impact framework-foundation, event-registry-middleware
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-module
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/contexts/foundation-context/modules/middleware-manager.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-27) - Initial implementation with core middleware management functionality
 * v1.1.0 (2025-09-04) - Enhanced with standardized header and improved governance compliance
 */

import { MemorySafeResourceManager } from '../../MemorySafeResourceManager';
import { ResilientTimer, IResilientTimingResult } from '../../utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../utils/ResilientMetrics';
import {
  IHandlerMiddleware,
  IRegisteredHandler,
  IHandlerResult
} from '../types/EventTypes';

// ============================================================================
// SECTION 1: MIDDLEWARE MANAGER CLASS (Lines 1-100)
// AI Context: "Core middleware management with resilient timing integration"
// ============================================================================

export interface IMiddlewareManagerConfig {
  maxMiddleware?: number;
  enableTiming?: boolean;
  timeoutMs?: number;
}

export class MiddlewareManager extends MemorySafeResourceManager {
  // ✅ RESILIENT TIMING: Infrastructure for enterprise-grade timing
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  // Middleware storage and configuration
  private _middleware: IHandlerMiddleware[] = [];
  private readonly _config: Required<IMiddlewareManagerConfig>;

  // Middleware metrics
  private _middlewareMetrics = {
    totalExecutions: 0,
    successfulExecutions: 0,
    failedExecutions: 0,
    skippedExecutions: 0,
    averageExecutionTime: 0,
    totalMiddleware: 0
  };

  constructor(config: IMiddlewareManagerConfig = {}) {
    super({
      maxIntervals: 2,
      maxTimeouts: 5,
      maxCacheSize: 1024 * 1024, // 1MB
      memoryThresholdMB: 10,
      cleanupIntervalMs: 300000 // 5 minutes
    });

    this._config = {
      maxMiddleware: config.maxMiddleware || 10,
      enableTiming: config.enableTiming ?? true,
      timeoutMs: config.timeoutMs || 5000
    };
  }

  /**
   * Public initialize method for external use
   */
  public async initialize(): Promise<void> {
    await super.initialize();
  }

  /**
   * Public shutdown method for external use
   */
  public async shutdown(): Promise<void> {
    await super.shutdown();
  }

  protected async doInitialize(): Promise<void> {
    // ✅ RESILIENT TIMING: Initialize timing infrastructure
    this._resilientTimer = new ResilientTimer({
      enableFallbacks: true,
      maxExpectedDuration: this._config.timeoutMs,
      unreliableThreshold: 3,
      estimateBaseline: 2 // 2ms baseline for middleware
    });

    this._metricsCollector = new ResilientMetricsCollector({
      enableFallbacks: true,
      cacheUnreliableValues: false,
      maxMetricsAge: 300000, // 5 minutes
      defaultEstimates: new Map([
        ['middlewareExecution', 2],
        ['middlewareChain', 5],
        ['handlerWithMiddleware', 10]
      ])
    });
  }

  protected async doShutdown(): Promise<void> {
    // Clear middleware and reset metrics
    this._middleware = [];
    this._middlewareMetrics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      skippedExecutions: 0,
      averageExecutionTime: 0,
      totalMiddleware: 0
    };
  }

  // ============================================================================
  // SECTION 2: MIDDLEWARE MANAGEMENT (Lines 101-150)
  // AI Context: "Middleware registration and management"
  // ============================================================================

  /**
   * ✅ RESILIENT TIMING: Add middleware with timing validation
   */
  public addMiddleware(middleware: IHandlerMiddleware): void {
    if (this._middleware.length >= this._config.maxMiddleware) {
      throw new Error(`Maximum middleware limit reached: ${this._config.maxMiddleware}`);
    }

    // Validate middleware
    if (!middleware.name || typeof middleware.name !== 'string') {
      throw new Error('Invalid middleware: must have a valid name');
    }

    if (typeof middleware.priority !== 'number') {
      throw new Error('Invalid middleware: must have a numeric priority');
    }

    // Check for duplicate names
    if (this._middleware.some(m => m.name === middleware.name)) {
      throw new Error(`Middleware with name '${middleware.name}' already exists`);
    }

    this._middleware.push(middleware);
    this._middleware.sort((a, b) => (b.priority || 0) - (a.priority || 0));
    this._middlewareMetrics.totalMiddleware = this._middleware.length;
  }

  /**
   * Remove middleware by name
   */
  public removeMiddleware(name: string): boolean {
    const initialLength = this._middleware.length;
    this._middleware = this._middleware.filter(m => m.name !== name);
    this._middlewareMetrics.totalMiddleware = this._middleware.length;
    return this._middleware.length < initialLength;
  }

  /**
   * Get all middleware (readonly)
   */
  public getMiddleware(): readonly IHandlerMiddleware[] {
    return [...this._middleware];
  }

  /**
   * Get middleware by name
   */
  public getMiddlewareByName(name: string): IHandlerMiddleware | undefined {
    return this._middleware.find(m => m.name === name);
  }

  /**
   * Clear all middleware
   */
  public clearAllMiddleware(): void {
    this._middleware = [];
    this._middlewareMetrics.totalMiddleware = 0;
  }

  // ============================================================================
  // SECTION 3: MIDDLEWARE EXECUTION (Lines 151-250)
  // AI Context: "Middleware chain execution with resilient timing"
  // ============================================================================

  /**
   * ✅ RESILIENT TIMING: Execute handler with middleware chain
   */
  public async executeHandlerWithMiddleware(
    handler: IRegisteredHandler,
    data: unknown,
    eventType: string
  ): Promise<IHandlerResult> {
    const chainContext = this._resilientTimer.start();
    
    try {
      this._middlewareMetrics.totalExecutions++;

      // If no middleware, execute handler directly
      if (this._middleware.length === 0) {
        const result = await this._executeHandlerDirect(handler, data, eventType);
        const timing = chainContext.end();
        this._metricsCollector.recordTiming('handlerWithoutMiddleware', timing);
        return result;
      }

      // Execute middleware chain
      const result = await this._executeMiddlewareChain(handler, data, eventType);

      const timing = chainContext.end();
      this._metricsCollector.recordTiming('middlewareChain', timing);

      // Update metrics - only count as successful if not skipped
      this._updateExecutionMetrics(timing.duration, true);
      if (!result.skippedByMiddleware) {
        this._middlewareMetrics.successfulExecutions++;
      }

      return result;
    } catch (error) {
      const timing = chainContext.end();
      this._metricsCollector.recordTiming('middlewareChainError', timing);
      
      this._updateExecutionMetrics(timing.duration, false);
      this._middlewareMetrics.failedExecutions++;
      
      throw error;
    }
  }

  // ============================================================================
  // SECTION 4: METRICS AND MONITORING (Lines 251-300)
  // AI Context: "Middleware metrics and performance monitoring"
  // ============================================================================

  /**
   * Get middleware metrics
   */
  public getMiddlewareMetrics() {
    return {
      ...this._middlewareMetrics,
      metricsSnapshot: this._metricsCollector.createSnapshot()
    };
  }

  /**
   * Reset middleware metrics
   */
  public resetMiddlewareMetrics(): void {
    this._middlewareMetrics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      skippedExecutions: 0,
      averageExecutionTime: 0,
      totalMiddleware: this._middleware.length
    };
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async _executeHandlerDirect(
    handler: IRegisteredHandler,
    data: unknown,
    eventType: string
  ): Promise<IHandlerResult> {
    const handlerContext = this._resilientTimer.start();
    
    try {
      const result = await handler.callback(data, {
        eventType,
        clientId: handler.clientId,
        timestamp: new Date(),
        metadata: handler.metadata
      });
      
      const timing = handlerContext.end();
      
      return {
        handlerId: handler.id,
        clientId: handler.clientId,
        result,
        executionTime: timing.duration,
        success: true,
        skippedByMiddleware: undefined
      };
    } catch (error) {
      const timing = handlerContext.end();
      
      return {
        handlerId: handler.id,
        clientId: handler.clientId,
        result: error,
        executionTime: timing.duration,
        success: false,
        skippedByMiddleware: undefined
      };
    }
  }

  private async _executeMiddlewareChain(
    handler: IRegisteredHandler,
    data: unknown,
    eventType: string
  ): Promise<IHandlerResult> {
    const context = {
      handlerId: handler.id,
      clientId: handler.clientId,
      eventType,
      eventData: data,
      timestamp: new Date(),
      metadata: handler.metadata || {},
      executionAttempt: 1
    };

    let skippedByMiddleware: string | undefined;

    // Execute before-handler middleware in priority order (highest priority first)
    for (const middleware of this._middleware) {
      if (middleware.beforeHandlerExecution) {
        try {
          const shouldContinue = await middleware.beforeHandlerExecution(context);
          if (!shouldContinue) {
            skippedByMiddleware = middleware.name;
            this._middlewareMetrics.skippedExecutions++;
            return {
              handlerId: handler.id,
              clientId: handler.clientId,
              result: undefined,
              executionTime: 0,
              success: true,
              skippedByMiddleware
            };
          }
        } catch (error) {
          // Middleware error handling - continue execution unless error handler says otherwise
          if (middleware.onHandlerError) {
            const handled = await middleware.onHandlerError(context, error instanceof Error ? error : new Error(String(error)));
            if (handled) continue;
          }
          throw error;
        }
      }
    }

    // Execute the actual handler
    let handlerResult: IHandlerResult = await this._executeHandlerDirect(handler, data, eventType);
    
    // ✅ CRITICAL FIX: Check if handler failed and let middleware handle the error
    if (!handlerResult.success && handlerResult.result) {
      let handled = false;
      const error = handlerResult.result instanceof Error ? handlerResult.result : new Error(String(handlerResult.result));
      
      for (const middleware of this._middleware) {
        if (middleware.onHandlerError) {
          try {
            const wasHandled = await middleware.onHandlerError(context, error);
            if (wasHandled) {
              handled = true;
              // ✅ CRITICAL FIX: Create proper success result when error is handled
              handlerResult = {
                handlerId: handler.id,
                clientId: handler.clientId,
                result: undefined,
                executionTime: handlerResult.executionTime,
                success: true // Mark as successful since error was handled
              };
              break;
            }
          } catch (middlewareError) {
            // Middleware error handling failed, continue to next middleware
            continue;
          }
        }
      }
      
      // If no middleware handled the error, keep the failed result as-is
      if (!handled) {
        // handlerResult remains as the failed result from _executeHandlerDirect
      }
    }

    // Execute after-handler middleware
    for (const middleware of this._middleware) {
      if (middleware.afterHandlerExecution && handlerResult!.success) {
        try {
          await middleware.afterHandlerExecution(context, handlerResult!.result);
        } catch (error) {
          // After-handler middleware errors don't affect the result
          console.warn(`After-handler middleware ${middleware.name} failed:`, error);
        }
      }
    }

    return handlerResult!;
  }

  private _updateExecutionMetrics(duration: number, _success: boolean): void {
    const totalExecutions = this._middlewareMetrics.totalExecutions;
    const currentAverage = this._middlewareMetrics.averageExecutionTime;
    
    // Update rolling average
    this._middlewareMetrics.averageExecutionTime = 
      (currentAverage * (totalExecutions - 1) + duration) / totalExecutions;
  }
}
