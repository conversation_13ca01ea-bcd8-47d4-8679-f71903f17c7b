/**
 * @file Event Handler Enhanced Types
 * @filepath shared/src/base/event-handler-registry/types/EventHandlerEnhancedTypes.ts
 * @task-id M-TSK-01.SUB-01.1.TYP-01
 * @component event-handler-enhanced-types
 * @reference foundation-context.TYPES.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context foundation-context
 * @category Event-Processing-Types
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Type definitions for EventHandlerRegistryEnhanced providing:
 * - Event emission system interfaces and types
 * - Middleware system type definitions with execution contexts
 * - Handler deduplication strategy types and configurations
 * - Event buffering and queuing type definitions
 * - Error handling and classification type structures
 * - Configuration interfaces for enhanced event processing
 * - Performance optimization type definitions
 * - Enterprise-grade type safety for event processing components
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-emission-types
 * @governance-dcr DCR-foundation-002-event-emission-types-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/EventHandlerRegistry
 * @enables shared/src/base/EventHandlerRegistryEnhanced
 * @enables shared/src/base/event-handler-registry/modules/*
 * @related-contexts foundation-context, event-processing-context, type-definitions-context
 * @governance-impact framework-foundation, type-safety, event-processing-types
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type event-processing-type-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/event-processing-context/types/EventHandlerEnhancedTypes.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial type definitions extracted from main implementation
 * v1.1.0 (2025-07-28) - Added comprehensive interface definitions for enhanced features
 */

// Local type definition
type EventHandlerCallback = (
  event: unknown,
  context?: {
    eventType: string;
    clientId: string;
    timestamp: Date;
  }
) => unknown | Promise<unknown>;

// ============================================================================
// EVENT EMISSION SYSTEM INTERFACES
// ============================================================================

export interface IEventEmissionSystem {
  emitEvent(eventType: string, data: unknown, options?: IEmissionOptions): Promise<IEmissionResult>;
  emitEventToClient(clientId: string, eventType: string, data: unknown): Promise<IClientEmissionResult>;
  emitEventBatch(events: IEventBatch[]): Promise<IBatchEmissionResult>;
  emitEventWithTimeout(eventType: string, data: unknown, timeoutMs: number): Promise<IEmissionResult>;
}

export interface IEmissionOptions {
  targetClients?: string[];
  excludeClients?: string[];
  priority?: 'low' | 'normal' | 'high' | 'critical';
  timeout?: number;
  requireAcknowledgment?: boolean;
  retryPolicy?: IRetryPolicy;
}

export interface IEmissionResult {
  eventId: string;
  eventType: string;
  targetHandlers: number;
  successfulHandlers: number;
  failedHandlers: number;
  executionTime: number;
  handlerResults: IHandlerResult[];
  errors: IHandlerError[];
}

export interface IHandlerResult {
  handlerId: string;
  clientId: string;
  result: unknown;
  executionTime: number;
  success: boolean;
  skippedByMiddleware?: string;
}

export interface IHandlerError {
  handlerId: string;
  clientId: string;
  error: Error;
  timestamp: Date;
}

export interface IClientEmissionResult extends IEmissionResult {
  targetClientId: string;
}

export interface IEventBatch {
  eventType: string;
  data: unknown;
  options?: IEmissionOptions;
}

export interface IBatchEmissionResult {
  batchId: string;
  totalEvents: number;
  successfulEvents: number;
  failedEvents: number;
  executionTime: number;
  results: IEmissionResult[];
}

// ============================================================================
// RETRY & ERROR HANDLING INTERFACES
// ============================================================================

export interface IRetryPolicy {
  maxRetries: number;
  retryDelayMs: number;
  backoffMultiplier: number;
  maxBackoffDelayMs?: number;
  retryableErrorTypes?: string[];
  nonRetryableErrorTypes?: string[];
}

export interface IErrorClassification {
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  retryable: boolean;
}

export interface IEventPriorityContext {
  eventType: string;
  data: unknown;
  options: IEmissionOptions;
  timestamp: Date;
  systemLoad: ISystemLoad;
  queueDepth: number;
  targetHandlerCount: number;
}

export interface ISystemLoad {
  memoryUtilization: number;
  cpuUtilization: number;
  eventQueueDepth: number;
  activeHandlers: number;
}

// ============================================================================
// MIDDLEWARE SYSTEM INTERFACES
// ============================================================================

export interface IHandlerMiddleware {
  name: string;
  priority: number; // Higher priority executes first
  beforeHandlerExecution?(context: IHandlerExecutionContext): Promise<boolean>; // false = skip handler
  afterHandlerExecution?(context: IHandlerExecutionContext, result: unknown): Promise<void>;
  onHandlerError?(context: IHandlerExecutionContext, error: Error): Promise<boolean>; // true = error handled
}

export interface IHandlerExecutionContext {
  handlerId: string;
  clientId: string;
  eventType: string;
  eventData: unknown;
  timestamp: Date;
  metadata: Record<string, unknown>;
  executionAttempt: number;
}

// ============================================================================
// DEDUPLICATION INTERFACES
// ============================================================================

export interface IHandlerDeduplication {
  enabled: boolean;
  strategy: 'signature' | 'reference' | 'custom';
  customDeduplicationFn?: (handler1: EventHandlerCallback, handler2: EventHandlerCallback) => boolean;
  autoMergeMetadata: boolean;
  onDuplicateDetected?: (existing: any, duplicate: any) => void;
}

// ============================================================================
// BUFFERING INTERFACES
// ============================================================================

export interface IEventBuffering {
  enabled: boolean;
  bufferSize: number;
  flushInterval: number; // milliseconds
  bufferStrategy: 'fifo' | 'lifo' | 'priority' | 'time_window';
  priorityFn?: (context: IEventPriorityContext) => number;
  autoFlushThreshold: number; // 0.0-1.0, flush when buffer is X% full
  onBufferOverflow: 'drop_oldest' | 'drop_newest' | 'force_flush' | 'error';
  deadLetterQueueHandler?: (event: any) => Promise<void>;
}

// ============================================================================
// CONFIGURATION INTERFACES
// ============================================================================

export interface IEventHandlerRegistryEnhancedConfig {
  deduplication?: IHandlerDeduplication;
  buffering?: IEventBuffering;
  maxMiddleware?: number;
  emissionTimeoutMs?: number;
}
