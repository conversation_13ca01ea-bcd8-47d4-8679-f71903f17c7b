/**
 * @file Event Configuration
 * @filepath shared/src/base/event-handler-registry/types/EventConfiguration.ts
 * @task-id M-TSK-01.SUB-01.1.ENH-02
 * @component event-configuration
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety
 * @created 2025-01-27 12:15:00 +03
 * @modified 2025-09-04 19:15:00 +03
 *
 * @description
 * Enterprise-grade event configuration module for EventHandlerRegistryEnhanced providing:
 * - Centralized configuration management with resilient timing integration and validation
 * - Default configuration values for all subsystems with enterprise-grade defaults
 * - Factory functions for creating configuration objects with comprehensive validation
 * - Configuration integrity validation with error handling and recovery mechanisms
 * - Memory-safe configuration management with automatic cleanup and boundary enforcement
 * - Anti-Simplification Policy compliance with comprehensive configuration coverage
 * - Integration with EventTypes for type-safe configuration management
 * - Foundation configuration supporting event handler registry across framework
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level module-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-002-event-registry-architecture
 * @governance-dcr DCR-foundation-002-event-registry-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/event-handler-registry/types/EventTypes
 * @enables shared/src/base/event-handler-registry/EventHandlerRegistryEnhanced
 * @enables shared/src/base/event-handler-registry/modules/EventEmissionSystem
 * @enables shared/src/base/event-handler-registry/modules/EventBuffering
 * @related-contexts foundation-context, memory-safety-context
 * @governance-impact framework-foundation, event-registry-configuration
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-configuration
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/contexts/foundation-context/types/event-configuration.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-01-27) - Initial implementation with core event configuration functionality
 * v1.1.0 (2025-09-04) - Enhanced with standardized header and improved governance compliance
 */

import {
  IEventHandlerRegistryEnhancedConfig,
  IHandlerDeduplication,
  IEventBuffering,
  IRetryPolicy,
  IEmissionOptions
} from './EventTypes';

// ============================================================================
// SECTION 1: DEFAULT CONFIGURATION VALUES (Lines 1-120)
// AI Context: "Enterprise-grade default configuration with resilient timing"
// ============================================================================

/**
 * ✅ RESILIENT TIMING: Default configuration with enterprise-grade resilient timing
 */
export const DEFAULT_RESILIENT_TIMING_CONFIG = {
  enableFallbacks: true as const,
  maxExpectedDuration: 10000, // 10 seconds for event operations
  unreliableThreshold: 3, // 3 consecutive failures = unreliable
  estimateBaseline: 5, // 5ms baseline estimate for event operations
  enableDetailedLogging: process.env.NODE_ENV !== 'production'
} as const;

/**
 * Default deduplication configuration
 */
export const DEFAULT_DEDUPLICATION_CONFIG: IHandlerDeduplication = {
  enabled: false,
  strategy: 'signature',
  autoMergeMetadata: true
} as const;

/**
 * Default buffering configuration
 */
export const DEFAULT_BUFFERING_CONFIG: IEventBuffering = {
  enabled: false,
  bufferSize: 1000,
  flushInterval: 100, // 100ms
  bufferStrategy: 'fifo',
  autoFlushThreshold: 0.8, // Flush when 80% full
  onBufferOverflow: 'drop_oldest'
} as const;

/**
 * Default retry policy configuration
 */
export const DEFAULT_RETRY_POLICY: IRetryPolicy = {
  maxRetries: 3,
  retryDelayMs: 1000,
  backoffMultiplier: 2,
  maxBackoffDelayMs: 10000,
  retryableErrorTypes: ['NetworkError', 'TimeoutError', 'TemporaryError'],
  nonRetryableErrorTypes: ['ValidationError', 'AuthenticationError', 'AuthorizationError']
} as const;

/**
 * Default emission options
 */
export const DEFAULT_EMISSION_OPTIONS: IEmissionOptions = {
  priority: 'normal',
  timeout: 5000, // 5 seconds
  requireAcknowledgment: false
} as const;

/**
 * Default enhanced registry configuration
 */
export const DEFAULT_ENHANCED_CONFIG: IEventHandlerRegistryEnhancedConfig = {
  deduplication: DEFAULT_DEDUPLICATION_CONFIG,
  maxMiddleware: 10,
  emissionTimeoutMs: 10000, // 10 seconds
  resilientTiming: DEFAULT_RESILIENT_TIMING_CONFIG
} as const;

/**
 * Performance configuration constants
 */
export const PERFORMANCE_THRESHOLDS = {
  EMISSION_MAX_DURATION_MS: 10, // <10ms emission for <100 handlers
  MIDDLEWARE_MAX_DURATION_MS: 2, // <2ms middleware processing
  DEDUPLICATION_MAX_DURATION_MS: 1, // <1ms deduplication
  BUFFERING_FLUSH_MAX_DURATION_MS: 5, // <5ms buffer flush
  BATCH_EMISSION_MAX_DURATION_MS: 50, // <50ms for batch emission
  TIMING_RELIABILITY_THRESHOLD: 0.8 // >0.8 reliability score required
} as const;

/**
 * Default middleware configuration
 */
export const DEFAULT_MIDDLEWARE_CONFIG = {
  MAX_MIDDLEWARE_COUNT: 10,
  PRIORITY_MIN: 0,
  PRIORITY_MAX: 1000,
  EXECUTION_TIMEOUT_MS: 5000 // 5 seconds max for middleware execution
} as const;

/**
 * Memory safety configuration
 */
export const MEMORY_SAFETY_LIMITS = {
  MAX_INTERVALS: 10,
  MAX_TIMEOUTS: 20,
  MAX_CACHE_SIZE: 2 * 1024 * 1024, // 2MB
  MEMORY_THRESHOLD_MB: 100,
  CLEANUP_INTERVAL_MS: 300000 // 5 minutes
} as const;

// ============================================================================
// SECTION 2: CONFIGURATION FACTORY FUNCTIONS (Lines 121-220)
// AI Context: "Factory functions for creating and merging configurations"
// ============================================================================

/**
 * ✅ RESILIENT TIMING: Create enhanced configuration with resilient timing integration
 */
export function createEnhancedConfig(
  partial?: Partial<IEventHandlerRegistryEnhancedConfig>
): IEventHandlerRegistryEnhancedConfig {
  return {
    ...DEFAULT_ENHANCED_CONFIG,
    ...partial,
    resilientTiming: {
      ...DEFAULT_RESILIENT_TIMING_CONFIG,
      ...partial?.resilientTiming
    },
    deduplication: {
      ...DEFAULT_DEDUPLICATION_CONFIG,
      ...partial?.deduplication
    },
    buffering: partial?.buffering ? {
      ...DEFAULT_BUFFERING_CONFIG,
      ...partial.buffering
    } : undefined
  };
}

/**
 * Create deduplication configuration with validation
 */
export function createDeduplicationConfig(
  partial?: Partial<IHandlerDeduplication>
): IHandlerDeduplication {
  const config = {
    ...DEFAULT_DEDUPLICATION_CONFIG,
    ...partial
  };

  // Validate configuration
  if (config.enabled && config.strategy === 'custom' && !config.customDeduplicationFn) {
    throw new Error('Custom deduplication function required when strategy is "custom"');
  }

  return config;
}

/**
 * Create buffering configuration with validation
 */
export function createBufferingConfig(
  partial?: Partial<IEventBuffering>
): IEventBuffering {
  const config = {
    ...DEFAULT_BUFFERING_CONFIG,
    ...partial
  };

  // Validate configuration
  if (config.enabled) {
    if (config.bufferSize <= 0) {
      throw new Error('Buffer size must be greater than 0');
    }
    if (config.flushInterval <= 0) {
      throw new Error('Flush interval must be greater than 0');
    }
    if (config.autoFlushThreshold < 0 || config.autoFlushThreshold > 1) {
      throw new Error('Auto flush threshold must be between 0 and 1');
    }
  }

  return config;
}

/**
 * Create retry policy configuration with validation
 */
export function createRetryPolicy(
  partial?: Partial<IRetryPolicy>
): IRetryPolicy {
  const config = {
    ...DEFAULT_RETRY_POLICY,
    ...partial
  };

  // Validate configuration
  if (config.maxRetries < 0) {
    throw new Error('Max retries must be non-negative');
  }
  if (config.retryDelayMs <= 0) {
    throw new Error('Retry delay must be greater than 0');
  }
  if (config.backoffMultiplier <= 0) {
    throw new Error('Backoff multiplier must be greater than 0');
  }

  return config;
}

/**
 * ✅ RESILIENT TIMING: Create emission options with resilient timing considerations
 */
export function createEmissionOptions(
  partial?: Partial<IEmissionOptions>
): IEmissionOptions {
  const options = {
    ...DEFAULT_EMISSION_OPTIONS,
    ...partial
  };

  // ✅ RESILIENT TIMING: Adjust timeout based on resilient timing configuration
  if (options.timeout && options.timeout > DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration) {
    console.warn(`Emission timeout (${options.timeout}ms) exceeds resilient timing max duration (${DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration}ms)`);
  }

  return options;
}

// ============================================================================
// SECTION 3: CONFIGURATION VALIDATION & UTILITIES (Lines 221-300)
// AI Context: "Configuration validation and utility functions"
// ============================================================================

/**
 * Validate complete enhanced configuration
 */
export function validateEnhancedConfig(config: IEventHandlerRegistryEnhancedConfig): void {
  // Validate basic limits
  if (config.maxMiddleware !== undefined && config.maxMiddleware <= 0) {
    throw new Error('Max middleware must be greater than 0');
  }

  if (config.emissionTimeoutMs !== undefined && config.emissionTimeoutMs <= 0) {
    throw new Error('Emission timeout must be greater than 0');
  }

  // Validate deduplication if provided
  if (config.deduplication) {
    createDeduplicationConfig(config.deduplication);
  }

  // Validate buffering if provided
  if (config.buffering) {
    createBufferingConfig(config.buffering);
  }

  // ✅ RESILIENT TIMING: Validate resilient timing configuration
  if (config.resilientTiming) {
    validateResilientTimingConfig(config.resilientTiming);
  }
}

/**
 * ✅ RESILIENT TIMING: Validate resilient timing configuration
 */
export function validateResilientTimingConfig(config: {
  enableFallbacks: boolean;
  maxExpectedDuration: number;
  unreliableThreshold: number;
  estimateBaseline: number;
  enableDetailedLogging?: boolean;
}): void {
  if (config.maxExpectedDuration <= 0) {
    throw new Error('Max expected duration must be greater than 0');
  }

  if (config.unreliableThreshold <= 0) {
    throw new Error('Unreliable threshold must be greater than 0');
  }

  if (config.estimateBaseline <= 0) {
    throw new Error('Estimate baseline must be greater than 0');
  }
}

/**
 * Check if configuration enables performance monitoring
 */
export function isPerformanceMonitoringEnabled(config: IEventHandlerRegistryEnhancedConfig): boolean {
  return config.resilientTiming?.enableDetailedLogging === true;
}

/**
 * Check if configuration enables buffering
 */
export function isBufferingEnabled(config: IEventHandlerRegistryEnhancedConfig): boolean {
  return config.buffering?.enabled === true;
}

/**
 * Check if configuration enables deduplication
 */
export function isDeduplicationEnabled(config: IEventHandlerRegistryEnhancedConfig): boolean {
  return config.deduplication?.enabled === true;
}

/**
 * Get effective emission timeout considering resilient timing
 */
export function getEffectiveEmissionTimeout(config: IEventHandlerRegistryEnhancedConfig): number {
  const configTimeout = config.emissionTimeoutMs || DEFAULT_ENHANCED_CONFIG.emissionTimeoutMs!;
  const resilientMaxDuration = config.resilientTiming?.maxExpectedDuration || DEFAULT_RESILIENT_TIMING_CONFIG.maxExpectedDuration;
  
  // Use the smaller of the two to ensure resilient timing compatibility
  return Math.min(configTimeout, resilientMaxDuration);
}

/**
 * ✅ ANTI-SIMPLIFICATION COMPLIANCE: Comprehensive configuration summary
 */
export function getConfigurationSummary(config: IEventHandlerRegistryEnhancedConfig): {
  deduplicationEnabled: boolean;
  bufferingEnabled: boolean;
  performanceMonitoringEnabled: boolean;
  maxMiddleware: number;
  effectiveTimeout: number;
  resilientTimingEnabled: boolean;
} {
  return {
    deduplicationEnabled: isDeduplicationEnabled(config),
    bufferingEnabled: isBufferingEnabled(config),
    performanceMonitoringEnabled: isPerformanceMonitoringEnabled(config),
    maxMiddleware: config.maxMiddleware || DEFAULT_ENHANCED_CONFIG.maxMiddleware!,
    effectiveTimeout: getEffectiveEmissionTimeout(config),
    resilientTimingEnabled: config.resilientTiming?.enableFallbacks === true
  };
} 