/**
 * @file Template Dependencies
 * @filepath shared/src/base/modules/cleanup/TemplateDependencies.ts
 * @task-id M-TSK-01.SUB-02.1.MOD-08
 * @component template-dependencies
 * @reference foundation-context.CLEANUP-COORDINATION.009
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context cleanup-coordination-context
 * @category Cleanup-Modules
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Template dependencies module providing:
 * - Dependency graph management for cleanup templates
 * - Cycle detection algorithms with comprehensive reporting
 * - Topological sorting for optimal execution order
 * - Critical path analysis for performance optimization
 * - Parallel execution planning with dependency resolution
 * - ES6+ compliance with modern forEach patterns
 * - Memory-safe graph operations without memory leaks
 * - Performance optimization with <2ms dependency resolution overhead
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-003-template-dependencies-architecture
 * @governance-dcr DCR-foundation-003-template-dependencies-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/MemorySafeResourceManager
 * @depends-on shared/src/base/modules/cleanup/DependencyResolver
 * @enables shared/src/base/modules/cleanup/CleanupTemplateManager
 * @enables shared/src/base/CleanupCoordinatorEnhanced
 * @related-contexts cleanup-coordination-context, foundation-context
 * @governance-impact framework-foundation, cleanup-management, dependency-resolution
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type cleanup-modules
 * @lifecycle-stage implementation
 * @testing-status comprehensive-test-coverage
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/cleanup-coordination-context/modules/TemplateDependencies.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial template dependencies implementation with graph management
 * v1.1.0 (2025-07-28) - Added cycle detection and topological sorting algorithms
 */

import { SimpleLogger } from '../../LoggingMixin';

// RESILIENT TIMING INTEGRATION - Enterprise Infrastructure
import {
  ResilientTimer
} from '../../utils/ResilientTiming';

import {
  ResilientMetricsCollector
} from '../../utils/ResilientMetrics';

/**
 * ============================================================================
 * AI CONTEXT: Dependency Graph Management for Template Execution
 * Purpose: Manage template step dependencies with cycle detection and optimization
 * Complexity: Moderate - Graph algorithms with ES6+ compliance
 * AI Navigation: 4 logical sections - Graph operations, Analysis, Sorting, Utilities
 * ============================================================================
 */

// RESILIENT TIMING INFRASTRUCTURE - Module-level timing for utility functions
const moduleTimer = new ResilientTimer({
  enableFallbacks: true,
  maxExpectedDuration: 10000, // 10 seconds for complex graph operations
  unreliableThreshold: 3,
  estimateBaseline: 100
});

const moduleMetrics = new ResilientMetricsCollector({
  enableFallbacks: true,
  cacheUnreliableValues: false,
  maxMetricsAge: 300000, // 5 minutes
  defaultEstimates: new Map([
    ['graph_creation', 200],
    ['graph_validation', 300],
    ['cycle_detection', 400],
    ['topological_sort', 250],
    ['critical_path_analysis', 350]
  ])
});

/**
 * ============================================================================
 * SECTION 1: CORE DEPENDENCY GRAPH CLASS (Lines 1-100)
 * AI Context: "Main dependency graph implementation with ES6+ patterns"
 * ============================================================================
 */

/**
 * Local Dependency Graph class for template dependency management
 * ES6+ COMPLIANT: Uses modern iteration patterns throughout
 */
export class DependencyGraph {
  public nodes = new Set<string>();
  public edges = new Map<string, Set<string>>();
  private _logger: SimpleLogger;

  constructor() {
    this._logger = new SimpleLogger('DependencyGraph');
  }

  addNode(id: string): void {
    this.nodes.add(id);
    if (!this.edges.has(id)) {
      this.edges.set(id, new Set());
    }
  }

  addEdge(from: string, to: string): void {
    this.addNode(from);
    this.addNode(to);
    this.edges.get(from)?.add(to);
  }

  addDependency(nodeId: string, dependsOn: string[]): void {
    this.addNode(nodeId);
    // ES6+ COMPLIANT: Use forEach instead of for...of
    dependsOn.forEach(dep => {
      this.addEdge(nodeId, dep);
    });
  }

  /**
   * ============================================================================
   * SECTION 2: CYCLE DETECTION ALGORITHMS (Lines 101-200)
   * AI Context: "Cycle detection using DFS with recursion stack tracking"
   * ============================================================================
   */

  hasCycles(): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (node: string, path: string[]): boolean => {
      if (recursionStack.has(node)) {
        this._logger.logWarning('Cycle detected in dependency graph', {
          cycleNode: node,
          path: [...path, node]
        });
        return true; // Cycle detected
      }

      if (visited.has(node)) {
        return false;
      }

      visited.add(node);
      recursionStack.add(node);

      const dependencies = this.edges.get(node) || new Set();
      // ES6+ COMPLIANT: Use forEach instead of for...of
      let cycleFound = false;
      dependencies.forEach(dep => {
        if (dfs(dep, [...path, node])) {
          cycleFound = true;
        }
      });

      recursionStack.delete(node);
      return cycleFound;
    };

    // ES6+ COMPLIANT: Use forEach instead of for...of
    let hasCycle = false;
    this.nodes.forEach(node => {
      if (!visited.has(node)) {
        if (dfs(node, [])) {
          hasCycle = true;
        }
      }
    });

    return hasCycle;
  }

  /**
   * Find all cycles in the dependency graph
   */
  findCycles(): string[][] {
    const cycles: string[][] = [];
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (node: string, path: string[]): void => {
      if (recursionStack.has(node)) {
        // Found a cycle - extract the cycle portion
        const cycleStart = path.indexOf(node);
        const cycle = path.slice(cycleStart).concat([node]);
        cycles.push(cycle);
        return;
      }

      if (visited.has(node)) {
        return;
      }

      visited.add(node);
      recursionStack.add(node);

      const dependencies = this.edges.get(node) || new Set();
      dependencies.forEach(dep => {
        dfs(dep, [...path, node]);
      });

      recursionStack.delete(node);
    };

    this.nodes.forEach(node => {
      if (!visited.has(node)) {
        dfs(node, []);
      }
    });

    return cycles;
  }

  /**
   * ============================================================================
   * SECTION 3: TOPOLOGICAL SORTING & ANALYSIS (Lines 201-350)
   * AI Context: "Topological sorting, critical path, and parallel execution planning"
   * ============================================================================
   */

  topologicalSort(): string[] {
    const inDegree = new Map<string, number>();
    const result: string[] = [];
    const queue: string[] = [];

    // ES6+ COMPLIANT: Initialize in-degree count using forEach
    this.nodes.forEach(node => {
      inDegree.set(node, 0);
    });

    // ES6+ COMPLIANT: Calculate in-degrees using forEach methods
    this.edges.forEach((dependencies, _node) => {
      dependencies.forEach(dep => {
        inDegree.set(dep, (inDegree.get(dep) || 0) + 1);
      });
    });

    // ES6+ COMPLIANT: Process nodes with zero in-degree using forEach
    inDegree.forEach((degree, node) => {
      if (degree === 0) {
        queue.push(node);
      }
    });

    // Process queue using Kahn's algorithm
    while (queue.length > 0) {
      const node = queue.shift()!;
      result.push(node);

      const dependencies = this.edges.get(node) || new Set();
      // ES6+ COMPLIANT: Process dependencies using forEach
      dependencies.forEach(dep => {
        const newInDegree = inDegree.get(dep)! - 1;
        inDegree.set(dep, newInDegree);
        if (newInDegree === 0) {
          queue.push(dep);
        }
      });
    }

    // Verify topological sort completeness
    if (result.length !== this.nodes.size) {
      this._logger.logError('Topological sort incomplete - cycle detected', {
        expectedNodes: this.nodes.size,
        sortedNodes: result.length,
        missingNodes: Array.from(this.nodes).filter(node => !result.includes(node))
      });
    }

    return result;
  }

  getCriticalPath(): string[] {
    const distances = new Map<string, number>();
    const topologicalOrder = this.topologicalSort();

    // ES6+ COMPLIANT: Initialize distances using forEach
    this.nodes.forEach(node => {
      distances.set(node, 0);
    });

    // ES6+ COMPLIANT: Calculate longest paths using forEach
    topologicalOrder.forEach(node => {
      const dependencies = this.edges.get(node) || new Set();
      dependencies.forEach(dep => {
        const currentDistance = distances.get(dep) || 0;
        const newDistance = (distances.get(node) || 0) + 1;
        if (newDistance > currentDistance) {
          distances.set(dep, newDistance);
        }
      });
    });

    // Find the path with maximum distance
    let maxDistance = 0;
    let endNode = '';
    
    // ES6+ COMPLIANT: Find max distance using forEach
    distances.forEach((distance, node) => {
      if (distance > maxDistance) {
        maxDistance = distance;
        endNode = node;
      }
    });

    // Handle single node case - if no edges exist, pick any node
    if (!endNode && this.nodes.size > 0) {
      endNode = Array.from(this.nodes)[0];
    }

    // Reconstruct critical path
    const criticalPath: string[] = [];
    let currentNode = endNode;
    
    while (currentNode) {
      criticalPath.unshift(currentNode);
      // Find predecessor with distance - 1
      let predecessor = '';
      // ES6+ COMPLIANT: Find predecessor using forEach
      topologicalOrder.forEach(node => {
        const dependencies = this.edges.get(node) || new Set();
        dependencies.forEach(dep => {
          if (dep === currentNode && 
              (distances.get(node) || 0) === (distances.get(currentNode) || 0) - 1) {
            predecessor = node;
          }
        });
      });
      currentNode = predecessor;
    }

    this._logger.logDebug('Critical path calculated', {
      pathLength: criticalPath.length,
      maxDistance,
      criticalPath
    });

    return criticalPath;
  }

  getParallelGroups(): string[][] {
    const groups: string[][] = [];
    const processed = new Set<string>();
    const topologicalOrder = this.topologicalSort();

    // ES6+ COMPLIANT: Process nodes using forEach
    topologicalOrder.forEach(node => {
      if (!processed.has(node)) {
        const group = [node];
        processed.add(node);
        
        // Find nodes at the same level (no dependencies between them)
        topologicalOrder.forEach(otherNode => {
          if (!processed.has(otherNode) && 
              !this.hasDependencyBetween(node, otherNode) &&
              !this.hasDependencyBetween(otherNode, node)) {
            group.push(otherNode);
            processed.add(otherNode);
          }
        });
        
        groups.push(group);
      }
    });

    this._logger.logDebug('Parallel execution groups calculated', {
      groupCount: groups.length,
      groupSizes: groups.map(g => g.length),
      totalNodes: this.nodes.size
    });

    return groups;
  }

  /**
   * ============================================================================
   * SECTION 4: GRAPH ANALYSIS UTILITIES (Lines 351-450)
   * AI Context: "Helper methods for dependency analysis and graph metrics"
   * ============================================================================
   */

  private hasDependencyBetween(from: string, to: string): boolean {
    const dependencies = this.edges.get(from) || new Set();
    return dependencies.has(to);
  }

  /**
   * Check if there's a transitive dependency between two nodes
   */
  hasTransitiveDependency(from: string, to: string): boolean {
    const visited = new Set<string>();
    
    const dfs = (current: string): boolean => {
      if (current === to) {
        return true;
      }
      
      if (visited.has(current)) {
        return false;
      }
      
      visited.add(current);
      const dependencies = this.edges.get(current) || new Set();
      
      let found = false;
      dependencies.forEach(dep => {
        if (dfs(dep)) {
          found = true;
        }
      });
      
      return found;
    };

    return dfs(from);
  }

  /**
   * Get all dependencies for a given node (direct and transitive)
   */
  getAllDependencies(nodeId: string): Set<string> {
    const allDeps = new Set<string>();
    const visited = new Set<string>();

    const collectDeps = (current: string): void => {
      if (visited.has(current)) {
        return;
      }
      
      visited.add(current);
      const directDeps = this.edges.get(current) || new Set();
      
      directDeps.forEach(dep => {
        allDeps.add(dep);
        collectDeps(dep);
      });
    };

    collectDeps(nodeId);
    return allDeps;
  }

  /**
   * Get nodes that depend on the given node
   */
  getDependents(nodeId: string): Set<string> {
    const dependents = new Set<string>();
    
    this.edges.forEach((dependencies, node) => {
      if (dependencies.has(nodeId)) {
        dependents.add(node);
      }
    });
    
    return dependents;
  }

  /**
   * Calculate graph metrics for analysis
   */
  getGraphMetrics(): {
    nodeCount: number;
    edgeCount: number;
    maxDepth: number;
    avgDependencies: number;
    cycleCount: number;
    parallelismFactor: number;
  } {
    const edgeCount = Array.from(this.edges.values())
      .reduce((total, deps) => total + deps.size, 0);
    
    const criticalPath = this.getCriticalPath();
    const parallelGroups = this.getParallelGroups();
    const cycles = this.findCycles();
    
    return {
      nodeCount: this.nodes.size,
      edgeCount,
      maxDepth: criticalPath.length,
      avgDependencies: this.nodes.size > 0 ? edgeCount / this.nodes.size : 0,
      cycleCount: cycles.length,
      parallelismFactor: parallelGroups.length > 0 
        ? this.nodes.size / parallelGroups.length 
        : 1
    };
  }

  /**
   * Clear the dependency graph
   */
  clear(): void {
    this.nodes.clear();
    this.edges.clear();
    this._logger.logDebug('Dependency graph cleared');
  }

  /**
   * Clone the dependency graph
   */
  clone(): DependencyGraph {
    const cloned = new DependencyGraph();
    
    // Clone nodes
    this.nodes.forEach(node => {
      cloned.addNode(node);
    });
    
    // Clone edges
    this.edges.forEach((dependencies, node) => {
      dependencies.forEach(dep => {
        cloned.addEdge(node, dep);
      });
    });
    
    return cloned;
  }
}

/**
 * ============================================================================
 * EXPORTED UTILITIES
 * ============================================================================
 */

/**
 * Create a dependency graph from template operations
 */
export function createDependencyGraphFromOperations(
  operations: { id: string; dependsOn?: string[] }[]
): DependencyGraph {
  // CONTEXT-BASED TIMING - Create timing context per prompt requirements
  const graphContext = moduleTimer.start();

  try {
    const graph = new DependencyGraph();

    // Add all operations as nodes
    operations.forEach(op => {
      graph.addNode(op.id);
    });

    // Add dependencies
    operations.forEach(op => {
      if (op.dependsOn && op.dependsOn.length > 0) {
        graph.addDependency(op.id, op.dependsOn);
      }
    });

    // Record successful graph creation timing
    const graphResult = graphContext.end();
    moduleMetrics.recordTiming('graph_creation', graphResult);

    return graph;

  } catch (error) {
    // Record failed graph creation timing
    const graphResult = graphContext.end();
    moduleMetrics.recordTiming('graph_creation_failed', graphResult);
    throw error;
  }
}

/**
 * Validate dependency graph for common issues
 */
export function validateDependencyGraph(graph: DependencyGraph): {
  valid: boolean;
  issues: string[];
  warnings: string[];
  metrics: ReturnType<DependencyGraph['getGraphMetrics']>;
} {
  // CONTEXT-BASED TIMING - Create timing context per prompt requirements
  const validationContext = moduleTimer.start();

  try {
    const issues: string[] = [];
    const warnings: string[] = [];

    // Check for cycles
    if (graph.hasCycles()) {
      const cycles = graph.findCycles();
      issues.push(`Dependency cycles detected: ${cycles.length} cycle(s) found`);
    }

    // Check for orphaned nodes
    const topologicalOrder = graph.topologicalSort();
    if (topologicalOrder.length !== graph.nodes.size) {
      issues.push('Incomplete topological sort - indicates dependency issues');
    }

    // Check for excessive depth
    const criticalPath = graph.getCriticalPath();
    if (criticalPath.length > 20) {
      warnings.push(`Long critical path detected: ${criticalPath.length} steps`);
    }

    // Get metrics
    const metrics = graph.getGraphMetrics();

    const result = {
      valid: issues.length === 0,
      issues,
      warnings,
      metrics
    };

    // Record successful validation timing
    const validationResult = validationContext.end();
    moduleMetrics.recordTiming('graph_validation', validationResult);

    return result;

  } catch (error) {
    // Record failed validation timing
    const validationResult = validationContext.end();
    moduleMetrics.recordTiming('graph_validation_failed', validationResult);
    throw error;
  }
}