/**
 * @file Memory-Safe Environment Constants Calculator
 * @filepath shared/src/constants/platform/tracking/environment-constants-calculator.ts
 * @task-id T-TSK-03.SUB-04.CONST-02-FIXED
 * @component environment-constants-calculator-fixed
 * @reference foundation-context.CONSTANTS.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T0
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-26
 * @modified 2025-07-15 12:30:00 +03
 * 
 * @description
 * MEMORY-LEAK FIXED VERSION of the environment constants calculator.
 * Now inherits from MemorySafeResourceManager to prevent memory leaks
 * and ensure proper resource cleanup.
 * 
 * 🛡️ MEMORY LEAK FIXES APPLIED:
 * - Lazy initialization (no immediate singleton creation)
 * - Safe interval management with automatic cleanup
 * - Process exit handlers for graceful shutdown
 * - Reference counting for shared resources
 * - Container-aware memory management
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-002-environment-adaptation-fixed
 * @governance-dcr DCR-foundation-002-smart-constants-memory-safe
 * @governance-status approved
 * @governance-compliance authority-validated
 */

import * as os from 'os';
import * as fs from 'fs';
import { MemorySafeResourceManager, createMemorySafeSingleton } from '../../../base/MemorySafeResourceManager';


// ============================================================================
// ENVIRONMENT DETECTION INTERFACES (unchanged from original)
// ============================================================================

export interface ISystemResources {
  totalMemoryMB: number;
  freeMemoryMB: number;
  totalCPUCores: number;
  nodeHeapLimitMB: number;
  containerMemoryLimitMB?: number;
  availableDiskSpaceMB: number;
  platform: string;
  architecture: string;
  nodeVersion: string;
}

export interface IEnvironmentProfile {
  name: string;
  type: 'development' | 'staging' | 'production' | 'testing';
  memoryReservationRatio: number;
  cpuReservationRatio: number;
  cacheAllocationRatio: number;
  batchSizeMultiplier: number;
  concurrencyMultiplier: number;
  retentionMultiplier: number;
  safetyMarginRatio: number;
}

export interface ICalculatedConstants {
  // Memory-based constants
  MAX_MEMORY_USAGE: number;
  MEMORY_USAGE_THRESHOLD: number;
  
  // Cache constants
  MAX_CACHE_SIZE: number;
  ANALYTICS_CACHE_MAX_SIZE: number;
  SMART_PATH_CACHE_SIZE: number;
  CONTEXT_AUTHORITY_CACHE_SIZE: number;
  
  // Batch processing constants
  MAX_BATCH_SIZE: number;
  MIN_BATCH_SIZE: number;
  MAX_METRICS_BATCH_SIZE: number;
  WARMUP_BATCH_SIZE: number;
  
  // Concurrency constants
  MAX_CONCURRENT_OPERATIONS: number;
  MAX_REALTIME_SUBSCRIBERS: number;
  MAX_CONCURRENT_OPTIMIZATIONS: number;
  
  // Performance thresholds
  MAX_RESPONSE_TIME: number;
  PERFORMANCE_MONITORING_INTERVAL: number;
  CPU_USAGE_THRESHOLD: number;
  
  // Retention and cleanup constants
  MAX_LOG_FILE_SIZE: number;
  MAX_LOG_RETENTION_DAYS: number;
  CACHE_TTL: number;
  CLEANUP_INTERVAL: number;
  
  // Query and processing limits
  MAX_QUERY_SIZE: number;
  MAX_DEPENDENCY_DEPTH: number;
  MAX_AUTHORITY_CHAIN_DEPTH: number;
  
  // Environment metadata
  calculatedAt: Date;
  environmentProfile: string;
  systemResources: ISystemResources;
}

// ============================================================================
// ENVIRONMENT PROFILES (unchanged from original)
// ============================================================================

const ENVIRONMENT_PROFILES: Record<string, IEnvironmentProfile> = {
  development: {
    name: 'Development',
    type: 'development',
    memoryReservationRatio: 0.3,
    cpuReservationRatio: 0.5,
    cacheAllocationRatio: 0.2,
    batchSizeMultiplier: 0.5,
    concurrencyMultiplier: 0.5,
    retentionMultiplier: 0.5,
    safetyMarginRatio: 0.3
  },
  
  testing: {
    name: 'Testing',
    type: 'testing',
    memoryReservationRatio: 0.2,
    cpuReservationRatio: 0.3,
    cacheAllocationRatio: 0.15,
    batchSizeMultiplier: 0.3,
    concurrencyMultiplier: 0.3,
    retentionMultiplier: 0.25,
    safetyMarginRatio: 0.4
  },
  
  staging: {
    name: 'Staging',
    type: 'staging',
    memoryReservationRatio: 0.6,
    cpuReservationRatio: 0.7,
    cacheAllocationRatio: 0.3,
    batchSizeMultiplier: 0.8,
    concurrencyMultiplier: 0.8,
    retentionMultiplier: 0.8,
    safetyMarginRatio: 0.2
  },
  
  production: {
    name: 'Production',
    type: 'production',
    memoryReservationRatio: 0.7,
    cpuReservationRatio: 0.8,
    cacheAllocationRatio: 0.4,
    batchSizeMultiplier: 1.0,
    concurrencyMultiplier: 1.0,
    retentionMultiplier: 1.0,
    safetyMarginRatio: 0.15
  }
};

// ============================================================================
// MEMORY-SAFE ENVIRONMENT CONSTANTS CALCULATOR
// ============================================================================

export class EnvironmentConstantsCalculator extends MemorySafeResourceManager {
  private cachedResources: ISystemResources | null = null;
  private lastCalculation: ICalculatedConstants | null = null;
  private currentNodeEnv: string | null = null;
  
  constructor() {
    // Configure memory limits for this specific service
    super({
      maxIntervals: 2, // Only need recalculation interval
      maxTimeouts: 5,
      maxCacheSize: 10000000, // 10MB for environment data
      maxConnections: 0, // No connections needed
      memoryThresholdMB: 50, // Conservative limit
      cleanupIntervalMs: 300000 // 5 minutes
    });
  }

  // ============================================================================
  // MEMORY-SAFE INITIALIZATION
  // ============================================================================

  protected async doInitialize(): Promise<void> {
    // Only start recalculation timer if actually needed
    if (this.shouldStartRecalculation()) {
      this.createSafeInterval(
        () => this.invalidateCache(),
        5 * 60 * 1000, // 5 minutes
        'environment_recalculation'
      );
    }
  }

  protected async doShutdown(): Promise<void> {
    // Cleanup is handled automatically by the base class
    this.cachedResources = null;
    this.lastCalculation = null;
  }

  /**
   * Determine if recalculation timer should start
   */
  private shouldStartRecalculation(): boolean {
    // Don't start timer in test environment
    if (process.env.NODE_ENV === 'test') {
      return false;
    }
    
    // Don't start timer if process is short-lived
    if (process.env.DISABLE_BACKGROUND_TIMERS === 'true') {
      return false;
    }
    
    return true;
  }

  // ============================================================================
  // SYSTEM RESOURCE DETECTION (improved with error handling)
  // ============================================================================

  public getSystemResources(): ISystemResources {
    if (this.cachedResources) {
      return this.cachedResources;
    }

    try {
      const totalMemoryBytes = os.totalmem();
      const freeMemoryBytes = os.freemem();
      const totalCPUCores = os.cpus().length;
      
      // Get Node.js heap limit safely
      let nodeHeapLimitMB: number;
      try {
        const v8 = require('v8');
        const heapStats = v8.getHeapStatistics();
        nodeHeapLimitMB = Math.round(heapStats.heap_size_limit / (1024 * 1024));
      } catch {
        // Fallback if v8 module is not available
        nodeHeapLimitMB = Math.round(totalMemoryBytes * 0.5 / (1024 * 1024));
      }
      
      // Detect container memory limit (Docker/Kubernetes)
      const containerMemoryLimitMB = this.detectContainerMemoryLimit();
      
      // Get available disk space
      const availableDiskSpaceMB = this.getAvailableDiskSpace();

      const resourcesBase = {
        totalMemoryMB: Math.round(totalMemoryBytes / (1024 * 1024)),
        freeMemoryMB: Math.round(freeMemoryBytes / (1024 * 1024)),
        totalCPUCores,
        nodeHeapLimitMB,
        availableDiskSpaceMB,
        platform: os.platform(),
        architecture: os.arch(),
        nodeVersion: process.version
      };

      this.cachedResources = containerMemoryLimitMB !== undefined
        ? { ...resourcesBase, containerMemoryLimitMB }
        : resourcesBase;

      return this.cachedResources;
    } catch (error) {
      this.emit('error', new Error(`Failed to get system resources: ${error}`));
      
      // Return safe fallback values
      return {
        totalMemoryMB: 1024,
        freeMemoryMB: 512,
        totalCPUCores: 1,
        nodeHeapLimitMB: 512,
        availableDiskSpaceMB: 1000,
        platform: 'unknown',
        architecture: 'unknown',
        nodeVersion: process.version
      };
    }
  }

  /**
   * Detect container memory limit with better error handling
   */
  private detectContainerMemoryLimit(): number | undefined {
    try {
      // Check cgroup v1
      if (fs.existsSync('/sys/fs/cgroup/memory/memory.limit_in_bytes')) {
        const limitBytes = fs.readFileSync('/sys/fs/cgroup/memory/memory.limit_in_bytes', 'utf8').trim();
        const limit = parseInt(limitBytes);
        if (limit && limit < Number.MAX_SAFE_INTEGER) {
          return Math.round(limit / (1024 * 1024));
        }
      }
      
      // Check cgroup v2
      if (fs.existsSync('/sys/fs/cgroup/memory.max')) {
        const limitBytes = fs.readFileSync('/sys/fs/cgroup/memory.max', 'utf8').trim();
        if (limitBytes !== 'max') {
          const limit = parseInt(limitBytes);
          if (limit && limit < Number.MAX_SAFE_INTEGER) {
            return Math.round(limit / (1024 * 1024));
          }
        }
      }
    } catch {
      // Ignore errors - not in container or no access
    }
    
    return undefined;
  }

  /**
   * Get available disk space with error handling
   */
  private getAvailableDiskSpace(): number {
    try {
      // Check if current directory is accessible
      fs.statSync(process.cwd());
      // This is a simplified approach - in real implementation,
      // you'd use a library like 'statvfs' for accurate disk space
      return 10000; // 10GB fallback
    } catch {
      return 10000; // 10GB fallback
    }
  }

  // ============================================================================
  // ENVIRONMENT PROFILE DETECTION
  // ============================================================================

  public detectEnvironmentProfile(): IEnvironmentProfile {
    const nodeEnv = process.env.NODE_ENV?.toLowerCase() || 'development';
    
    // Cache the detected environment
    if (this.currentNodeEnv !== nodeEnv) {
      this.currentNodeEnv = nodeEnv;
      this.invalidateCache(); // Force recalculation for new environment
    }
    
    return ENVIRONMENT_PROFILES[nodeEnv] || ENVIRONMENT_PROFILES.development;
  }

  // ============================================================================
  // CONSTANTS CALCULATION (improved with safety checks)
  // ============================================================================

  /**
   * Get constants synchronously (returns cached values or calculates with defaults)
   * This method is safe to call from synchronous contexts
   */
  public getConstantsSync(): ICalculatedConstants {
    // Return cached calculation if available
    if (this.lastCalculation) {
      return this.lastCalculation;
    }

    // Calculate with safe defaults for synchronous access
    try {
      const resources = this.getSystemResources();
      const profile = this.detectEnvironmentProfile();

      // Calculate memory allocations with safety margins
      const availableMemoryMB = Math.min(
        resources.totalMemoryMB,
        resources.nodeHeapLimitMB,
        1024 // Cap at 1GB for safety in sync mode
      );

      const reservedMemoryMB = Math.floor(availableMemoryMB * profile.memoryReservationRatio);
      const safeMemoryMB = Math.floor(reservedMemoryMB * (1 - profile.safetyMarginRatio));

      // CPU-aware calculations
      const availableCPUs = Math.max(1, Math.floor(resources.totalCPUCores * profile.cpuReservationRatio));

      this.lastCalculation = {
        // Memory-based constants
        MAX_MEMORY_USAGE: safeMemoryMB * 1024 * 1024, // Convert to bytes
        MEMORY_USAGE_THRESHOLD: Math.floor(safeMemoryMB * 0.8) * 1024 * 1024,

        // Cache constants (memory-proportional)
        MAX_CACHE_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio) * 1024 * 1024,
        ANALYTICS_CACHE_MAX_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio * 0.3) * 1024 * 1024,
        SMART_PATH_CACHE_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio * 0.2) * 1024 * 1024,
        CONTEXT_AUTHORITY_CACHE_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio * 0.15) * 1024 * 1024,

        // Batch processing constants
        MAX_BATCH_SIZE: Math.max(10, Math.floor(safeMemoryMB * profile.batchSizeMultiplier)),
        MIN_BATCH_SIZE: Math.max(1, Math.floor(safeMemoryMB * profile.batchSizeMultiplier * 0.1)),
        MAX_METRICS_BATCH_SIZE: Math.max(5, Math.floor(safeMemoryMB * profile.batchSizeMultiplier * 0.5)),
        WARMUP_BATCH_SIZE: Math.max(1, Math.floor(safeMemoryMB * profile.batchSizeMultiplier * 0.2)),

        // Concurrency constants
        MAX_CONCURRENT_OPERATIONS: Math.max(1, availableCPUs * profile.concurrencyMultiplier),
        MAX_REALTIME_SUBSCRIBERS: Math.max(10, availableCPUs * profile.concurrencyMultiplier * 5),
        MAX_CONCURRENT_OPTIMIZATIONS: Math.max(1, Math.floor(availableCPUs * profile.concurrencyMultiplier * 0.5)),

        // Performance thresholds
        MAX_RESPONSE_TIME: 5000, // 5 seconds default
        PERFORMANCE_MONITORING_INTERVAL: 30000, // 30 seconds default
        CPU_USAGE_THRESHOLD: 80, // 80% default

        // Retention and cleanup constants
        MAX_LOG_FILE_SIZE: Math.floor(safeMemoryMB * 0.1) * 1024 * 1024, // 10% of memory
        MAX_LOG_RETENTION_DAYS: Math.floor(30 * profile.retentionMultiplier),
        CACHE_TTL: 300000, // 5 minutes default
        CLEANUP_INTERVAL: 600000, // 10 minutes default

        // Query and processing limits
        MAX_QUERY_SIZE: Math.floor(safeMemoryMB * 0.05) * 1024 * 1024, // 5% of memory
        MAX_DEPENDENCY_DEPTH: Math.max(5, Math.floor(availableCPUs * 2)),
        MAX_AUTHORITY_CHAIN_DEPTH: Math.max(3, Math.floor(availableCPUs)),

        // Calculation metadata
        calculatedAt: new Date(),
        environmentProfile: profile.name,
        systemResources: resources
      };

      return this.lastCalculation;
    } catch (error) {
      // Return safe defaults if calculation fails
      return this.getSafeDefaults();
    }
  }

  /**
   * Get safe default constants when calculation fails
   */
  private getSafeDefaults(): ICalculatedConstants {
    return {
      // Memory-based constants (conservative defaults)
      MAX_MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
      MEMORY_USAGE_THRESHOLD: 80 * 1024 * 1024, // 80MB

      // Cache constants
      MAX_CACHE_SIZE: 10 * 1024 * 1024, // 10MB
      ANALYTICS_CACHE_MAX_SIZE: 3 * 1024 * 1024, // 3MB
      SMART_PATH_CACHE_SIZE: 2 * 1024 * 1024, // 2MB
      CONTEXT_AUTHORITY_CACHE_SIZE: 1 * 1024 * 1024, // 1MB

      // Batch processing constants
      MAX_BATCH_SIZE: 100,
      MIN_BATCH_SIZE: 10,
      MAX_METRICS_BATCH_SIZE: 50,
      WARMUP_BATCH_SIZE: 20,

      // Concurrency constants
      MAX_CONCURRENT_OPERATIONS: 4,
      MAX_REALTIME_SUBSCRIBERS: 20,
      MAX_CONCURRENT_OPTIMIZATIONS: 2,

      // Performance thresholds
      MAX_RESPONSE_TIME: 5000, // 5 seconds
      PERFORMANCE_MONITORING_INTERVAL: 30000, // 30 seconds
      CPU_USAGE_THRESHOLD: 80, // 80%

      // Retention and cleanup constants
      MAX_LOG_FILE_SIZE: 10 * 1024 * 1024, // 10MB
      MAX_LOG_RETENTION_DAYS: 7,
      CACHE_TTL: 300000, // 5 minutes
      CLEANUP_INTERVAL: 600000, // 10 minutes

      // Query and processing limits
      MAX_QUERY_SIZE: 5 * 1024 * 1024, // 5MB
      MAX_DEPENDENCY_DEPTH: 5,
      MAX_AUTHORITY_CHAIN_DEPTH: 3,

      // Calculation metadata
      calculatedAt: new Date(),
      environmentProfile: 'safe-defaults',
      systemResources: {
        totalMemoryMB: 512,
        freeMemoryMB: 256,
        nodeHeapLimitMB: 256,
        totalCPUCores: 2,
        availableDiskSpaceMB: 1024,
        platform: process.platform,
        architecture: process.arch,
        nodeVersion: process.version
      }
    };
  }

  public async calculateConstants(): Promise<ICalculatedConstants> {
    // Ensure initialization
    await this.initialize();

    if (this.lastCalculation) {
      return this.lastCalculation;
    }

    try {
      const resources = this.getSystemResources();
      const profile = this.detectEnvironmentProfile();
      
      // Calculate memory allocations with safety margins
      const availableMemoryMB = Math.min(
        resources.totalMemoryMB,
        resources.nodeHeapLimitMB,
        resources.containerMemoryLimitMB || Number.MAX_SAFE_INTEGER
      );
      
      const reservedMemoryMB = Math.floor(availableMemoryMB * profile.memoryReservationRatio);
      const safeMemoryMB = Math.floor(reservedMemoryMB * (1 - profile.safetyMarginRatio));
      
      // CPU-aware calculations
      const availableCPUs = Math.max(1, Math.floor(resources.totalCPUCores * profile.cpuReservationRatio));
      
      this.lastCalculation = {
        // Memory-based constants
        MAX_MEMORY_USAGE: safeMemoryMB * 1024 * 1024, // Convert to bytes
        MEMORY_USAGE_THRESHOLD: Math.floor(safeMemoryMB * 0.8) * 1024 * 1024,
        
        // Cache constants (memory-proportional)
        MAX_CACHE_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio) * 1024 * 1024,
        ANALYTICS_CACHE_MAX_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio * 0.3) * 1024 * 1024,
        SMART_PATH_CACHE_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio * 0.2) * 1024 * 1024,
        CONTEXT_AUTHORITY_CACHE_SIZE: Math.floor(safeMemoryMB * profile.cacheAllocationRatio * 0.15) * 1024 * 1024,
        
        // Batch processing constants (CPU-aware)
        MAX_BATCH_SIZE: Math.max(10, Math.floor(availableCPUs * 50 * profile.batchSizeMultiplier)),
        MIN_BATCH_SIZE: Math.max(1, Math.floor(availableCPUs * 5 * profile.batchSizeMultiplier)),
        MAX_METRICS_BATCH_SIZE: Math.max(5, Math.floor(availableCPUs * 25 * profile.batchSizeMultiplier)),
        WARMUP_BATCH_SIZE: Math.max(2, Math.floor(availableCPUs * 10 * profile.batchSizeMultiplier)),
        
        // Concurrency constants (CPU-aware)
        MAX_CONCURRENT_OPERATIONS: Math.max(1, Math.floor(availableCPUs * 2 * profile.concurrencyMultiplier)),
        MAX_REALTIME_SUBSCRIBERS: Math.max(10, Math.floor(availableCPUs * 50 * profile.concurrencyMultiplier)),
        MAX_CONCURRENT_OPTIMIZATIONS: Math.max(1, Math.floor(availableCPUs * 1 * profile.concurrencyMultiplier)),
        
        // Performance thresholds (environment-aware)
        MAX_RESPONSE_TIME: profile.type === 'production' ? 1000 : 5000,
        PERFORMANCE_MONITORING_INTERVAL: profile.type === 'production' ? 30000 : 60000,
        CPU_USAGE_THRESHOLD: profile.type === 'production' ? 70 : 85,
        
        // Retention and cleanup constants (environment-proportional)
        MAX_LOG_FILE_SIZE: Math.floor(100 * profile.retentionMultiplier) * 1024 * 1024,
        MAX_LOG_RETENTION_DAYS: Math.floor(30 * profile.retentionMultiplier),
        CACHE_TTL: Math.floor(300000 * profile.retentionMultiplier), // 5 minutes base
        CLEANUP_INTERVAL: Math.floor(300000 * profile.retentionMultiplier), // 5 minutes base
        
        // Query and processing limits (conservative)
        MAX_QUERY_SIZE: 10000,
        MAX_DEPENDENCY_DEPTH: 10,
        MAX_AUTHORITY_CHAIN_DEPTH: 5,
        
        // Metadata
        calculatedAt: new Date(),
        environmentProfile: profile.name,
        systemResources: resources
      };

      return this.lastCalculation;
    } catch (error) {
      this.emit('error', error);
      throw new Error(`Failed to calculate environment constants: ${error}`);
    }
  }

  // ============================================================================
  // TRACKING CONSTANTS INTEGRATION
  // ============================================================================

  public async getTrackingConstants(): Promise<Record<string, any>> {
    const calculated = await this.calculateConstants();
    
    return {
      // Core tracking constants
      TRACKING_CONSTANTS: {
        MAX_MEMORY_USAGE: calculated.MAX_MEMORY_USAGE,
        MAX_CACHE_SIZE: calculated.MAX_CACHE_SIZE,
        MAX_BATCH_SIZE: calculated.MAX_BATCH_SIZE,
        MAX_CONCURRENT_OPERATIONS: calculated.MAX_CONCURRENT_OPERATIONS,
        CACHE_TTL: calculated.CACHE_TTL,
        CLEANUP_INTERVAL: calculated.CLEANUP_INTERVAL,
      },
      
      // Analytics constants
      ANALYTICS_CONSTANTS: {
        MAX_CACHE_SIZE: calculated.ANALYTICS_CACHE_MAX_SIZE,
        MAX_BATCH_SIZE: calculated.MAX_METRICS_BATCH_SIZE,
        MAX_CONCURRENT_OPERATIONS: calculated.MAX_CONCURRENT_OPTIMIZATIONS,
        CACHE_TTL: calculated.CACHE_TTL,
        MIN_CACHE_HIT_RATIO: 0.7,
        ANALYSIS_TIMEOUT: calculated.MAX_RESPONSE_TIME * 50,
      },
      
      // Performance thresholds
      PERFORMANCE_THRESHOLDS: {
        MAX_RESPONSE_TIME: calculated.MAX_RESPONSE_TIME * 10,
        MAX_MEMORY_USAGE: calculated.MAX_MEMORY_USAGE,
        MAX_CPU_USAGE: calculated.CPU_USAGE_THRESHOLD,
        MIN_CACHE_HIT_RATIO: 0.7,
        MAX_ERROR_RATE: 0.05,
        MIN_THROUGHPUT: Math.floor(calculated.MAX_CONCURRENT_OPERATIONS * 10),
      },
      
      // Environment metadata
      ENVIRONMENT_METADATA: {
        calculatedAt: calculated.calculatedAt.toISOString(),
        profile: calculated.environmentProfile,
        systemInfo: {
          totalMemoryMB: calculated.systemResources.totalMemoryMB,
          totalCPUCores: calculated.systemResources.totalCPUCores,
          platform: calculated.systemResources.platform,
          nodeVersion: calculated.systemResources.nodeVersion,
          containerized: calculated.systemResources.containerMemoryLimitMB !== undefined
        }
      }
    };
  }

  // ============================================================================
  // CACHE MANAGEMENT
  // ============================================================================

  public invalidateCache(): void {
    this.cachedResources = null;
    this.lastCalculation = null;
  }

  public async getCalculationSummary(): Promise<string> {
    const calculated = await this.calculateConstants();
    
    return `Environment: ${calculated.environmentProfile} | ` +
           `Memory: ${calculated.systemResources.totalMemoryMB}MB | ` +
           `CPUs: ${calculated.systemResources.totalCPUCores} | ` +
           `Max Operations: ${calculated.MAX_CONCURRENT_OPERATIONS} | ` +
           `Cache Size: ${Math.round(calculated.MAX_CACHE_SIZE / 1024 / 1024)}MB | ` +
           `Calculated: ${calculated.calculatedAt.toISOString()}`;
  }

  public getHealthStatus(): { healthy: boolean; details: string } {
    const isHealthy = this.isHealthy();
    const metrics = this.getResourceMetrics();
    
    return {
      healthy: isHealthy,
      details: isHealthy
        ? 'Memory usage within limits'
        : `Memory usage exceeds threshold: ${metrics.memoryUsageMB}MB`
    };
  }
}

// ============================================================================
// MEMORY-SAFE SINGLETON FACTORY
// ============================================================================

// Create memory-safe singleton instance instead of immediate instantiation
const environmentCalculatorInstance = createMemorySafeSingleton(EnvironmentConstantsCalculator);

// ============================================================================
// UTILITY FUNCTIONS (with lazy initialization)
// ============================================================================

/**
 * Get singleton instance and calculate constants immediately
 */
export async function getEnvironmentConstants(): Promise<ICalculatedConstants> {
  return environmentCalculatorInstance.calculateConstants();
}

/**
 * Get environment constants synchronously (safe for immediate use)
 * This function returns cached constants or calculates them with safe defaults
 */
export function getEnvironmentConstantsSync(): ICalculatedConstants {
  return environmentCalculatorInstance.getConstantsSync();
}

/**
 * Get constants formatted for tracking-constants.ts integration
 */
export async function getTrackingConstants(): Promise<Record<string, any>> {
  return environmentCalculatorInstance.getTrackingConstants();
}

/**
 * Get environment calculation summary
 */
export async function getEnvironmentSummary(): Promise<string> {
  return environmentCalculatorInstance.getCalculationSummary();
}

/**
 * Get environment summary synchronously
 */
export function getEnvironmentSummarySync(): string {
  const constants = getEnvironmentConstantsSync();
  return `Environment: ${constants.environmentProfile} | ` +
         `Memory: ${constants.systemResources.totalMemoryMB}MB | ` +
         `CPUs: ${constants.systemResources.totalCPUCores} | ` +
         `Max Operations: ${constants.MAX_CONCURRENT_OPERATIONS} | ` +
         `Cache Size: ${Math.round(constants.MAX_CACHE_SIZE / 1024 / 1024)}MB | ` +
         `Calculated: ${constants.calculatedAt.toISOString()}`;
}

/**
 * Force recalculation of environment constants
 */
export async function recalculateEnvironmentConstants(): Promise<ICalculatedConstants> {
  environmentCalculatorInstance.invalidateCache();
  return environmentCalculatorInstance.calculateConstants();
}

/**
 * Gracefully shutdown the environment calculator
 */
export async function shutdownEnvironmentCalculator(): Promise<void> {
  await environmentCalculatorInstance.shutdown();
}

// ============================================================================
// EXPORTS
// ============================================================================

export default EnvironmentConstantsCalculator;

// ❌ REMOVED: Immediate instantiation that caused memory leaks
// export const environmentCalculator = EnvironmentConstantsCalculator.getInstance();

// ✅ ADDED: Lazy getter function that prevents immediate instantiation
export function getEnvironmentCalculator(): EnvironmentConstantsCalculator {
  return environmentCalculatorInstance;
}
