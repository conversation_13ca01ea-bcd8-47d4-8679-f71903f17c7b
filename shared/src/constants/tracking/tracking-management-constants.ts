/**
 * @file Tracking Management Constants
 * @filepath shared/src/constants/tracking/tracking-management-constants.ts
 * @task-id T-TSK-03.SUB-03.2.IMP-04
 * @component tracking-tracking-constants
 * @reference foundation-context.CONSTANT.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/tracking/tracking-management-types
 * @enables server/src/platform/tracking/core-managers
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type constants
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/constants/tracking-management-constants.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { TManagerConfig } from '../../types/tracking/tracking-management-types';

// ============================================================================
// MANAGER CONFIGURATION CONSTANTS
// ============================================================================

/**
 * Default Manager Configuration
 * Standard configuration for all managers
 */
export const DEFAULT_MANAGER_CONFIG: TManagerConfig = {
  id: 'default-manager',
  name: 'Default Manager',
  version: '1.0.0',
  debug: false,
  logLevel: 'info',
  monitoring: {
    enabled: true,
    interval: 30000, // 30 seconds
    metrics: ['performance', 'operations', 'resources']
  },
  security: {
    enabled: true,
    encryption: true,
    authentication: true
  },
  cache: {
    enabled: true,
    ttl: 300000, // 5 minutes
    maxSize: 1000
  },
  retry: {
    enabled: true,
    maxAttempts: 3,
    backoffMs: 1000
  },
  timeout: {
    operation: 30000, // 30 seconds
    connection: 10000, // 10 seconds
    idle: 300000 // 5 minutes
  },
  custom: {}
};

/**
 * Tracking Manager Configuration
 * Specific configuration for tracking manager
 */
export const TRACKING_MANAGER_CONFIG: Partial<TManagerConfig> = {
  id: 'tracking-manager',
  name: 'Tracking Manager',
  monitoring: {
    enabled: true,
    interval: 15000, // 15 seconds
    metrics: ['performance', 'operations', 'resources', 'tracking']
  },
  custom: {
    maxConcurrentOperations: 100,
    trackingBufferSize: 1000,
    batchProcessingSize: 50
  }
};

/**
 * File Manager Configuration
 * Specific configuration for file manager
 */
export const FILE_MANAGER_CONFIG: Partial<TManagerConfig> = {
  id: 'file-manager',
  name: 'File Manager',
  timeout: {
    operation: 60000, // 1 minute for file operations
    connection: 5000,
    idle: 600000 // 10 minutes
  },
  custom: {
    maxFileSize: 104857600, // 100MB
    allowedExtensions: ['.json', '.log', '.txt', '.csv'],
    backupEnabled: true,
    compressionEnabled: true
  }
};

/**
 * Real Time Manager Configuration
 * Specific configuration for real-time manager
 */
export const REALTIME_MANAGER_CONFIG: Partial<TManagerConfig> = {
  id: 'realtime-manager',
  name: 'Real Time Manager',
  monitoring: {
    enabled: true,
    interval: 5000, // 5 seconds for real-time monitoring
    metrics: ['performance', 'operations', 'resources', 'connections']
  },
  custom: {
    maxConnections: 1000,
    heartbeatInterval: 30000, // 30 seconds
    messageQueueSize: 10000,
    broadcastThrottle: 100 // messages per second
  }
};

/**
 * Dashboard Manager Configuration
 * Specific configuration for dashboard manager
 */
export const DASHBOARD_MANAGER_CONFIG: Partial<TManagerConfig> = {
  id: 'dashboard-manager',
  name: 'Dashboard Manager',
  cache: {
    enabled: true,
    ttl: 60000, // 1 minute for dashboard data
    maxSize: 500
  },
  custom: {
    maxWidgetsPerDashboard: 20,
    refreshInterval: 30000, // 30 seconds
    exportFormats: ['json', 'csv', 'pdf'],
    maxDashboards: 100
  }
};

// ============================================================================
// OPERATION CONSTANTS
// ============================================================================

/**
 * Manager Operations
 * Standard operations for all managers
 */
export const MANAGER_OPERATIONS = {
  INITIALIZE: 'initialize',
  START: 'start',
  STOP: 'stop',
  SHUTDOWN: 'shutdown',
  GET_STATUS: 'getStatus',
  GET_METRICS: 'getMetrics',
  GET_HEALTH: 'getHealth'
} as const;

/**
 * File Operations
 * File system operations
 */
export const FILE_OPERATIONS = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  LIST: 'list',
  STATS: 'stats',
  WATCH: 'watch',
  MKDIR: 'mkdir',
  COPY: 'copy',
  MOVE: 'move'
} as const;

/**
 * Real Time Operations
 * Real-time operations
 */
export const REALTIME_OPERATIONS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  SUBSCRIBE: 'subscribe',
  UNSUBSCRIBE: 'unsubscribe',
  BROADCAST: 'broadcast',
  SEND_MESSAGE: 'sendMessage',
  GET_CONNECTIONS: 'getConnections'
} as const;

/**
 * Dashboard Operations
 * Dashboard operations
 */
export const DASHBOARD_OPERATIONS = {
  GET_DATA: 'getData',
  UPDATE_DATA: 'updateData',
  CREATE: 'create',
  DELETE: 'delete',
  LIST: 'list',
  EXPORT: 'export',
  IMPORT: 'import'
} as const;

// ============================================================================
// STATUS CONSTANTS
// ============================================================================

/**
 * Manager Status Values
 * Possible status values for managers
 */
export const MANAGER_STATUS = {
  INITIALIZING: 'initializing',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ERROR: 'error',
  SHUTDOWN: 'shutdown'
} as const;

/**
 * Health Status Values
 * Health status indicators
 */
export const HEALTH_STATUS = {
  HEALTHY: 'healthy',
  DEGRADED: 'degraded',
  UNHEALTHY: 'unhealthy'
} as const;

/**
 * Connection Status Values
 * Real-time connection status
 */
export const CONNECTION_STATUS = {
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  DISCONNECTING: 'disconnecting',
  DISCONNECTED: 'disconnected'
} as const;

// ============================================================================
// ERROR CONSTANTS
// ============================================================================

/**
 * Manager Error Codes
 * Error codes for manager operations
 */
export const MANAGER_ERROR_CODES = {
  INITIALIZATION_FAILED: 'MANAGER_INIT_FAILED',
  OPERATION_FAILED: 'MANAGER_OP_FAILED',
  CONFIGURATION_INVALID: 'MANAGER_CONFIG_INVALID',
  TIMEOUT_EXCEEDED: 'MANAGER_TIMEOUT',
  RESOURCE_UNAVAILABLE: 'MANAGER_RESOURCE_UNAVAILABLE',
  PERMISSION_DENIED: 'MANAGER_PERMISSION_DENIED',
  SERVICE_UNAVAILABLE: 'MANAGER_SERVICE_UNAVAILABLE'
} as const;

/**
 * File Error Codes
 * Error codes for file operations
 */
export const FILE_ERROR_CODES = {
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  PERMISSION_DENIED: 'FILE_PERMISSION_DENIED',
  DISK_FULL: 'FILE_DISK_FULL',
  INVALID_PATH: 'FILE_INVALID_PATH',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FORMAT: 'FILE_INVALID_FORMAT',
  OPERATION_FAILED: 'FILE_OP_FAILED'
} as const;

/**
 * Real Time Error Codes
 * Error codes for real-time operations
 */
export const REALTIME_ERROR_CODES = {
  CONNECTION_FAILED: 'RT_CONNECTION_FAILED',
  SUBSCRIPTION_FAILED: 'RT_SUBSCRIPTION_FAILED',
  MESSAGE_FAILED: 'RT_MESSAGE_FAILED',
  BROADCAST_FAILED: 'RT_BROADCAST_FAILED',
  CLIENT_NOT_FOUND: 'RT_CLIENT_NOT_FOUND',
  MAX_CONNECTIONS: 'RT_MAX_CONNECTIONS',
  INVALID_MESSAGE: 'RT_INVALID_MESSAGE'
} as const;

// ============================================================================
// PERFORMANCE CONSTANTS
// ============================================================================

/**
 * Performance Thresholds
 * Performance monitoring thresholds
 */
export const PERFORMANCE_THRESHOLDS = {
  RESPONSE_TIME_WARNING: 1000, // 1 second
  RESPONSE_TIME_CRITICAL: 5000, // 5 seconds
  ERROR_RATE_WARNING: 0.05, // 5%
  ERROR_RATE_CRITICAL: 0.10, // 10%
  MEMORY_WARNING: 512, // 512MB
  MEMORY_CRITICAL: 1024, // 1GB
  CPU_WARNING: 70, // 70%
  CPU_CRITICAL: 90 // 90%
} as const;

/**
 * Cache Constants
 * Caching configuration constants
 */
export const CACHE_CONSTANTS = {
  DEFAULT_TTL: 300000, // 5 minutes
  MAX_SIZE: 1000,
  CLEANUP_INTERVAL: 60000, // 1 minute
  HIT_RATIO_THRESHOLD: 0.8 // 80%
} as const;

/**
 * Queue Constants
 * Queue processing constants
 */
export const QUEUE_CONSTANTS = {
  MAX_SIZE: 10000,
  BATCH_SIZE: 100,
  PROCESSING_INTERVAL: 1000, // 1 second
  RETRY_DELAY: 5000 // 5 seconds
} as const;

// ============================================================================
// VALIDATION CONSTANTS
// ============================================================================

/**
 * Validation Rules
 * Input validation constants
 */
export const VALIDATION_RULES = {
  MAX_STRING_LENGTH: 1000,
  MAX_ARRAY_LENGTH: 1000,
  MAX_OBJECT_DEPTH: 10,
  REQUIRED_FIELDS: ['id', 'name', 'version'],
  ALLOWED_LOG_LEVELS: ['debug', 'info', 'warn', 'error'],
  MIN_TIMEOUT: 1000, // 1 second
  MAX_TIMEOUT: 300000 // 5 minutes
} as const;

// ============================================================================
// LOGGING CONSTANTS
// ============================================================================

/**
 * Log Levels
 * Logging level constants
 */
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
} as const;

/**
 * Log Categories
 * Log category constants
 */
export const LOG_CATEGORIES = {
  MANAGER: 'manager',
  FILE: 'file',
  REALTIME: 'realtime',
  DASHBOARD: 'dashboard',
  PERFORMANCE: 'performance',
  SECURITY: 'security'
} as const; 