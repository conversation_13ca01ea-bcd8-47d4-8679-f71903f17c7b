/**
 * @file Core Tracking Management Types
 * @filepath shared/src/types/tracking/core-types.ts
 * @task-id T-TSK-03.SUB-03.2.IMP-02A
 * @component tracking-core-types
 * @reference foundation-context.TYPE.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @enables server/src/platform/tracking/core-managers
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/core-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

// ============================================================================
// CORE MANAGEMENT TYPES
// ============================================================================

/**
 * Manager Status Type
 * Represents the operational status of a manager
 */
export type TManagerStatus = 'initializing' | 'active' | 'inactive' | 'error' | 'shutdown';

/**
 * Manager Configuration Type
 * Configuration options for management services
 */
export type TManagerConfig = {
  /** Manager identifier */
  id: string;
  /** Manager name */
  name: string;
  /** Manager version */
  version: string;
  /** Enable debug mode */
  debug: boolean;
  /** Log level */
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  /** Performance monitoring */
  monitoring: {
    enabled: boolean;
    interval: number;
    metrics: string[];
  };
  /** Security settings */
  security: {
    enabled: boolean;
    encryption: boolean;
    authentication: boolean;
  };
  /** Cache configuration */
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
  /** Retry configuration */
  retry: {
    enabled: boolean;
    maxAttempts: number;
    backoffMs: number;
  };
  /** Timeout configuration */
  timeout: {
    operation: number;
    connection: number;
    idle: number;
  };
  /** Custom configuration */
  custom: Record<string, any>;
};

/**
 * Manager Metrics Type
 * Performance and operational metrics for managers
 */
export type TManagerMetrics = {
  /** Timestamp of metrics */
  timestamp: string;
  /** Manager identifier */
  managerId: string;
  /** Manager status */
  status: TManagerStatus;
  /** Uptime in milliseconds */
  uptime: number;
  /** Performance metrics */
  performance: {
    /** Average response time */
    avgResponseTime: number;
    /** Operations per second */
    operationsPerSecond: number;
    /** Memory usage in MB */
    memoryUsage: number;
    /** CPU usage percentage */
    cpuUsage: number;
    /** Error rate percentage */
    errorRate: number;
  };
  /** Operation counters */
  operations: {
    /** Total operations */
    total: number;
    /** Successful operations */
    successful: number;
    /** Failed operations */
    failed: number;
    /** Operations by type */
    byType: Record<string, number>;
  };
  /** Resource usage */
  resources: {
    /** Active connections */
    connections: number;
    /** File handles */
    fileHandles: number;
    /** Cache entries */
    cacheEntries: number;
    /** Queue size */
    queueSize: number;
  };
  /** Custom metrics */
  custom: Record<string, any>;
};

/**
 * Operation Result Type
 * Generic operation result
 */
export type TOperationResult<T = any> = {
  /** Operation success status */
  success: boolean;
  /** Operation result data */
  data?: T;
  /** Error information */
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  /** Operation metadata */
  metadata: {
    /** Operation timestamp */
    timestamp: string;
    /** Operation duration */
    duration: number;
    /** Operation ID */
    operationId: string;
  };
};

/**
 * Health Status Type
 * Service health status
 */
export type THealthStatus = {
  /** Overall health status */
  status: 'healthy' | 'degraded' | 'unhealthy';
  /** Health checks */
  checks: THealthCheck[];
  /** Health timestamp */
  timestamp: string;
  /** Health metadata */
  metadata: Record<string, any>;
};

/**
 * Health Check Type
 * Individual health check result
 */
export type THealthCheck = {
  /** Check name */
  name: string;
  /** Check status */
  status: 'pass' | 'warn' | 'fail';
  /** Check message */
  message?: string;
  /** Check details */
  details?: any;
  /** Check timestamp */
  timestamp: string;
};

/**
 * Configuration Validation Type
 * Configuration validation result
 */
export type TConfigValidation = {
  /** Validation success status */
  valid: boolean;
  /** Validation errors */
  errors: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
  /** Validation warnings */
  warnings: Array<{
    field: string;
    message: string;
    value?: any;
  }>;
  /** Validation metadata */
  metadata: {
    /** Validation timestamp */
    timestamp: string;
    /** Validation version */
    version: string;
  };
};

// ============================================================================
// RE-EXPORT COMMON TYPES
// ============================================================================

// Re-export commonly used types from the main tracking types
export type {
  TTrackingData,
  TValidationResult,
  TTrackingConfig
} from '../platform/tracking/tracking-types';