/**
 * @file Notification Types
 * @filepath shared/src/types/platform/governance/notification-types.ts
 * @task-id M-TSK-01.SUB-04.1.TYP-06
 * @component notification-types
 * @reference foundation-context.TYPES.006
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Type-Definitions
 * @created 2025-07-28 16:00:00 +03
 * @modified 2025-07-28 16:00:00 +03
 *
 * @description
 * Notification types module providing:
 * - Type definitions for notification system components
 * - Notification configuration and channel type structures
 * - Notification template and recipient type definitions
 * - Enterprise-grade notification type structures
 * - Performance-optimized type definitions for notification operations
 * - Integration type definitions for notification system coordination
 * - Type safety for notification processing and delivery
 * - Comprehensive type coverage for notification functionality
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-014-notification-types-architecture
 * @governance-dcr DCR-foundation-014-notification-types-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/governance/governance-types
 * @enables shared/src/interfaces/tracking/notification-interfaces
 * @enables server/src/platform/governance/automation-processing/NotificationProcessor
 * @related-contexts foundation-context, notification-context, type-definitions-context
 * @governance-impact framework-foundation, notification-system, type-safety
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type type-definitions
 * @lifecycle-stage implementation
 * @testing-status type-checked
 * @deployment-ready true
 * @monitoring-enabled false
 * @documentation docs/contexts/notification-context/types/notification-types.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   anti-simplification-compliant: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-28) - Initial notification types implementation
 * v1.1.0 (2025-07-28) - Added comprehensive type structures for notification system
 */

/**
 * Notification configuration type
 */
export type TNotificationConfig = {
  channels: TNotificationChannel[];
  recipients: TNotificationRecipient[];
  template?: string;
  priority: TNotificationPriority;
  metadata: Record<string, any>;
};

/**
 * Notification channel type
 */
export type TNotificationChannel = {
  channelId: string;
  type: 'email' | 'sms' | 'slack' | 'teams' | 'webhook';
  config: Record<string, any>;
  status: string;
  lastAttemptTime?: Date;
  metadata: Record<string, any>;
};

/**
 * Notification template type
 */
export type TNotificationTemplate = {
  templateId: string;
  name: string;
  description: string;
  content: string;
  variables: string[];
  channels: string[];
  version: string;
  metadata: Record<string, any>;
};

/**
 * Notification event type
 */
export type TNotificationEvent = {
  eventId: string;
  type: string;
  source: string;
  priority: TNotificationPriority;
  timestamp: Date;
  data: Record<string, any>;
  metadata: Record<string, any>;
};

/**
 * Notification result type
 */
export type TNotificationResult = {
  eventId: string;
  status: string;
  timestamp: Date;
  channels: TNotificationStatus[];
  metadata: Record<string, any>;
};

/**
 * Notification status type
 */
export type TNotificationStatus = {
  eventId: string;
  status: string;
  timestamp: Date;
  deliveryAttempts: number;
  lastAttemptTime?: Date;
  channels: Array<{
    channelId: string;
    status: string;
    lastAttemptTime?: Date;
  }>;
};

/**
 * Notification priority type
 */
export type TNotificationPriority = 'low' | 'medium' | 'high' | 'critical';

/**
 * Notification recipient type
 */
export type TNotificationRecipient = {
  recipientId: string;
  type: 'user' | 'group' | 'role';
  address: string;
  name?: string;
  metadata: Record<string, any>;
}; 