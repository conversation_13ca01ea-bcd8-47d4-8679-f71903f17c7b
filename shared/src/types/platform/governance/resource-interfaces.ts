/**
 * @file Resource Management Interfaces
 * @filepath shared/src/types/platform/governance/resource-interfaces.ts
 * @task-id G-TSK-01.SUB-01.1.INT-02
 * @component resource-interfaces
 * @reference foundation-context.GOVERNANCE.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T1
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24 18:33:55 +03
 * 
 * @description
 * Resource management interfaces for enterprise governance system providing:
 * - Resource allocation and tracking interfaces
 * - Resource metrics and monitoring interfaces
 * - Resource validation and audit interfaces
 * - Resource security and access control interfaces
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-architecture
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.TRACKING.tracking-types
 * @enables governance-resource-management-system
 * @related-contexts foundation-context, enterprise-context
 * @governance-impact framework-foundation, governance-infrastructure
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status interface-validated
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/interfaces/resource-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { IGovernanceService } from './governance-interfaces';
import { TValidationResult, TAuditResult } from '../tracking/tracking-types';

// ============================================================================
// RESOURCE MANAGEMENT INTERFACES
// ============================================================================

/**
 * Resource manager interface
 * Manages resource allocation and tracking
 */
export interface IResourceManager extends IGovernanceService {
  /**
   * Allocate resource
   * @param resourceType - Type of resource to allocate
   * @param amount - Amount to allocate
   * @param options - Allocation options
   */
  allocateResource(
    resourceType: string,
    amount: number,
    options?: {
      ownerId?: string;
      securityLevel?: string;
      accessToken?: string;
      timeout?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<string>;

  /**
   * Deallocate resource
   * @param allocationId - Allocation identifier
   * @param accessToken - Optional access token
   */
  deallocateResource(allocationId: string, accessToken?: string): Promise<void>;

  /**
   * Get resource allocation
   * @param allocationId - Allocation identifier
   * @param accessToken - Optional access token
   */
  getAllocation(allocationId: string, accessToken?: string): Promise<any>;

  /**
   * Get resource metrics
   */
  getResourceMetrics(): Promise<any>;

  /**
   * Validate resource allocation
   * @param allocationId - Allocation identifier
   * @param accessToken - Optional access token
   */
  validateAllocation(allocationId: string, accessToken?: string): Promise<TValidationResult>;

  /**
   * Audit resource allocation
   * @param allocationId - Allocation identifier
   * @param accessToken - Optional access token
   */
  auditAllocation(allocationId: string, accessToken?: string): Promise<TAuditResult>;
} 