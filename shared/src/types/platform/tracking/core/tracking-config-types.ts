/**
 * @file Core Tracking Configuration Types
 * @filepath shared/src/types/platform/tracking/core/tracking-config-types.ts
 * @reference foundation-context.TYPES.004
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E<PERSON>Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type config-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/config-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Core Tracking Configuration Types
 * 
 * Configuration types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

import type { 
  TRetryConfig, 
  TAlertThresholds, 
  TAlertConfig 
} from './base-types';

// ============================================================================
// MAIN CONFIGURATION TYPES
// ============================================================================

/**
 * Primary tracking configuration structure
 * Contains all tracking system configuration
 */
export type TTrackingConfig = {
  /** Service configuration */
  service: TServiceConfig;
  
  /** Governance configuration */
  governance: TGovernanceConfig;
  
  /** Performance configuration */
  performance: TPerformanceConfig;
  
  /** Logging configuration */
  logging: TLoggingConfig;
};

/**
 * Service configuration structure
 * Contains core service settings
 */
export type TServiceConfig = {
  /** Service name */
  name: string;
  
  /** Service version */
  version: string;
  
  /** Service environment */
  environment: 'development' | 'staging' | 'production';
  
  /** Service timeout (ms) */
  timeout: number;
  
  /** Retry configuration */
  retry: TRetryConfig;
};

/**
 * Governance configuration structure
 * Contains governance and compliance settings
 */
export type TGovernanceConfig = {
  /** Authority validator */
  authority: string;
  
  /** Required compliance checks */
  requiredCompliance: string[];
  
  /** Audit frequency (hours) */
  auditFrequency: number;
  
  /** Violation reporting enabled */
  violationReporting: boolean;
};

/**
 * Performance configuration structure
 * Contains performance monitoring settings
 */
export type TPerformanceConfig = {
  /** Metrics collection enabled */
  metricsEnabled: boolean;
  
  /** Metrics collection interval (ms) */
  metricsInterval: number;
  
  /** Performance monitoring enabled */
  monitoringEnabled: boolean;
  
  /** Alert thresholds */
  alertThresholds: TAlertThresholds;

  /** Rate limiting configuration */
  rateLimitThreshold?: number;
  rateLimitWindowMs?: number;

  /** Flooding protection configuration */
  floodingThreshold?: number;
  floodingWindowMs?: number;
};

/**
 * Logging configuration structure
 * Contains logging system settings
 */
export type TLoggingConfig = {
  /** Log level */
  level: 'debug' | 'info' | 'warn' | 'error';
  
  /** Log format */
  format: 'json' | 'text';
  
  /** Log file path */
  filePath?: string;
  
  /** Log rotation enabled */
  rotation: boolean;
  
  /** Max log file size (MB) */
  maxFileSize: number;
};

// ============================================================================
// SPECIALIZED CONFIGURATION TYPES
// ============================================================================

/**
 * Analytics cache configuration
 * Contains caching system settings
 */
export type TAnalyticsCacheConfig = {
  maxCacheSize?: number;
  cacheTTL?: number;
  [key: string]: any;
};

/**
 * Path resolution configuration
 * Contains path resolution settings
 */
export type TPathResolutionConfig = {
  maxCacheSize?: number;
  [key: string]: any;
};

/**
 * Cross reference configuration
 * Contains cross-reference validation settings
 */
export type TCrossReferenceConfig = {
  maxHistorySize?: number;
  [key: string]: any;
};

/**
 * Context authority configuration
 * Contains authority validation settings
 */
export type TContextAuthorityConfig = {
  maxCacheSize?: number;
  cacheTTL?: number;
  [key: string]: any;
};

// ============================================================================
// ORCHESTRATION CONFIGURATION TYPES
// ============================================================================

/**
 * Orchestration configuration structure
 * Contains comprehensive orchestration settings
 */
export type TOrchestrationConfig = {
  /** Orchestration mode */
  mode: 'sequential' | 'parallel' | 'adaptive' | 'intelligent';
  
  /** Timeout configuration */
  timeout: {
    workflow: number;
    service: number;
    coordination: number;
  };
  
  /** Retry configuration */
  retry: {
    maxAttempts: number;
    backoffStrategy: 'linear' | 'exponential' | 'adaptive';
    initialDelay: number;
    maxDelay: number;
  };
  
  /** Monitoring configuration */
  monitoring: {
    enabled: boolean;
    interval: number;
    metrics: string[];
    alerts: TAlertConfig[];
  };
  
  /** Security configuration */
  security: {
    authentication: boolean;
    authorization: boolean;
    encryption: boolean;
    auditLogging: boolean;
  };
  
  /** Performance configuration */
  performance: {
    maxConcurrentWorkflows: number;
    resourceLimits: TResourceLimits;
    optimization: boolean;
  };
};

/**
 * Service coordination configuration
 * Contains service coordination settings
 */
export type TServiceCoordinationConfig = {
  priority: number;
  timeout: number;
  retries: number;
  healthCheckInterval: number;
  dependencies: string[];
  coordination: {
    pattern: 'request-response' | 'publish-subscribe' | 'message-queue';
    consistency: 'strong' | 'eventual';
    isolation: 'read-uncommitted' | 'read-committed' | 'repeatable-read' | 'serializable';
  };
};

// Import resource limits from base types
import type { TResourceLimits } from './base-types';