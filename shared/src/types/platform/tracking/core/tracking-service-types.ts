/**
 * @file Core Tracking Service Interfaces
 * @filepath shared/src/types/platform/tracking/core/tracking-service-types.ts
 * @reference foundation-context.TYPES.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type core-service-interfaces
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/service-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Core Tracking Service Interfaces
 * 
 * Core service interfaces for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

import type { 
  TAuthorityLevel, 
  TRealtimeCallback, 
  TRealtimeData 
} from './base-types';

// Import forward declarations for types defined in other files
import type { 
  TTrackingData, 
  TValidationResult, 
  TMetrics,
  TGovernanceValidation,
  TAuditResult,
  TGovernanceStatus,
  TGovernanceViolation
} from './tracking-data-types';

// ============================================================================
// CORE SERVICE INTERFACES
// ============================================================================

/**
 * Base tracking service interface
 * Foundation interface for all tracking services
 */
export interface ITrackingService {
  /**
   * Initialize the tracking service
   * Must be called before any other operations
   */
  initialize(): Promise<void>;

  /**
   * Track data with governance validation
   * @param data - The tracking data to process
   */
  track(data: TTrackingData): Promise<void>;

  /**
   * Validate service state and data integrity
   * @returns Validation result with errors and warnings
   */
  validate(): Promise<TValidationResult>;

  /**
   * Get service metrics and performance data
   * @returns Current service metrics
   */
  getMetrics(): Promise<TMetrics>;

  /**
   * Check if service is initialized and ready
   * @returns True if service is ready for operations
   */
  isReady(): boolean;

  /**
   * Shutdown the service gracefully
   */
  shutdown(): Promise<void>;
}

/**
 * Governance trackable service interface
 * Extends base tracking with governance capabilities
 */
export interface IGovernanceTrackable extends ITrackingService {
  /**
   * Validate governance compliance
   * @returns Governance validation result
   */
  validateGovernance(): Promise<TGovernanceValidation>;

  /**
   * Audit compliance status
   * @returns Comprehensive audit result
   */
  auditCompliance(): Promise<TAuditResult>;

  /**
   * Get governance status
   * @returns Current governance compliance status
   */
  getGovernanceStatus(): Promise<TGovernanceStatus>;

  /**
   * Report governance violations
   * @param violation - The governance violation to report
   */
  reportViolation(violation: TGovernanceViolation): Promise<void>;
}

/**
 * Session tracking service interface
 * Manages session lifecycle and event logging
 */
export interface ISessionTracking extends ITrackingService {
  /**
   * Start a new session
   * @param sessionId - Unique session identifier
   * @param actor - User or system starting the session
   * @param sessionType - Type of session (user, system, api, background)
   * @param metadata - Additional session metadata
   * @returns Session data
   */
  startSession(
    sessionId: string,
    actor: string,
    sessionType?: 'user' | 'system' | 'api' | 'background',
    metadata?: Record<string, unknown>
  ): Promise<any>;

  /**
   * End an active session
   * @param sessionId - Session identifier to end
   * @param reason - Optional reason for ending session
   */
  endSession(sessionId: string, reason?: string): Promise<void>;

  /**
   * Log an event to a session
   * @param sessionId - Session identifier
   * @param level - Log level
   * @param eventType - Type of event
   * @param message - Event message
   * @param context - Additional event context
   * @param error - Error information if applicable
   */
  logSessionEvent(
    sessionId: string,
    level: 'info' | 'warn' | 'error' | 'debug',
    eventType: string,
    message: string,
    context?: Record<string, unknown>,
    error?: { code: string; message: string; stack?: string }
  ): Promise<void>;

  /**
   * Get session data
   * @param sessionId - Session identifier
   * @returns Session data or null if not found
   */
  getSessionData(sessionId: string): Promise<any | null>;

  /**
   * Get all active sessions
   * @returns Array of active session data
   */
  getActiveSessions(): Promise<any[]>;

  /**
   * Get session history
   * @param sessionId - Session identifier
   * @returns Array of session events
   */
  getSessionHistory(sessionId: string): Promise<any[]>;

  /**
   * Get session analytics
   * @returns Session analytics data
   */
  getSessionAnalytics(): Promise<any>;

  /**
   * Subscribe to real-time session events
   * @param sessionId - Session identifier
   * @param callback - Callback function for events
   * @returns Subscription identifier
   */
  subscribeToRealtimeEvents(sessionId: string, callback: TRealtimeCallback): Promise<string>;
}

/**
 * Auditable service interface
 * Provides comprehensive audit trail capabilities
 */
export interface IAuditableService extends ITrackingService {
  /**
   * Generate comprehensive audit trail
   * @param options - Audit generation options
   * @returns Audit trail data
   */
  generateAuditTrail(options?: {
    startDate?: Date;
    endDate?: Date;
    includeDetails?: boolean;
  }): Promise<any>;

  /**
   * Get audit history
   * @param limit - Maximum number of audit entries to return
   * @returns Array of audit entries
   */
  getAuditHistory(limit?: number): Promise<any[]>;

  /**
   * Export audit data in specified format
   * @param format - Export format (json, csv, xml)
   * @param options - Export options
   * @returns Exported audit data as string
   */
  exportAuditData(
    format: 'json' | 'csv' | 'xml',
    options?: {
      startDate?: Date;
      endDate?: Date;
      includeMetadata?: boolean;
    }
  ): Promise<string>;

  /**
   * Perform compliance audit
   * @param auditType - Type of audit to perform
   * @returns Audit result
   */
  performComplianceAudit(auditType?: 'full' | 'security' | 'governance' | 'performance'): Promise<TAuditResult>;

  /**
   * Get audit metrics
   * @returns Audit performance and compliance metrics
   */
  getAuditMetrics(): Promise<any>;

  /**
   * Schedule automatic audit
   * @param frequency - Audit frequency in hours
   * @param auditType - Type of audit to schedule
   * @returns Scheduled audit identifier
   */
  scheduleAudit(frequency: number, auditType?: string): Promise<string>;
}

/**
 * Real-time service interface
 * Manages real-time monitoring and data streaming
 */
export interface IRealtimeService extends ITrackingService {
  /**
   * Start real-time monitoring
   */
  startMonitoring(): Promise<void>;

  /**
   * Stop real-time monitoring
   */
  stopMonitoring(): Promise<void>;

  /**
   * Get real-time data
   * @returns Current real-time tracking data
   */
  getRealtimeData(): Promise<TRealtimeData>;

  /**
   * Subscribe to real-time updates
   * @param callback - Function to call when data updates
   */
  subscribe(callback: TRealtimeCallback): string;

  /**
   * Unsubscribe from real-time updates
   * @param subscriptionId - ID of subscription to remove
   */
  unsubscribe(subscriptionId: string): void;
}

/**
 * Tracking data interface
 * Provides tracking data access and manipulation
 */
export interface ITrackingData {
  /**
   * Get tracking data for this entity
   * @returns Current tracking data
   */
  getTrackingData(): TTrackingData;

  /**
   * Update tracking status
   * @param status - New tracking status
   */
  updateStatus(status: any): Promise<void>;

  /**
   * Get tracking history
   * @returns Historical tracking data
   */
  getHistory(): Promise<any[]>;
} 