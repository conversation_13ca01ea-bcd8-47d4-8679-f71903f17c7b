/**
 * @file Base Tracking Types and Enums
 * @filepath shared/src/types/platform/tracking/core/base-types.ts
 * @reference foundation-context.TYPES.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/constants/platform/tracking
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type base-tracking-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/base-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Base Tracking Types
 * 
 * Foundation types and enums for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

// ============================================================================
// FOUNDATION TYPES
// ============================================================================

/**
 * Base tracking service definition
 */
export type TTrackingService = {
  componentId: string;
  componentName: string;
  version: string;
  authority: string;
  complianceLevel: string;
  [key: string]: any;
};

/**
 * Authority levels for governance validation
 */
export type TAuthorityLevel = 
  | 'low' 
  | 'standard' 
  | 'high' 
  | 'critical' 
  | 'architectural-authority' 
  | 'maximum';

/**
 * Component status enumeration
 */
export type TComponentStatus = 
  | 'not-started'
  | 'planning'
  | 'in-progress'
  | 'testing'
  | 'review'
  | 'completed'
  | 'blocked'
  | 'failed'
  | 'deprecated';

/**
 * Validation status enumeration
 */
export type TValidationStatus = 
  | 'pending'
  | 'validated'
  | 'rejected'
  | 'expired';

/**
 * Health status enumeration
 */
export type THealthStatus = {
  status: 'healthy' | 'degraded' | 'critical';
  timestamp: Date;
  details: Record<string, any>;
};

/**
 * Service health status enumeration
 */
export type TServiceHealthStatus = {
  status: 'healthy' | 'degraded' | 'critical' | 'offline';
  timestamp: Date;
  uptime: number;
  details: Record<string, any>;
};

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

/**
 * Retry configuration for resilient operations
 */
export type TRetryConfig = {
  /** Maximum retry attempts */
  maxAttempts: number;
  
  /** Retry delay (ms) */
  delay: number;
  
  /** Backoff multiplier */
  backoffMultiplier: number;
  
  /** Maximum delay (ms) */
  maxDelay: number;
};

/**
 * Alert thresholds configuration
 */
export type TAlertThresholds = {
  /** Response time threshold (ms) */
  responseTime: number;
  
  /** Error rate threshold (%) */
  errorRate: number;
  
  /** Memory usage threshold (MB) */
  memoryUsage: number;
  
  /** CPU usage threshold (%) */
  cpuUsage: number;
};

// ============================================================================
// CALLBACK TYPES
// ============================================================================

/**
 * Real-time data structure
 */
export type TRealtimeData = {
  /** Session identifier */
  sessionId: string;
  
  /** Data timestamp */
  timestamp: string;
  
  /** Actor identifier */
  actor: string;
  
  /** Total event count */
  eventCount: number;
  
  /** Session status */
  status: 'active' | 'ended' | 'expired' | 'terminated';
  
  /** Performance metrics */
  performance: {
    totalEvents: number;
    eventsByLevel: Record<string, number>;
    avgProcessingTime: number;
    peakMemoryUsage: number;
    efficiencyScore: number;
  };
  
  /** Quality metrics */
  quality: {
    errorRate: number;
    warningRate: number;
    complianceScore: number;
    authorityValidationRate: number;
  };
};

/**
 * Real-time callback function type
 */
export type TRealtimeCallback = (data: TRealtimeData) => void;

// ============================================================================
// SERVICE CONFIGURATION TYPES
// ============================================================================

/**
 * Service configuration options
 */
export type ITrackingServiceOptions = {
  componentId?: string;
  serviceType?: string;
  version?: string;
  authority?: string;
  context?: string;
  [key: string]: any;
};

/**
 * Service events tracking
 */
export type TTrackingServiceEvents = {
  initialized: Date;
  started: Date;
  stopped: Date;
  error: Date;
  [key: string]: Date;
};

/**
 * Service configuration definition
 */
export type TServiceConfiguration = {
  componentId: string;
  serviceType: string;
  version: string;
  authority: string;
  context: string;
  capabilities: string[];
  configuration: Record<string, any>;
};

// ============================================================================
// RESOURCE TYPES
// ============================================================================

/**
 * Resource requirements specification
 */
export type TResourceRequirements = {
  cpu: string;
  memory: string;
  storage: string;
  network: string;
};

/**
 * Resource limits specification
 */
export type TResourceLimits = {
  maxCpu: string;
  maxMemory: string;
  maxStorage: string;
  maxNetworkBandwidth: string;
};

/**
 * Alert configuration
 */
export type TAlertConfig = {
  name: string;
  condition: string;
  threshold: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  actions: string[];
}; 