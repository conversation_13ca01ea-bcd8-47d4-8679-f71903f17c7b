/**
 * @file Core Tracking Data Types
 * @filepath shared/src/types/platform/tracking/core/tracking-data-types.ts
 * @reference foundation-context.TYPES.003
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type core-data-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/data-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Core Tracking Data Types
 * 
 * Core data structures for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

import type { 
  TAuthorityLevel, 
  TComponentStatus, 
  TValidationStatus 
} from './base-types';

// ============================================================================
// CORE TRACKING DATA STRUCTURES
// ============================================================================

/**
 * Primary tracking data structure
 * Contains all essential tracking information for a component
 */
export type TTrackingData = {
  /** Unique identifier for the component being tracked */
  componentId: string;
  
  /** Current status of the component */
  status: TComponentStatus;
  
  /** Timestamp when this tracking data was created */
  timestamp: string;
  
  /** Additional metadata for the tracking entry */
  metadata: TTrackingMetadata;
  
  /** Context information */
  context: TTrackingContext;
  
  /** Progress information */
  progress: TProgressData;
  
  /** Authority validation info */
  authority: TAuthorityData;
};

/**
 * Tracking metadata structure
 * Contains development and project metadata
 */
export type TTrackingMetadata = {
  /** Phase of development */
  phase: string;
  
  /** Progress percentage (0-100) */
  progress: number;
  
  /** Priority level */
  priority: 'P0' | 'P1' | 'P2' | 'P3';
  
  /** Estimated completion date */
  estimatedCompletion?: string;
  
  /** Assigned developer/team */
  assignee?: string;
  
  /** Tags for categorization */
  tags: string[];
  
  /** Additional custom metadata */
  custom: Record<string, unknown>;
};

/**
 * Tracking context structure
 * Contains contextual information about the component
 */
export type TTrackingContext = {
  /** Context identifier (foundation, authentication, etc.) */
  contextId: string;
  
  /** Milestone identifier */
  milestone: string;
  
  /** Component category */
  category: string;
  
  /** Dependencies */
  dependencies: string[];
  
  /** Components that depend on this one */
  dependents: string[];
};

/**
 * Progress data structure
 * Contains detailed progress and quality metrics
 */
export type TProgressData = {
  /** Overall completion percentage */
  completion: number;
  
  /** Tasks completed */
  tasksCompleted: number;
  
  /** Total tasks */
  totalTasks: number;
  
  /** Time spent (in minutes) */
  timeSpent: number;
  
  /** Estimated time remaining (in minutes) */
  estimatedTimeRemaining: number;
  
  /** Quality metrics */
  quality: TQualityMetrics;
};

/**
 * Quality metrics structure
 * Contains code quality and performance metrics
 */
export type TQualityMetrics = {
  /** Code coverage percentage */
  codeCoverage: number;
  
  /** Number of tests */
  testCount: number;
  
  /** Number of bugs found */
  bugCount: number;
  
  /** Code quality score (0-100) */
  qualityScore: number;
  
  /** Performance score (0-100) */
  performanceScore: number;
};

/**
 * Authority data structure
 * Contains authority validation information
 */
export type TAuthorityData = {
  /** Authority level */
  level: TAuthorityLevel;
  
  /** Authority validator */
  validator: string;
  
  /** Validation status */
  validationStatus: TValidationStatus;
  
  /** Validation timestamp */
  validatedAt?: string;
  
  /** Compliance score */
  complianceScore: number;
};

// ============================================================================
// METRICS AND PERFORMANCE TYPES
// ============================================================================

/**
 * Comprehensive metrics structure
 * Contains all tracking and performance metrics
 */
export type TMetrics = {
  /** Metrics timestamp */
  timestamp: string;
  
  /** Service name */
  service: string;
  
  /** Performance metrics */
  performance: TPerformanceMetrics;
  
  /** Usage metrics */
  usage: TUsageMetrics;
  
  /** Error metrics */
  errors: TErrorMetrics;
  
  /** Custom metrics */
  custom: Record<string, number>;
};

/**
 * Performance metrics structure
 * Contains performance-related measurements
 */
export type TPerformanceMetrics = {
  queryExecutionTimes: number[];
  cacheOperationTimes: number[];
  memoryUtilization: number[];
  throughputMetrics: number[];
  errorRates: number[];
};

/**
 * Usage metrics structure
 * Contains service usage statistics
 */
export type TUsageMetrics = {
  /** Total operations count */
  totalOperations: number;
  
  /** Successful operations count */
  successfulOperations: number;
  
  /** Failed operations count */
  failedOperations: number;
  
  /** Active users/sessions */
  activeUsers: number;
  
  /** Peak concurrent users */
  peakConcurrentUsers: number;
};

/**
 * Error metrics structure
 * Contains error tracking and statistics
 */
export type TErrorMetrics = {
  /** Total error count */
  totalErrors: number;
  
  /** Error rate percentage */
  errorRate: number;
  
  /** Errors by type */
  errorsByType: Record<string, number>;
  
  /** Recent errors */
  recentErrors: TErrorInfo[];
};

/**
 * Error information structure
 * Contains detailed error information
 */
export type TErrorInfo = {
  /** Error timestamp */
  timestamp: string;
  
  /** Error type */
  type: string;
  
  /** Error message */
  message: string;
  
  /** Error stack trace */
  stack?: string;
  
  /** Error context */
  context: Record<string, unknown>;
};

// ============================================================================
// VALIDATION TYPES
// ============================================================================

/**
 * Validation result structure
 * Contains comprehensive validation results
 */
export type TValidationResult = {
  validationId: string;
  componentId: string;
  timestamp: Date;
  executionTime: number;
  status: 'valid' | 'invalid';
  overallScore: number;
  checks: any[];
  references: TReferenceMap;
  recommendations: string[];
  warnings: string[];
  errors: string[];
  metadata: {
    validationMethod: string;
    rulesApplied: number;
    dependencyDepth: number;
    cyclicDependencies: string[];
    orphanReferences: string[];
  };
};

/**
 * Reference map structure
 * Contains cross-reference information
 */
export type TReferenceMap = {
  componentId: string;
  internalReferences: any[];
  externalReferences: any[];
  circularReferences: any[];
  missingReferences: any[];
  redundantReferences: any[];
  metadata: {
    totalReferences: number;
    buildTimestamp: Date;
    analysisDepth: number;
  };
};

/**
 * Validation error structure
 * Contains validation error details
 */
export type TValidationError = {
  code: string;
  message: string;
  severity: 'error' | 'critical';
  field?: string;
  timestamp: Date;
  component: string;
};

/**
 * Validation warning structure
 * Contains validation warning details
 */
export type TValidationWarning = {
  code: string;
  message: string;
  severity: 'warning' | 'info';
  field?: string;
  timestamp: Date;
  component: string;
};

// ============================================================================
// GOVERNANCE TYPES
// ============================================================================

/**
 * Governance validation structure
 * Contains governance compliance validation results
 */
export type TGovernanceValidation = {
  validationId: string;
  timestamp: Date;
  status: 'valid' | 'invalid' | 'warning';
  score: number;
  checks: TComplianceCheck[];
  violations: TGovernanceViolation[];
  recommendations: string[];
  metadata: Record<string, any>;
};

/**
 * Audit result structure
 * Contains comprehensive audit results
 */
export type TAuditResult = {
  auditId: string;
  timestamp: Date;
  auditType: 'compliance' | 'security' | 'performance' | 'governance';
  status: 'passed' | 'failed' | 'warning';
  score: number;
  findings: TAuditFinding[];
  recommendations: string[];
  remediation: string[];
  nextAuditDate: Date;
};

/**
 * Governance status structure
 * Contains current governance compliance status
 */
export type TGovernanceStatus = {
  status: 'compliant' | 'non-compliant' | 'warning' | 'unknown';
  lastCheck: Date;
  complianceScore: number;
  violations: TGovernanceViolation[];
  activeIssues: number;
  resolvedIssues: number;
  nextReview: Date;
};

/**
 * Governance violation structure
 * Contains governance violation details
 */
export type TGovernanceViolation = {
  violationId: string;
  type: 'authority' | 'process' | 'quality' | 'security' | 'documentation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  component: string;
  timestamp: Date;
  status: 'open' | 'resolved' | 'acknowledged';
  remediation?: string;
};

/**
 * Compliance check structure
 * Contains individual compliance check results
 */
export type TComplianceCheck = {
  checkId: string;
  name: string;
  type: 'authority' | 'process' | 'quality' | 'security' | 'documentation';
  status: 'passed' | 'failed' | 'warning' | 'skipped';
  score: number;
  details: string;
  timestamp: Date;
};

/**
 * Audit finding structure
 * Contains individual audit finding details
 */
export type TAuditFinding = {
  findingId: string;
  type: 'violation' | 'risk' | 'improvement' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  evidence: string[];
  impact: string;
  recommendation: string;
  status: 'open' | 'resolved' | 'acknowledged';
};

// ============================================================================
// HISTORY TYPES
// ============================================================================

/**
 * Tracking history structure
 * Contains historical tracking changes
 */
export type TTrackingHistory = {
  /** History entry timestamp */
  timestamp: string;
  
  /** Previous status */
  previousStatus: TComponentStatus;
  
  /** New status */
  newStatus: TComponentStatus;
  
  /** Change reason */
  reason: string;
  
  /** User who made the change */
  changedBy: string;
  
  /** Additional change details */
  details: Record<string, unknown>;
};

/**
 * Implementation compliance structure
 * Contains implementation compliance assessment
 */
export type TImplementationCompliance = {
  /** Whether implementation is compliant */
  isCompliant: boolean;
  
  /** Compliance checks performed */
  checks: TComplianceCheck[];
  
  /** Overall compliance score */
  score: number;
  
  /** Compliance details */
  details: Record<string, unknown>;
}; 