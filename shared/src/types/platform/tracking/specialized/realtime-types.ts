/**
 * @file Specialized Realtime Types
 * @filepath shared/src/types/platform/tracking/specialized/realtime-types.ts
 * @reference foundation-context.TYPES.009
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type realtime-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/realtime-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Specialized Realtime Types
 * 
 * Realtime and compliance tracking types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

import type { 
  TAuthorityLevel 
} from '../core/base-types';

import type { 
  TGovernanceViolation,
  TAuditResult
} from '../core/tracking-data-types';

// Import from core service types
import type { ITrackingService } from '../core/tracking-service-types';

// ============================================================================
// COMPLIANCE TRACKING INTERFACES
// ============================================================================

/**
 * Compliance trackable interface
 * Provides comprehensive compliance tracking capabilities
 */
export interface IComplianceTrackable extends ITrackingService {
  /**
   * Check compliance status
   * @param scope - Compliance scope to check
   * @returns Compliance check result
   */
  checkCompliance(scope?: 'all' | 'authority' | 'process' | 'quality' | 'security' | 'documentation'): Promise<any>;

  /**
   * Generate compliance report
   * @param format - Report format
   * @param options - Report generation options
   * @returns Compliance report
   */
  generateComplianceReport(
    format?: 'summary' | 'detailed' | 'executive',
    options?: {
      includeRecommendations?: boolean;
      includeHistory?: boolean;
      timeRange?: { start: Date; end: Date };
    }
  ): Promise<any>;

  /**
   * Get compliance metrics
   * @returns Current compliance metrics
   */
  getComplianceMetrics(): Promise<any>;

  /**
   * Track compliance changes
   * @param change - Compliance change details
   */
  trackComplianceChange(change: {
    type: string;
    description: string;
    impact: 'low' | 'medium' | 'high' | 'critical';
    component: string;
  }): Promise<void>;

  /**
   * Get compliance trend data
   * @param timeRange - Time range for trend analysis
   * @returns Compliance trend data
   */
  getComplianceTrend(timeRange?: { start: Date; end: Date }): Promise<any>;

  /**
   * Set compliance thresholds
   * @param thresholds - Compliance threshold configuration
   */
  setComplianceThresholds(thresholds: {
    minimumScore: number;
    warningThreshold: number;
    criticalThreshold: number;
  }): Promise<void>;
} 