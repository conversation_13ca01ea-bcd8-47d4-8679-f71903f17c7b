/**
 * @file Specialized Validation Types
 * @filepath shared/src/types/platform/tracking/specialized/validation-types.ts
 * @reference foundation-context.TYPES.006
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type validation-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/validation-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Specialized Validation Types
 * 
 * Cross-reference validation and path resolution types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

import type { 
  TPathResolutionConfig, 
  TCrossReferenceConfig 
} from '../core/tracking-config-types';

import type { 
  TValidationResult, 
  TReferenceMap 
} from '../core/tracking-data-types';

// ============================================================================
// PATH RESOLUTION INTERFACES
// ============================================================================

/**
 * Path resolution interface
 * Provides intelligent path resolution capabilities
 */
export interface IPathResolution {
  resolvePath(context: string, componentType: string, requirements: any): Promise<TSmartPath>;
}

/**
 * Intelligent service interface
 * Provides path analytics and optimization
 */
export interface IIntelligentService {
  getPathAnalytics(): TPathAnalytics;
}

// ============================================================================
// CROSS-REFERENCE VALIDATION INTERFACES
// ============================================================================

/**
 * Cross-reference validation interface
 * Provides comprehensive cross-reference validation
 */
export interface ICrossReferenceValidation {
  validateCrossReferences(componentId: string, references: any[]): Promise<TValidationResult>;
}

/**
 * Validation service interface
 * Provides validation history and dependency analysis
 */
export interface IValidationService {
  getValidationHistory(): TValidationResult[];
  getDependencyGraph(): TDependencyGraph;
}

// ============================================================================
// PATH RESOLUTION DATA TYPES
// ============================================================================

/**
 * Path resolution data structure
 * Contains path resolution request and result data
 */
export type TPathResolutionData = {
  pathId: string;
  context: string;
  componentType: string;
  resolvedPath: string;
  [key: string]: any;
};

/**
 * Smart path structure
 * Contains intelligent path resolution with optimization
 */
export type TSmartPath = {
  pathId: string;
  context: string;
  componentType: string;
  resolvedPath: string;
  optimizationLevel: string;
  contextScore: number;
  performanceScore: number;
  recommendations: string[];
  metadata: {
    resolutionMethod: string;
    confidenceLevel: number;
    alternativePaths: number;
    optimizationsApplied: string[];
    timestamp: Date;
  };
  validation: {
    isValid: boolean;
    validationScore: number;
    issues: string[];
    warnings: string[];
  };
};

/**
 * Path optimization structure
 * Defines path optimization rules and conditions
 */
export type TPathOptimization = {
  name: string;
  conditions: string[];
  actions: string[];
  priority: string;
};

/**
 * Path analytics structure
 * Contains path resolution performance analytics
 */
export type TPathAnalytics = {
  totalResolutions: number;
  successfulResolutions: number;
  optimizationCount: number;
  averageResolutionTime: number;
  cacheHitRatio: number;
  pathEfficiencyScore: number;
  contextAccuracyScore: number;
  performanceImprovements: string[];
};

/**
 * Component placement structure
 * Contains component placement optimization data
 */
export type TComponentPlacement = {
  componentId: string;
  path: string;
  context: string;
  placementScore: number;
  [key: string]: any;
};

/**
 * Path validation structure
 * Contains path validation results
 */
export type TPathValidation = {
  pathId: string;
  isValid: boolean;
  validationScore: number;
  issues: string[];
  timestamp: Date;
};

// ============================================================================
// CROSS-REFERENCE VALIDATION DATA TYPES
// ============================================================================

/**
 * Cross-reference data structure
 * Contains cross-reference validation data
 */
export type TCrossReferenceData = {
  componentId: string;
  references: any[];
  validationResult: TValidationResult;
  [key: string]: any;
};

/**
 * Integrity check structure
 * Contains component integrity validation results
 */
export type TIntegrityCheck = {
  componentId: string;
  timestamp: Date;
  status: 'healthy' | 'degraded' | 'critical';
  issues: string[];
  score: number;
};

/**
 * Validation rule structure
 * Defines validation rules and criteria
 */
export type TValidationRule = {
  name: string;
  type: string;
  severity: string;
  threshold: number;
  description: string;
};

/**
 * Dependency graph structure
 * Contains comprehensive dependency analysis
 */
export type TDependencyGraph = {
  nodes: Map<string, any>;
  edges: Map<string, any[]>;
  cycles: string[][];
  orphans: string[];
  roots: string[];
  leaves: string[];
}; 