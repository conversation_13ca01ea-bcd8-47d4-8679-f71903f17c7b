/**
 * @file Specialized Analytics Types
 * @filepath shared/src/types/platform/tracking/specialized/analytics-types.ts
 * @reference foundation-context.TYPES.005
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type analytics-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/analytics-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Specialized Analytics Types
 * 
 * Analytics and caching types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

import type { TAnalyticsCacheConfig } from '../core/tracking-config-types';

// ============================================================================
// ANALYTICS INTERFACES
// ============================================================================

/**
 * Analytics service interface
 * Provides analytics query and caching capabilities
 */
export interface IAnalytics {
  executeQuery(query: TAnalyticsQuery): Promise<TAnalyticsResult>;
  getCachedResult(queryKey: string): Promise<TAnalyticsResult | null>;
  cacheResult(queryKey: string, result: TAnalyticsResult, query: TAnalyticsQuery): Promise<void>;
}

/**
 * Cacheable service interface
 * Provides cache management capabilities
 */
export interface ICacheableService {
  clearCache(): Promise<void>;
  getCacheMetrics(): TCacheMetrics;
}

// ============================================================================
// ANALYTICS DATA TYPES
// ============================================================================

/**
 * Analytics data structure
 * Contains cached analytics data with metadata
 */
export type TAnalyticsData = {
  queryKey: string;
  query: TAnalyticsQuery;
  result: TAnalyticsResult;
  timestamp: number;
  lastAccessed: Date;
  accessCount: number;
  size: number;
  compressed: boolean;
};

/**
 * Analytics query structure
 * Defines analytics query parameters and options
 */
export type TAnalyticsQuery = {
  type: 'performance' | 'tracking' | 'governance' | 'orchestration' | string;
  parameters: Record<string, any>;
  filters?: Record<string, any>;
  timeRange?: {
    start: Date;
    end: Date;
  };
  // Additional query properties
  cacheable?: boolean;
  ttl?: number;
};

/**
 * Analytics result structure
 * Contains query results with comprehensive metadata
 */
export type TAnalyticsResult = {
  queryId: string;
  query: TAnalyticsQuery;
  data?: any;
  results?: any[]; // Alternative data property
  metadata: {
    executionTime: number;
    dataPoints: number;
    accuracy: number;
    timestamp: Date;
    source: string;
    recordCount?: number; // Additional metadata property
    cached?: boolean;
  };
  performance: {
    cacheHit: boolean;
    processingTime: number;
    memoryUsed: number;
    optimizationApplied: boolean;
  };
};

// ============================================================================
// CACHE METRICS AND PERFORMANCE TYPES
// ============================================================================

/**
 * Cache metrics structure
 * Contains comprehensive cache performance metrics
 */
export type TCacheMetrics = {
  hits: number;
  misses: number;
  hitRatio: number;
  totalQueries: number;
  cacheSize: number;
  memoryUsage: number;
  lastCleanup: Date;
  avgResponseTime: number;
};

/**
 * Cache strategy structure
 * Defines cache behavior and optimization strategies
 */
export type TCacheStrategy = {
  evictionPolicy: string;
  warmupStrategy: string;
  compressionEnabled: boolean;
  distributedCaching: boolean;
  intelligentPrefetch: boolean;
};

// ============================================================================
// ANALYTICS CACHE TYPES
// ============================================================================

/**
 * Analytics cache entry structure
 * Contains detailed cache entry information
 */
export type TAnalyticsCacheEntry = {
  key: string;
  data: TAnalyticsData;
  metadata: {
    createdAt: Date;
    lastAccessed: Date;
    accessCount: number;
    ttl: number;
    strategy: string;
    tier: string;
    compressed: boolean;
    size: number;
  };
  performance: {
    cacheTime: number;
    retrievalTime: number;
    compressionRatio: number;
    hitCount: number;
    missCount: number;
  };
};

/**
 * Analytics cache metrics structure
 * Contains comprehensive cache performance metrics
 */
export type TAnalyticsCacheMetrics = {
  totalEntries: number;
  cacheSize: number;
  hitRate: number;
  missRate: number;
  evictionRate: number;
  compressionRatio: number;
  memoryUtilization: number;
  memoryUsage: number; // Added to fix compilation error - tracks actual memory usage in bytes
  performanceScore: number;
  healthStatus: string;
  lastOptimization: Date;
  // Additional metrics for comprehensive tracking
  totalHits: number;
  totalMisses: number;
  totalEvictions: number;
  totalWrites: number;
  totalReads: number;
  averageRetrievalTime: number;
  averageCacheTime: number;
  compressionSavings: number;
  realTime?: {
    hitRate: number;
    missRate: number;
    evictionRate: number;
    compressionRatio: number;
    memoryUtilization: number;
    performanceScore: number;
  };
};

/**
 * Analytics cache performance structure
 * Contains detailed performance metrics
 */
export type TAnalyticsCachePerformance = {
  averageCacheTime: number;
  averageRetrievalTime: number;
  throughput: number;
  latency: number;
  reliability: number;
  efficiency: number;
  optimizationScore: number;
  benchmarkComparison: Record<string, number>;
  // Additional performance metrics
  averageLatency: number;
  errorRate: number;
  availabilityScore: number;
  efficiencyScore: number;
  scalabilityScore: number;
};

/**
 * Analytics cache strategy structure
 * Defines cache strategy parameters and performance
 */
export type TAnalyticsCacheStrategy = {
  name: string;
  type: 'LRU' | 'LFU' | 'FIFO' | 'TTL' | 'INTELLIGENT';
  priority: number;
  parameters: Record<string, any>;
  performance: {
    hitRate: number;
    efficiency: number;
    reliability: number;
  };
  // Additional strategy properties
  evictionPolicy: string;
  maxAge: number;
  maxSize: number;
  compression: boolean;
};

/**
 * Analytics cache health structure
 * Contains cache health status and diagnostics
 */
export type TAnalyticsCacheHealth = {
  status: 'healthy' | 'degraded' | 'critical' | 'offline' | 'unhealthy';
  score: number;
  issues: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    timestamp: Date;
  }>;
  recommendations: string[];
  lastCheck: Date;
  lastChecked?: Date; // Alternative property name for compatibility
}; 