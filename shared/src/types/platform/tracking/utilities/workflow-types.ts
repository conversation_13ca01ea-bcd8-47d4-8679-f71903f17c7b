/**
 * @file Utility Workflow Types
 * @filepath shared/src/types/platform/tracking/utilities/workflow-types.ts
 * @reference foundation-context.TYPES.012
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type workflow-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/workflow-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Utility Workflow Types
 * 
 * Workflow and process management types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

// ============================================================================
// WORKFLOW UTILITY TYPES
// ============================================================================

/**
 * Workflow execution utility type
 * Used for workflow processing and coordination across tracking components
 */
export type TWorkflowExecutor = {
  workflowId: string;
  executionMode: 'sequential' | 'parallel' | 'conditional';
  steps: Array<{
    stepId: string;
    type: string;
    parameters: Record<string, any>;
    dependencies?: string[];
  }>;
  errorHandling: {
    strategy: 'abort' | 'continue' | 'retry';
    maxRetries?: number;
  };
  monitoring: {
    trackProgress: boolean;
    notifyOnComplete: boolean;
    logLevel: 'none' | 'basic' | 'detailed';
  };
};

/**
 * Workflow coordination utility type
 * Used for coordinating workflows across multiple tracking services
 */
export type TWorkflowCoordinator = {
  coordinatorId: string;
  managedWorkflows: string[];
  coordinationStrategy: 'centralized' | 'distributed';
  conflictResolution: 'priority' | 'timestamp' | 'manual';
  healthCheck: {
    enabled: boolean;
    interval: number;
    timeoutThreshold: number;
  };
}; 