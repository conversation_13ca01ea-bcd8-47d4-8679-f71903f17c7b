/**
 * @file Utility Metrics Types
 * @filepath shared/src/types/platform/tracking/utilities/metrics-types.ts
 * @reference foundation-context.TYPES.010
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/core/base-types
 * @enables server/src/platform/tracking/core-data
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type metrics-types
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/types/metrics-types.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

/**
 * OA Framework Utility Metrics Types
 * 
 * Metrics and monitoring types for the tracking infrastructure
 * 
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 * @created 2025-06-23 17:02:07 +03
 */

// ============================================================================
// METRICS UTILITY TYPES
// ============================================================================

/**
 * Metrics aggregation utility type
 * Used for combining and processing metrics from multiple sources
 */
export type TMetricsAggregator = {
  source: string;
  timestamp: Date;
  aggregationType: 'sum' | 'average' | 'max' | 'min' | 'count';
  value: number;
  unit: string;
  metadata?: Record<string, any>;
};

/**
 * Metrics collection utility type
 * Used for collecting metrics from various tracking components
 */
export type TMetricsCollector = {
  componentId: string;
  metricsType: string;
  collectionInterval: number;
  enabled: boolean;
  filters?: string[];
  transformations?: Array<{
    type: string;
    parameters: Record<string, any>;
  }>;
}; 