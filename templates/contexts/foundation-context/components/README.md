# Component Header Templates - Foundation Context

**Document Type**: Template Documentation  
**Version**: 2.1.0 - GOVERNANCE PROCESS ALIGNMENT  
**Updated**: 2025-09-04  
**Purpose**: Standardized component header templates for OA Framework  
**Authority**: President & CEO, E.Z. Consultancy  

## 📁 **Template Files**

### **Primary Template**
- `component-header-standard.template` - Base template with placeholders
- `component-header-example.template` - Complete example with sample values

## 🎯 **Template Variables**

### **Core Identification**
- `{{COMPONENT_NAME}}` - Human-readable component name (e.g., "Atomic Circular Buffer")
- `{{FILEPATH}}` - Relative path from project root (e.g., "shared/src/base/AtomicCircularBuffer.ts")
- `{{TASK_ID}}` - Milestone task identifier (e.g., "M-TSK-01.SUB-01.3.IMP-01")
- `{{COMPONENT_KEBAB_CASE}}` - kebab-case identifier (e.g., "atomic-circular-buffer")
- `{{CONTEXT}}` - Primary context (e.g., "foundation-context")
- `{{CATEGORY}}` - Context category (e.g., "Memory-Safety")
- `{{SEQUENCE}}` - Reference sequence (e.g., "006")
- `{{TIER}}` - Governance tier (T0, T1, T2, T3)

### **Timestamps**
- `{{CREATED_TIMESTAMP}}` - ISO timestamp with timezone (e.g., "2025-07-20 12:00:00 +03")
- `{{MODIFIED_TIMESTAMP}}` - ISO timestamp with timezone (e.g., "2025-07-21 15:30:00 +03")

### **Description**
- `{{DESCRIPTION_LINE_1}}` - Primary description line
- `{{DESCRIPTION_BULLETS}}` - Bullet-point feature list

### **Authority & Governance**
- `{{AUTHORITY_LEVEL}}` - Authority level (e.g., "architectural-authority")
- `{{AUTHORITY_VALIDATOR}}` - Authority validator (e.g., "President & CEO, E.Z. Consultancy")
- `{{GOVERNANCE_ADR}}` - Related ADR (e.g., "ADR-foundation-001-tracking-architecture")
- `{{GOVERNANCE_DCR}}` - Related DCR (e.g., "DCR-foundation-001-tracking-development")
- `{{GOVERNANCE_STATUS}}` - Status (e.g., "approved")
- `{{GOVERNANCE_COMPLIANCE}}` - Compliance status (e.g., "authority-validated")

### **Cross-References**
- `{{DEPENDENCIES}}` - Comma-separated dependencies
- `{{ENABLES}}` - Comma-separated enabled components
- `{{RELATED_CONTEXTS}}` - Related contexts
- `{{GOVERNANCE_IMPACT}}` - Governance impact areas

### **Metadata**
- `{{COMPONENT_TYPE}}` - Component type (e.g., "memory-safety-utility")
- `{{LIFECYCLE_STAGE}}` - Current stage (e.g., "implementation")
- `{{TESTING_STATUS}}` - Testing status (e.g., "unit-tested, integration-tested")
- `{{DEPLOYMENT_READY}}` - Deployment readiness (true/false)
- `{{MONITORING_ENABLED}}` - Monitoring status (true/false)
- `{{DOCUMENTATION_PATH}}` - Documentation path

### **Table of Contents**
- `{{TABLE_OF_CONTENTS_CONTENT}}` - Complete TOC structure with line numbers

### **Version History**
- `{{VERSION_HISTORY}}` - Multi-line version history

## 🔧 **Usage Instructions**

### **1. Copy Template**
```bash
cp templates/contexts/foundation-context/components/component-header-standard.template \
   your-component-header.ts
```

### **2. Replace Variables**
Replace all `{{VARIABLE}}` placeholders with actual values.

### **3. Generate Table of Contents**
Create the TOC structure following the pattern:
```
- **Class: ClassName<T>**
  - **Properties**
    - propertyName (lineNumber)
  - **Methods**
    - methodName (lineNumber)
```

### **4. Update Version History**
Add version entries in chronological order:
```
v1.0.0 (YYYY-MM-DD) - Description of changes
```

## 📋 **Compliance Requirements**

### **OA Framework Standards**
- ✅ Authority-driven governance (v2.1)
- ✅ Context-centric organization
- ✅ Cross-reference tracking
- ✅ AI-friendly documentation
- ✅ Progressive documentation standards

### **File Size Management**
- Files >700 lines: Add AI context comments
- Files >1000 lines: Comprehensive TOC required
- Files >1200 lines: Section headers every 150-200 lines

## 🎯 **Template Validation**

Before using, ensure:
- [ ] All variables replaced with actual values
- [ ] Table of contents matches actual code structure
- [ ] Line numbers in TOC are accurate
- [ ] Cross-references are valid
- [ ] Authority validation is complete
- [ ] Documentation path exists

---

**Template Status**: ACTIVE - GOVERNANCE ALIGNED  
**Authority**: President & CEO, E.Z. Consultancy  
**Compliance**: OA Framework v2.1 Standards
