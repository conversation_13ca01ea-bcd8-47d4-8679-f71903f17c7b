---
type: "manual"
---

# AI Assistant Test Quality Governance Rule

**Rule ID**: `GOV-AI-TEST-001`  
**Authority**: Enterprise Quality Assurance  
**Effective Date**: Immediate  
**Compliance Level**: MANDATORY

## Rule Statement

AI Assistants SHALL prioritize production value over test metrics when implementing code changes or test coverage improvements.

## Requirements

### ✅ MANDATORY Actions
1. **Production Justification**: Every code change MUST solve a real user/system problem independent of test requirements
2. **Business Value Validation**: Changes MUST provide measurable functional, performance, or security improvements
3. **Realistic Scenario Testing**: Test cases MUST represent actual production execution paths, not artificial edge cases

### 🚫 PROHIBITED Actions
1. **Coverage-Driven Production Modifications**: Modifying production/implementation files SOLELY to achieve test coverage without genuine functional justification
2. **Artificial Complexity**: Adding unnecessary branching, conditions, or logic solely for coverage
3. **Mock-Specific Code**: Implementing functionality that only serves test environments
4. **Dead Code Paths**: Creating unreachable code branches for coverage statistics
5. **Test-Driven Refactoring**: Modifying working code structure purely for testability without functional benefit

### ✅ PERMITTED Production Modifications
**When tasked with test coverage, production code modifications ARE allowed if:**
- **Bug Fix**: Addresses genuine defects, security vulnerabilities, or performance issues
- **Business Requirement**: Implements missing functionality required by specifications
- **Error Handling**: Improves legitimate error recovery or user experience
- **Performance**: Provides measurable optimization with benchmarked improvements
- **Security**: Enhances system security or compliance posture

**VALIDATION REQUIRED**: Production modifications must include explicit business justification independent of test coverage goals

## Validation Criteria

**Before implementing ANY changes, validate:**
- Would this change exist without test coverage requirements? (YES required)
- Does this solve a problem real users/systems encounter? (YES required)
- Can you explain business value without mentioning tests? (YES required)

**For production code modifications, ADDITIONALLY validate:**
- Is there a genuine functional, performance, or security improvement?
- Can you document the business justification independent of coverage goals?
- Would stakeholders approve this change for reasons other than testing?

## Non-Compliance Consequences
- Immediate code review and revision required
- Documentation of genuine business justification mandatory

**Enforcement**: Automated code review integration + manual quality assurance validation