# Component Header Standard Template

**Document Type**: Governance Template Standard  
**Version**: 2.1.0 - AUTHORITY-DRIVEN GOVERNANCE  
**Updated**: 2025-09-04  
**Purpose**: Official component header template standard for OA Framework  
**Authority**: President & CEO, E<PERSON>Z. Consultancy  
**Classification**: MANDATORY STANDARD  

## 🎯 **Template Locations**

### **Context-Specific Templates**
```
templates/contexts/
├── foundation-context/components/component-header-standard.template
├── authentication-context/components/component-header-standard.template
├── user-experience-context/components/component-header-standard.template
├── production-context/components/component-header-standard.template
└── enterprise-context/components/component-header-standard.template
```

### **Universal Template**
```
templates/universal/component-header-standard.template
```

## 📋 **Mandatory Header Sections**

### **1. Core Identification (Required)**
- `@file` - Human-readable component name
- `@filepath` - Relative path from project root
- `@task-id` - Milestone task identifier
- `@component` - kebab-case component identifier
- `@reference` - Context-based reference (context.CATEGORY.sequence)
- `@template` - Template path used
- `@tier` - Governance tier (T0/T1/T2/T3)
- `@context` - Primary context
- `@category` - Context category
- `@created` - Creation timestamp with timezone
- `@modified` - Last modification timestamp with timezone

### **2. Description (Required)**
- Multi-line description with enterprise capabilities
- Bullet-point feature list
- Integration points and dependencies
- Testing status and compliance notes

### **3. Authority-Driven Governance (Required)**
- `@authority-level` - Authority level
- `@authority-validator` - Authority validator
- `@governance-adr` - Related ADR
- `@governance-dcr` - Related DCR
- `@governance-status` - Governance status
- `@governance-compliance` - Compliance status

### **4. Cross-Context References (Required)**
- `@depends-on` - Dependencies
- `@enables` - Enabled components
- `@related-contexts` - Related contexts
- `@governance-impact` - Governance impact areas

### **5. Enhanced Metadata (Required)**
- `@component-type` - Component classification
- `@lifecycle-stage` - Current stage
- `@testing-status` - Testing completion
- `@deployment-ready` - Deployment readiness
- `@monitoring-enabled` - Monitoring status
- `@documentation` - Documentation path

### **6. Orchestration Metadata (Required)**
- `authority-driven` - Authority integration flag
- `context-validated` - Context validation flag
- `cross-reference-validated` - Cross-reference validation flag

### **7. Version History (Required)**
- Chronological version entries with dates and descriptions

### **8. Table of Contents (Required for files >700 lines)**
- Hierarchical structure with line numbers
- Classes, interfaces, properties, methods
- Imported classes and interfaces
- Clear section organization

## 🔧 **Usage Requirements**

### **Template Selection**
1. **Context-Specific**: Use when component belongs to specific context
2. **Universal**: Use for cross-context or utility components

### **Variable Replacement**
All `{{VARIABLE}}` placeholders must be replaced with actual values.

### **Line Number Accuracy**
Table of Contents line numbers must match actual code structure.

### **Authority Validation**
All authority fields must be completed and validated.

## 📊 **Compliance Validation**

### **Automated Checks**
- [ ] All required sections present
- [ ] All variables replaced
- [ ] Line numbers accurate
- [ ] Cross-references valid
- [ ] Authority validation complete

### **Manual Review**
- [ ] Description accuracy
- [ ] Dependency correctness
- [ ] Documentation path exists
- [ ] Version history complete

## 🎯 **Integration with OA Framework**

### **Development Standards v2.1**
- ✅ Authority-driven governance compliance
- ✅ Context-centric organization
- ✅ AI-friendly documentation
- ✅ Progressive documentation standards

### **Governance Process v2.0**
- ✅ Context-aware naming
- ✅ Authority enforcement
- ✅ Cross-reference tracking
- ✅ Dependency validation

### **File Size Management**
- Files ≤700 lines: Basic header sufficient
- Files 701-1200 lines: Add AI context comments
- Files >1200 lines: Full TOC and section headers required

## 🚀 **Implementation Commands**

### **Generate Header**
```bash
# Copy template
cp templates/contexts/foundation-context/components/component-header-standard.template \
   new-component-header.ts

# Replace variables (manual or automated)
# Update table of contents
# Validate compliance
```

### **Validation**
```bash
# Validate header compliance
ai-validate-header --file=src/components/Component.ts --standard=v2.1

# Check cross-references
ai-validate-cross-refs --file=src/components/Component.ts

# Authority validation
ai-validate-authority --file=src/components/Component.ts
```

---

**Template Status**: ACTIVE - MANDATORY STANDARD  
**Authority**: President & CEO, E.Z. Consultancy  
**Compliance**: OA Framework v2.1 Authority-Driven Standards  
**Enforcement**: REQUIRED for all component files
