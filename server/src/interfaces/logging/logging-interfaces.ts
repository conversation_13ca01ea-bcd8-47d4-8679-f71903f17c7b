/**
 * @file logging-interfaces.ts
 * @filepath server/src/interfaces/logging/logging-interfaces.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.INT-06
 * @component logging-interfaces
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Core
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level core-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-logging-architecture
 * @governance-dcr DCR-foundation-001-logging-management
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.CORE.logging-service
 * @enables all-contexts.LOG.event-logging
 * @related-contexts foundation-context, all-contexts
 * @governance-impact core-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/interfaces/logging-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export interface ILoggingService {
  initialize(): Promise<void>;
  log(level: LogLevel, message: string, context?: LogContext): void;
  error(message: string, context?: LogContext): void;
  warn(message: string, context?: LogContext): void;
  info(message: string, context?: LogContext): void;
  debug(message: string, context?: LogContext): void;
  getLogEntries(filter: LogFilter): Promise<LogEntry[]>;
}

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  TRACE = 'trace'
}

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: LogContext;
  metadata: Record<string, unknown>;
}

export interface LogContext {
  component: string;
  event: string;
  userId?: string;
  error?: Error | unknown;
  metadata?: Record<string, unknown>;
  // Additional properties for security management
  status?: string;
  context?: unknown;
  policy?: unknown;
  violation?: unknown;
  format?: unknown;
  result?: unknown;
}

export interface LogFilter {
  startDate?: Date;
  endDate?: Date;
  level?: LogLevel;
  component?: string;
  limit?: number;
  offset?: number;
} 