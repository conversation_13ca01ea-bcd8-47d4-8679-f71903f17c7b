/**
 * @file monitoring-interfaces.ts
 * @filepath server/src/interfaces/monitoring/monitoring-interfaces.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.INT-05
 * @component monitoring-interfaces
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Core
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level core-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-monitoring-architecture
 * @governance-dcr DCR-foundation-001-monitoring-management
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.CORE.monitoring-service
 * @enables all-contexts.MON.metrics-collection
 * @related-contexts foundation-context, all-contexts
 * @governance-impact core-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/interfaces/monitoring-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export interface IMonitoringService {
  initialize(config: AlertConfig): Promise<void>;
  recordMetric(name: string, value: number, tags?: Record<string, string>): void;
  getMetrics(filter: MetricFilter): Promise<MonitoringMetrics>;
  createAlert(alert: AlertConfig): Promise<void>;
  checkAlerts(): Promise<AlertStatus[]>;
}

export interface MonitoringMetrics {
  timestamp: Date;
  metrics: Record<string, number>;
  aggregations: Record<string, unknown>;
  trends: Record<string, unknown[]>;
}

export interface AlertConfig {
  name?: string;
  threshold: number;
  interval: number;
  condition?: string;
  actions?: AlertAction[];
  metadata?: Record<string, unknown>;
}

export interface AlertAction {
  type: string;
  target: string;
  params: Record<string, unknown>;
}

export enum AlertLevel {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  INFO = 'info'
}

export interface AlertStatus {
  id: string;
  timestamp: Date;
  level: AlertLevel;
  message: string;
  metrics: Record<string, number>;
  metadata: Record<string, unknown>;
}

export interface MetricFilter {
  startDate?: Date;
  endDate?: Date;
  names?: string[];
  tags?: Record<string, string>;
  aggregation?: string;
} 