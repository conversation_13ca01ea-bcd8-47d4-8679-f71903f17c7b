/**
 * @file configuration-interfaces.ts
 * @filepath server/src/interfaces/configuration/configuration-interfaces.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.INT-04
 * @component configuration-interfaces
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Core
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level core-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-configuration-architecture
 * @governance-dcr DCR-foundation-001-configuration-management
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.CORE.configuration-service
 * @enables all-contexts.CONFIG.configuration-management
 * @related-contexts foundation-context, all-contexts
 * @governance-impact core-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/interfaces/configuration-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export interface IConfigurationService {
  initialize(): Promise<void>;
  loadConfiguration(context: ConfigurationContext): Promise<unknown>;
  saveConfiguration(context: ConfigurationContext, config: unknown): Promise<void>;
  validateConfiguration(config: unknown): Promise<boolean>;
  getConfigurationSchema(context: ConfigurationContext): Promise<ConfigurationSchema>;
}

export interface ConfigurationContext {
  component: string;
  environment: string;
  version?: string;
  metadata?: Record<string, unknown>;
}

export interface ConfigurationSchema {
  id: string;
  type: string;
  properties: Record<string, ConfigurationProperty>;
  required: string[];
  metadata: Record<string, unknown>;
}

export interface ConfigurationProperty {
  type: string;
  description?: string;
  default?: unknown;
  enum?: unknown[];
  minimum?: number;
  maximum?: number;
  pattern?: string;
  format?: string;
} 