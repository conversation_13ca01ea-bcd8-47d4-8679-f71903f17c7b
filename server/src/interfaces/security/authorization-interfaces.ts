/**
 * @file authorization-interfaces.ts
 * @filepath server/src/interfaces/security/authorization-interfaces.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.INT-03
 * @component authorization-interfaces
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Security
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level security-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-security-architecture
 * @governance-dcr DCR-foundation-001-security-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.SERV.authorization-service
 * @enables authentication-context.AUTH.permission-validation
 * @related-contexts foundation-context, authentication-context
 * @governance-impact security-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type interface-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/interfaces/authorization-interfaces.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export interface IAuthorizationManager {
  initialize(): Promise<void>;
  authorize(context: AuthorizationContext): Promise<boolean>;
  validatePermissions(permissions: PermissionSet): Promise<boolean>;
  getAccessLevel(context: AuthorizationContext): Promise<AccessLevel>;
}

export interface AuthorizationContext {
  userId: string;
  roles: string[];
  permissions: PermissionSet;
  resource: string;
  action: string;
  metadata: Record<string, unknown>;
}

export enum AccessLevel {
  NONE = 'none',
  READ = 'read',
  WRITE = 'write',
  ADMIN = 'admin',
  SYSTEM = 'system'
}

export interface PermissionSet {
  roles: string[];
  grants: string[];
  restrictions: string[];
  metadata: Record<string, unknown>;
} 