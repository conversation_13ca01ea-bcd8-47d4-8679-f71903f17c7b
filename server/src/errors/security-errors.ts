/**
 * @file security-errors.ts
 * @filepath server/src/errors/security-errors.ts
 * @reference G-TSK-04.SUB-04.1.IMP-01.ERR-01
 * @component security-errors
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Security
 * @created 2025-06-30
 * @modified 2025-06-30
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level security-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-error-handling
 * @governance-dcr DCR-foundation-001-security-error-management
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on foundation-context.SERV.error-handling
 * @enables all-contexts.ERR.security-error-handling
 * @related-contexts foundation-context, all-contexts
 * @governance-impact security-foundation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type error-definitions
 * @lifecycle-stage implementation
 * @testing-status unit-tested
 * @deployment-ready true
 * @documentation docs/contexts/foundation-context/errors/security-errors.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

export class ValidationError extends Error {
  constructor(message: string, public readonly metadata?: Record<string, unknown>) {
    super(message);
    this.name = 'ValidationError';
    Object.setPrototypeOf(this, ValidationError.prototype);
  }
}

export class SecurityError extends Error {
  constructor(message: string, public readonly metadata?: Record<string, unknown>) {
    super(message);
    this.name = 'SecurityError';
    Object.setPrototypeOf(this, SecurityError.prototype);
  }
}

export class ConfigurationError extends Error {
  constructor(message: string, public readonly metadata?: Record<string, unknown>) {
    super(message);
    this.name = 'ConfigurationError';
    Object.setPrototypeOf(this, ConfigurationError.prototype);
  }
}

export class AuditError extends Error {
  constructor(message: string, public readonly metadata?: Record<string, unknown>) {
    super(message);
    this.name = 'AuditError';
    Object.setPrototypeOf(this, AuditError.prototype);
  }
}

export class IntegrityError extends Error {
  constructor(message: string, public readonly metadata?: Record<string, unknown>) {
    super(message);
    this.name = 'IntegrityError';
    Object.setPrototypeOf(this, IntegrityError.prototype);
  }
} 