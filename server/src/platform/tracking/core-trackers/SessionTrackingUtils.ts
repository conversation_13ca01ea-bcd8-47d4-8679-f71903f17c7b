/**
 * @file Session Tracking Utils
 * @filepath server/src/platform/tracking/core-trackers/SessionTrackingUtils.ts
 * @task-id T-REFACTOR-002.UTILS
 * @component session-tracking-utils
 * @reference foundation-context.SERVICE.003.UTILS
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-23
 * @modified 2025-06-23
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @enables server/src/platform/tracking/core-trackers/SessionTrackingService
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type session-tracking-utils
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/services/session-tracking-utils.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { TValidationResult, TComplianceCheck } from '../../../../../shared/src/types/platform/tracking/tracking-types';

/**
 * Session Data Structure for Utils
 */
export interface ISessionData {
  sessionId: string;
  actor: string;
  sessionType: 'user' | 'system' | 'api' | 'background';
  startTime: Date;
  endTime?: Date;
  lastActivity: Date;
  status: 'active' | 'inactive' | 'expired' | 'terminated';
  metadata: Record<string, unknown>;
  events: Array<{
    timestamp: Date;
    level: 'info' | 'warn' | 'error' | 'debug';
    eventType: string;
    message: string;
    context?: Record<string, unknown>;
    error?: { code: string; message: string; stack?: string };
  }>;
  analytics: {
    totalEvents: number;
    errorCount: number;
    warningCount: number;
    duration: number;
    activityScore: number;
  };
}

/**
 * Session Analytics Structure
 */
export interface ISessionAnalytics {
  totalSessions: number;
  activeSessions: number;
  averageSessionDuration: number;
  totalEvents: number;
  errorRate: number;
  lastUpdated: Date;
}

/**
 * Session Tracking Utilities
 * 
 * Comprehensive utility functions, validation logic, and helper methods
 * for session tracking operations.
 */
export class SessionTrackingUtils {
  /** Service version */
  private readonly _version: string = '2.0.0';

  /**
   * Initialize utilities
   */
  constructor() {
    // Initialize utility functions
  }

  /**
   * Calculate activity score for session
   */
  calculateActivityScore(sessionData: ISessionData): number {
    try {
      const factors = [
        // Event frequency (15% weight)
        this._calculateEventFrequencyScore(sessionData) * 0.15,
        
        // Session duration (15% weight)
        this._calculateDurationScore(sessionData) * 0.15,
        
        // Error rate (30% weight)
        this._calculateErrorRateScore(sessionData) * 0.3,
        
        // Recency (40% weight)
        this._calculateRecencyScore(sessionData) * 0.4
      ];

      const totalScore = factors.reduce((sum, score) => sum + score, 0);
      return Math.min(100, Math.max(0, Math.round(totalScore)));
    } catch (error) {
      console.error('Failed to calculate activity score:', error);
      return 0;
    }
  }

  /**
   * Update session analytics
   */
  updateSessionAnalytics(
    analytics: ISessionAnalytics, 
    activeSessions: Map<string, ISessionData>
  ): ISessionAnalytics {
    try {
      const sessions = Array.from(activeSessions.values());
      
      // Calculate total events
      const totalEvents = sessions.reduce((sum, session) => sum + session.analytics.totalEvents, 0);
      
      // Calculate average duration
      const completedSessions = sessions.filter(s => s.endTime);
      const averageDuration = completedSessions.length > 0 
        ? completedSessions.reduce((sum, session) => {
            const duration = session.endTime!.getTime() - session.startTime.getTime();
            return sum + duration;
          }, 0) / completedSessions.length
        : 0;
      
      // Calculate error rate
      const totalErrors = sessions.reduce((sum, session) => sum + session.analytics.errorCount, 0);
      const errorRate = totalEvents > 0 ? (totalErrors / totalEvents) * 100 : 0;
      
      return {
        totalSessions: analytics.totalSessions,
        activeSessions: activeSessions.size,
        averageSessionDuration: averageDuration,
        totalEvents,
        errorRate,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('Failed to update session analytics:', error);
      return analytics;
    }
  }

  /**
   * Validate active sessions
   */
  async validateActiveSessions(
    activeSessions: Map<string, ISessionData>,
    validationResult: TValidationResult
  ): Promise<void> {
    try {
      const sessionCount = activeSessions.size;
      const expiredSessions = this._findExpiredSessions(activeSessions);
      const stalenessSessions = this._findStaleSessions(activeSessions);
      
      // Check session count
      if (sessionCount > 1000) {
        validationResult.warnings.push('High number of active sessions detected');
      }
      
      // Check for expired sessions
      if (expiredSessions.length > 0) {
        validationResult.warnings.push(`${expiredSessions.length} expired sessions found`);
      }
      
      // Check for stale sessions
      if (stalenessSessions.length > 0) {
        validationResult.warnings.push(`${stalenessSessions.length} stale sessions found`);
      }
      
      // Add validation check
      validationResult.checks.push({
        checkId: 'active-sessions-validation',
        name: 'Active Sessions Validation',
        type: 'operational',
        status: expiredSessions.length === 0 && stalenessSessions.length === 0 ? 'passed' : 'warning',
        score: Math.max(0, 100 - (expiredSessions.length + stalenessSessions.length) * 10),
        message: `${sessionCount} active sessions, ${expiredSessions.length} expired, ${stalenessSessions.length} stale`,
        details: {
          totalSessions: sessionCount,
          expiredSessions: expiredSessions.length,
          staleSessions: stalenessSessions.length
        },
        timestamp: new Date(),
        duration: 0
      });
    } catch (error) {
      console.error('Failed to validate active sessions:', error);
      validationResult.errors.push('Active sessions validation failed');
    }
  }

  /**
   * Validate session data integrity
   */
  async validateSessionDataIntegrity(
    sessionHistory: Map<string, ISessionData>,
    validationResult: TValidationResult
  ): Promise<void> {
    try {
      let integrityIssues = 0;
      const totalSessions = sessionHistory.size;
      
             const sessionHistoryEntries = Array.from(sessionHistory.entries());
       for (const [sessionId, sessionData] of sessionHistoryEntries) {
        // Validate session structure
        if (!this._validateSessionStructure(sessionData)) {
          integrityIssues++;
        }
        
        // Validate session timeline
        if (!this._validateSessionTimeline(sessionData)) {
          integrityIssues++;
        }
      }
      
      const integrityScore = totalSessions > 0 ? 
        Math.round(((totalSessions - integrityIssues) / totalSessions) * 100) : 100;
      
      // Add validation check
      validationResult.checks.push({
        checkId: 'session-data-integrity',
        name: 'Session Data Integrity',
        type: 'data-quality',
        status: integrityIssues === 0 ? 'passed' : integrityIssues < totalSessions * 0.1 ? 'warning' : 'failed',
        score: integrityScore,
        message: `${integrityIssues} integrity issues found in ${totalSessions} sessions`,
        details: {
          totalSessions,
          integrityIssues,
          integrityScore
        },
        timestamp: new Date(),
        duration: 0
      });
      
      if (integrityIssues > 0) {
        validationResult.warnings.push(`${integrityIssues} session data integrity issues detected`);
      }
    } catch (error) {
      console.error('Failed to validate session data integrity:', error);
      validationResult.errors.push('Session data integrity validation failed');
    }
  }

  /**
   * Generate unique session ID
   */
  generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Generate unique event ID
   */
  generateEventId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `event_${timestamp}_${random}`;
  }

  /**
   * Sanitize session metadata
   */
  sanitizeMetadata(metadata: Record<string, unknown>): Record<string, unknown> {
    const sanitized: Record<string, unknown> = {};
    
    for (const [key, value] of Object.entries(metadata)) {
      // Remove sensitive information
      if (this._isSensitiveKey(key)) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'string' && value.length > 1000) {
        // Truncate long strings
        sanitized[key] = value.substring(0, 1000) + '...';
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  /**
   * Format session duration
   */
  formatDuration(startTime: Date, endTime?: Date): string {
    try {
      if (!startTime) return '0s';
      
      const end = endTime || new Date();
      const durationMs = end.getTime() - startTime.getTime();
      
      const seconds = Math.floor(durationMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      
      if (days > 0) {
        return `${days}d ${hours % 24}h ${minutes % 60}m`;
      } else if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
      } else {
        return `${seconds}s`;
      }
    } catch (error) {
      return '0s';
    }
  }

  /**
   * Check if session is expired
   */
  isSessionExpired(sessionData: ISessionData, timeoutMs: number = 24 * 60 * 60 * 1000): boolean {
    try {
      if (!sessionData || !sessionData.lastActivity) return true;
      
      const now = new Date();
      const timeSinceActivity = now.getTime() - sessionData.lastActivity.getTime();
      return timeSinceActivity > timeoutMs;
    } catch (error) {
      return true;
    }
  }

  /**
   * Check if session is stale
   */
  isSessionStale(sessionData: ISessionData, staleThresholdMs: number = 4 * 60 * 60 * 1000): boolean {
    try {
      if (!sessionData || !sessionData.lastActivity || !sessionData.status) return false;
      
      const now = new Date();
      const timeSinceActivity = now.getTime() - sessionData.lastActivity.getTime();
      return timeSinceActivity > staleThresholdMs && sessionData.status === 'active';
    } catch (error) {
      return false;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Calculate event frequency score
   */
  private _calculateEventFrequencyScore(sessionData: ISessionData): number {
    const eventCount = sessionData.analytics.totalEvents;
    const sessionDuration = sessionData.endTime 
      ? sessionData.endTime.getTime() - sessionData.startTime.getTime()
      : Date.now() - sessionData.startTime.getTime();
    
    const durationHours = sessionDuration / (1000 * 60 * 60);
    const eventsPerHour = durationHours > 0 ? eventCount / durationHours : 0;
    
    // Score based on events per hour (0-50 range, normalized to 100)
    return Math.min(100, eventsPerHour * 2);
  }

  /**
   * Calculate duration score
   */
  private _calculateDurationScore(sessionData: ISessionData): number {
    const sessionDuration = sessionData.endTime 
      ? sessionData.endTime.getTime() - sessionData.startTime.getTime()
      : Date.now() - sessionData.startTime.getTime();
    
    const durationHours = sessionDuration / (1000 * 60 * 60);
    
    // Optimal session duration is 1-4 hours
    if (durationHours >= 1 && durationHours <= 4) {
      return 100;
    } else if (durationHours < 1) {
      return durationHours * 100; // Linear scale for short sessions
    } else {
      return Math.max(0, 100 - (durationHours - 4) * 10); // Penalty for very long sessions
    }
  }

  /**
   * Calculate error rate score
   */
  private _calculateErrorRateScore(sessionData: ISessionData): number {
    const totalEvents = sessionData.analytics.totalEvents;
    const errorCount = sessionData.analytics.errorCount;
    
    if (totalEvents === 0) return 100;
    
    const errorRate = errorCount / totalEvents;
    return Math.max(0, 100 - (errorRate * 1000)); // Significantly increased penalty for errors
  }

  /**
   * Calculate recency score
   */
  private _calculateRecencyScore(sessionData: ISessionData): number {
    const now = Date.now();
    const timeSinceActivity = now - sessionData.lastActivity.getTime();
    const hoursSinceActivity = timeSinceActivity / (1000 * 60 * 60);
    
    // Recent activity gets higher score
    if (hoursSinceActivity < 1) {
      return 100;
    }
    
    // Exponential decay for older sessions
    const daysSinceActivity = hoursSinceActivity / 24;
    if (daysSinceActivity > 7) {
      return Math.max(0, Math.min(100, 100 - Math.pow(daysSinceActivity, 2))); // Much more aggressive decay for very old sessions
    }
    
    return Math.max(0, Math.min(100, 100 - Math.pow(hoursSinceActivity, 1.5)));
  }

  /**
   * Find expired sessions
   */
  private _findExpiredSessions(activeSessions: Map<string, ISessionData>): ISessionData[] {
    const expired: ISessionData[] = [];
    
    const sessionValues = Array.from(activeSessions.values());
    for (const sessionData of sessionValues) {
      if (this.isSessionExpired(sessionData)) {
        expired.push(sessionData);
      }
    }
    
    return expired;
  }

  /**
   * Find stale sessions
   */
  private _findStaleSessions(activeSessions: Map<string, ISessionData>): ISessionData[] {
    const stale: ISessionData[] = [];
    
    const sessionValues = Array.from(activeSessions.values());
    for (const sessionData of sessionValues) {
      if (this.isSessionStale(sessionData)) {
        stale.push(sessionData);
      }
    }
    
    return stale;
  }

  /**
   * Validate session structure
   */
  private _validateSessionStructure(sessionData: ISessionData): boolean {
    return !!(
      sessionData.sessionId &&
      sessionData.actor &&
      sessionData.sessionType &&
      sessionData.startTime &&
      sessionData.status &&
      sessionData.metadata &&
      sessionData.events &&
      sessionData.analytics
    );
  }

  /**
   * Validate session timeline
   */
  private _validateSessionTimeline(sessionData: ISessionData): boolean {
    // Check that end time is after start time
    if (sessionData.endTime && sessionData.endTime <= sessionData.startTime) {
      return false;
    }
    
    // Check that last activity is reasonable
    if (sessionData.lastActivity < sessionData.startTime) {
      return false;
    }
    
    // Check event timestamps
    for (const event of sessionData.events) {
      if (event.timestamp < sessionData.startTime) {
        return false;
      }
      
      if (sessionData.endTime && event.timestamp > sessionData.endTime) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Check if key contains sensitive information
   */
  private _isSensitiveKey(key: string): boolean {
    const sensitivePatterns = [
      'password', 'token', 'secret', 'key', 'auth',
      'credential', 'private', 'confidential', 'ssn',
      'credit', 'card', 'account', 'personal'
    ];
    
    const lowerKey = key.toLowerCase();
    return sensitivePatterns.some(pattern => lowerKey.includes(pattern));
  }
} 