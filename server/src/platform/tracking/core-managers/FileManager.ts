/**
 * @file File Manager Implementation
 * @filepath server/src/platform/tracking/core-managers/FileManager.ts
 * @task-id T-TSK-03.SUB-03.1.IMP-02
 * @component tracking-file-manager
 * @reference foundation-context.MANAGER.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-24
 * @modified 2025-06-24
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/interfaces/tracking/core-interfaces
 * @depends-on shared/src/types/tracking/tracking-management-types
 * @depends-on shared/src/constants/tracking/tracking-management-constants
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService
 * @enables server/src/platform/tracking/core-managers
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, tracking-dependency
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type manager
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/managers/file-manager.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { promises as fs, watch as fsWatch } from 'fs';
import { join, dirname, extname, resolve, relative } from 'path';
import { BaseTrackingService } from '../core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';
import {
  IFileManager
} from '../../../../../shared/src/interfaces/tracking/core-interfaces';
import {
  IFileService
} from '../../../../../shared/src/interfaces/tracking/tracking-interfaces';
import {
  TTrackingData,
  TValidationResult,
  TManagerConfig,
  TFileOperationResult
} from '../../../../../shared/src/types/tracking/tracking-management-types';
import {
  FILE_MANAGER_CONFIG,
  FILE_OPERATIONS,
  FILE_ERROR_CODES,
  VALIDATION_RULES
} from '../../../../../shared/src/constants/tracking/tracking-management-constants';

import { ResilientTimer } from '../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../shared/src/base/utils/ResilientMetrics';

/**
 * File Manager Implementation
 *
 * Manages file operations for tracking data including reading, writing,
 * deleting, and organizing tracking files with enterprise-grade security
 * and performance optimization.
 *
 * @implements {IFileManager}
 * @implements {IFileService}
 * @extends {BaseTrackingService}
 */
export class FileManager extends BaseTrackingService implements IFileManager, IFileService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  /** File manager configuration */
  private _fileConfig: TManagerConfig;

  /** Active file operations */
  private readonly _activeOperations: Map<string, any> = new Map();

  /** File watchers registry */
  private readonly _watchers: Map<string, any> = new Map();

  /** File operation cache */
  private readonly _operationCache: Map<string, TFileOperationResult> = new Map();

  /** File statistics */
  private _fileStats = {
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    bytesRead: 0,
    bytesWritten: 0,
    filesCreated: 0,
    filesDeleted: 0
  };

  // P1: Resilient timing integration
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  /**
   * Initialize File Manager
   * @param config - File manager configuration
   */
  constructor(config?: Partial<TManagerConfig>) {
    const mergedConfig = {
      ...FILE_MANAGER_CONFIG,
      ...config
    };

    super();
    this._fileConfig = mergedConfig as TManagerConfig;

    this.logInfo('FileManager initialized', {
      managerId: this._fileConfig.id,
      version: this._fileConfig.version
    });
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS
  // ============================================================================

  /**
   * Get service name
   * @returns Service name
   */
  protected getServiceName(): string {
    return 'FileManager';
  }
  /**
   * Initialize resilient timing infrastructure
   */
  private _initializeResilientTimingSync(): void {
    try {
      this._resilientTimer = new ResilientTimer({ enableFallbacks: true, maxExpectedDuration: 8000, unreliableThreshold: 3, estimateBaseline: 5 });
      this._metricsCollector = new ResilientMetricsCollector({ enableFallbacks: true, cacheUnreliableValues: false, maxMetricsAge: 300000, defaultEstimates: new Map([
        ['readFile', 3],
        ['writeFile', 5],
        ['deleteFile', 4],
        ['doValidate', 6]
      ]) });
    } catch (e) {
      this._resilientTimer = new ResilientTimer();
      this._metricsCollector = new ResilientMetricsCollector({ enableFallbacks: true, cacheUnreliableValues: true, maxMetricsAge: 300000, defaultEstimates: new Map() });
    }
  }


  /**
   * Get service version
   * @returns Service version
   */
  protected getServiceVersion(): string {
    return this._fileConfig.version;
  }

  /**
   * Perform service-specific initialization
   */
  protected async doInitialize(): Promise<void> {
    // Initialize resilient timing infrastructure
    this._initializeResilientTimingSync();

    try {
      // Validate file system access
      await this._validateFileSystemAccess();

      // Initialize cache cleanup
      this._setupCacheCleanup();

      this.logInfo('FileManager initialization completed');

    } catch (error) {
      this.logError('doInitialize', error);
      throw error;
    }
  }

  /**
   * Perform service-specific tracking
   * @param data - Tracking data
   */
  protected async doTrack(data: TTrackingData): Promise<void> {
    try {
      // Track file operation if relevant
      if (data.metadata && data.metadata.custom && data.metadata.custom.fileOperation) {
        this._fileStats.totalOperations++;
        this.incrementCounter('file_track');
      }

    } catch (error) {
      this.logError('doTrack', error);
      throw error;
    }
  }

  /**
   * Perform service-specific validation
   * @returns Validation result
   */
  protected async doValidate(): Promise<TValidationResult> {
    const _ctx = this._resilientTimer?.start();
    try {
      const validationId = this.generateId();
      const startTime = Date.now();

      const warnings: string[] = [];
      const errors: string[] = [];

      // Validate file system access
      try {
        await fs.access(process.cwd());
      } catch (error) {
        errors.push('File system access validation failed');
      }

      // Validate cache size
      if (this._operationCache.size > 1000) {
        warnings.push('File operation cache size is large');
      }

      // Validate active operations
      if (this._activeOperations.size > 50) {
        warnings.push('High number of active file operations');
      }

      return {
        validationId,
        componentId: this._fileConfig.id,
        timestamp: new Date(),
        executionTime: Date.now() - startTime,
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: errors.length === 0 ? (warnings.length === 0 ? 100 : 80) : 0,
        checks: [],
        references: {
          componentId: this._fileConfig.id,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings.length > 0 ? ['Review warning conditions'] : [],
        warnings,
        errors,
        metadata: {
          validationMethod: 'FileManager',
          rulesApplied: 3,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

    } catch (error) {
      return {
        validationId: this.generateId(),
        componentId: this._fileConfig.id,
        timestamp: new Date(),
        executionTime: 0,
        status: 'invalid',
        overallScore: 0,
        checks: [],
        references: {
          componentId: this._fileConfig.id,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 0
          }
        },
        recommendations: [],
        warnings: [],
        errors: [`Validation failed: ${error instanceof Error ? error.message : String(error)}`],
        metadata: {
          validationMethod: 'FileManager',
          rulesApplied: 0,
          dependencyDepth: 0,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };
    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('doValidate', _ctx.end());
    }
  }

  /**
   * Perform service-specific shutdown
   */
  protected async doShutdown(): Promise<void> {
    try {
      // Close all file watchers
      const watchers = Array.from(this._watchers.entries());
      for (const [watchId, watcher] of watchers) {
        try {
          if (watcher && typeof watcher.close === 'function') {
            await watcher.close();
          }
          this._watchers.delete(watchId);
        } catch (error) {
          this.logError('_closeWatcher', error, { watchId });
        }
      }

      // Clear caches
      this._operationCache.clear();
      this._activeOperations.clear();

      this.logInfo('FileManager shutdown completed');

    } catch (error) {
      this.logError('doShutdown', error);
      throw error;
    }
  }

  // ============================================================================
  // INTERFACE IMPLEMENTATIONS
  // ============================================================================

  /**
   * Read tracking file
   * @param filePath - Path to the file
   * @returns File operation result
   */
  public async readFile(filePath: string): Promise<TFileOperationResult> {
    const operationId = this.generateId();
    const startTime = Date.now();

    const _ctx = this._resilientTimer?.start();
    try {
      this._activeOperations.set(operationId, {
        id: operationId,
        operation: FILE_OPERATIONS.READ,
        filePath,
        startTime
      });

      // Validate file path
      await this._validateFilePath(filePath);

      // Check cache first
      const cacheKey = `read:${filePath}`;
      const cached = this._operationCache.get(cacheKey);
      if (cached && this._isCacheValid(cached)) {
        return cached;
      }

      // Read file
      const data = await fs.readFile(filePath, 'utf8');
      const stats = await fs.stat(filePath);

      const result: TFileOperationResult = {
        success: true,
        filePath,
        operation: 'read',
        data,
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime,
          fileSize: stats.size,
          permissions: stats.mode.toString(8)
        }
      };

      // Cache result
      this._operationCache.set(cacheKey, result);

      // Update statistics
      this._fileStats.successfulOperations++;
      this._fileStats.bytesRead += stats.size;

      this.logDebug('File read successfully', { filePath, size: stats.size });

      return result;

    } catch (error) {
      const result: TFileOperationResult = {
        success: false,
        filePath,
        operation: 'read',
        error: {
          code: FILE_ERROR_CODES.OPERATION_FAILED,
          message: error instanceof Error ? error.message : String(error),
          details: { operationId }
        },
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime
        }
      };

      this._fileStats.failedOperations++;
      this.logError('readFile', error, { filePath });

      return result;

    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('readFile', _ctx.end());
      this._activeOperations.delete(operationId);
    }
  }

  /**
   * Write tracking file
   * @param filePath - Path to the file
   * @param data - Data to write
   * @returns File operation result
   */
  public async writeFile(filePath: string, data: any): Promise<TFileOperationResult> {
    const operationId = this.generateId();
    const startTime = Date.now();

    const _ctx = this._resilientTimer?.start();
    try {
      this._activeOperations.set(operationId, {
        id: operationId,
        operation: FILE_OPERATIONS.WRITE,
        filePath,
        startTime
      });

      // Validate file path and data
      await this._validateFilePath(filePath);
      await this._validateFileData(data);

      // Ensure directory exists
      await this.ensureDirectory(dirname(filePath));

      // Convert data to string if needed
      const content = typeof data === 'string' ? data : JSON.stringify(data, null, 2);

      // Write file
      await fs.writeFile(filePath, content, 'utf8');
      const stats = await fs.stat(filePath);

      const result: TFileOperationResult = {
        success: true,
        filePath,
        operation: 'write',
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime,
          fileSize: stats.size,
          permissions: stats.mode.toString(8)
        }
      };

      // Update statistics
      this._fileStats.successfulOperations++;
      this._fileStats.bytesWritten += stats.size;
      this._fileStats.filesCreated++;

      // Invalidate read cache
      const cacheKey = `read:${filePath}`;
      this._operationCache.delete(cacheKey);

      this.logDebug('File written successfully', { filePath, size: stats.size });

      return result;

    } catch (error) {
      // 🚨 SECURITY FIX: Re-throw validation errors instead of returning failed results
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Check if this is a security validation error that should be thrown
      if (errorMessage.includes('not allowed') ||
          errorMessage.includes('Path traversal') ||
          errorMessage.includes('Access to paths outside') ||
          errorMessage.includes('File extension')) {
        this._fileStats.failedOperations++;
        this.logError('writeFile', error, { filePath });
        throw error; // Re-throw security validation errors
      }

      // For other errors, return failed result
      const result: TFileOperationResult = {
        success: false,
        filePath,
        operation: 'write',
        error: {
          code: FILE_ERROR_CODES.OPERATION_FAILED,
          message: errorMessage,
          details: { operationId }
        },
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime
        }
      };

      this._fileStats.failedOperations++;
      this.logError('writeFile', error, { filePath });

      return result;

    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('writeFile', _ctx.end());
      this._activeOperations.delete(operationId);
    }
  }

  /**
   * Delete tracking file
   * @param filePath - Path to the file
   * @returns File operation result
   */
  public async deleteFile(filePath: string): Promise<TFileOperationResult> {
    const operationId = this.generateId();
    const startTime = Date.now();

    const _ctx = this._resilientTimer?.start();
    try {
      this._activeOperations.set(operationId, {
        id: operationId,
        operation: FILE_OPERATIONS.DELETE,
        filePath,
        startTime
      });

      // Validate file path
      await this._validateFilePath(filePath);

      // Check if file exists
      await fs.access(filePath);

      // Delete file
      await fs.unlink(filePath);

      const result: TFileOperationResult = {
        success: true,
        filePath,
        operation: 'delete',
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime
        }
      };

      // Update statistics
      this._fileStats.successfulOperations++;
      this._fileStats.filesDeleted++;

      // Remove from cache
      const cacheKey = `read:${filePath}`;
      this._operationCache.delete(cacheKey);

      this.logDebug('File deleted successfully', { filePath });

      return result;

    } catch (error) {
      const result: TFileOperationResult = {
        success: false,
        filePath,
        operation: 'delete',
        error: {
          code: FILE_ERROR_CODES.OPERATION_FAILED,
          message: error instanceof Error ? error.message : String(error),
          details: { operationId }
        },
        metadata: {
          timestamp: new Date().toISOString(),
          duration: Date.now() - startTime
        }
      };

      this._fileStats.failedOperations++;
      this.logError('deleteFile', error, { filePath });

      return result;

    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('deleteFile', _ctx.end());
      this._activeOperations.delete(operationId);
    }
  }

  /**
   * List files in directory
   * @param directoryPath - Path to the directory
   * @returns List of files
   */
  public async listFiles(directoryPath: string): Promise<string[]> {
    try {
      const files = await fs.readdir(directoryPath);
      return files.filter(file => !file.startsWith('.'));

    } catch (error) {
      this.logError('listFiles', error, { directoryPath });
      return [];
    }
  }

  /**
   * Ensure directory exists
   * @param directoryPath - Path to the directory
   */
  public async ensureDirectory(directoryPath: string): Promise<void> {
    try {
      // Validate directory path first
      await this._validateFilePath(directoryPath);

      // Create directory if it doesn't exist
      await fs.mkdir(directoryPath, { recursive: true });

      this.logDebug('Directory created or verified', { directoryPath });

    } catch (error) {
      this.logError('ensureDirectory', error, { directoryPath });
      throw error;
    }
  }

  /**
   * Get file stats
   * @param filePath - Path to the file
   * @returns File statistics
   */
  public async getFileStats(filePath: string): Promise<any> {
    try {
      const stats = await fs.stat(filePath);
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        accessed: stats.atime,
        permissions: stats.mode.toString(8),
        type: stats.isFile() ? 'file' : stats.isDirectory() ? 'directory' : 'other',
        readable: true, // Simplified for now
        writable: true, // Simplified for now
        executable: false // Simplified for now
      };

    } catch (error) {
      this.logError('getFileStats', error, { filePath });
      throw error;
    }
  }

  /**
   * Check if file exists
   * @param filePath - Path to check
   * @returns True if file exists
   */
  public async exists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get file permissions
   * @param filePath - Path to check
   * @returns File permissions
   */
  public async getPermissions(filePath: string): Promise<any> {
    try {
      const stats = await fs.stat(filePath);
      return {
        mode: stats.mode,
        octal: stats.mode.toString(8),
        readable: true,
        writable: true,
        executable: false
      };
    } catch (error) {
      this.logError('getPermissions', error, { filePath });
      throw error;
    }
  }

  /**
   * Set file permissions
   * @param filePath - Path to modify
   * @param permissions - Permissions to set
   */
  public async setPermissions(filePath: string, permissions: any): Promise<void> {
    try {
      const mode = typeof permissions === 'number' ? permissions : parseInt(permissions.mode || '644', 8);
      await fs.chmod(filePath, mode);
    } catch (error) {
      this.logError('setPermissions', error, { filePath, permissions });
      throw error;
    }
  }

  /**
   * Watch file for changes
   * @param filePath - Path to watch
   * @param callback - Callback for changes
   * @returns Watch identifier
   */
  public async watchFile(filePath: string, callback: (event: any) => void): Promise<string> {
    const watchId = this.generateId();

    try {
      const watcher = fsWatch(filePath, (eventType: string, filename: string | null) => {
        callback({
          type: eventType,
          filePath,
          filename: filename || null,
          timestamp: new Date().toISOString()
        });
      });

      this._watchers.set(watchId, watcher);
      return watchId;

    } catch (error) {
      this.logError('watchFile', error, { filePath });
      throw error;
    }
  }

  /**
   * Stop watching file
   * @param watchId - Watch identifier
   */
  public async stopWatching(watchId: string): Promise<void> {
    try {
      const watcher = this._watchers.get(watchId);
      if (watcher && typeof watcher.close === 'function') {
        await watcher.close();
        this._watchers.delete(watchId);
      }
    } catch (error) {
      this.logError('stopWatching', error, { watchId });
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Validate file system access
   */
  private async _validateFileSystemAccess(): Promise<void> {
    try {
      await fs.access(process.cwd());
    } catch (error) {
      throw new Error('File system access validation failed');
    }
  }

  /**
   * Setup cache cleanup
   */
  private _setupCacheCleanup(): void {
    // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        const now = Date.now();
        const ttl = this._fileConfig.cache.ttl;

        const cacheEntries = Array.from(this._operationCache.entries());
        for (const [key, result] of cacheEntries) {
          const age = now - new Date(result.metadata.timestamp).getTime();
          if (age > ttl) {
            this._operationCache.delete(key);
          }
        }
      },
      60000, // Cleanup every minute
      'FileManager',
      'cache-cleanup'
    );
  }

  /**
   * Validate file path
   * @param filePath - Path to validate
   */
  private async _validateFilePath(filePath: string): Promise<void> {
    if (!filePath || typeof filePath !== 'string') {
      throw new Error('Invalid file path');
    }

    // 🚨 SECURITY FIX: Enhanced path traversal detection
    // Check for path traversal attempts before normalization
    if (filePath.includes('..') || filePath.includes('~')) {
      throw new Error('Path traversal not allowed');
    }

    // Normalize the path to handle all path separators consistently
    const normalizedPath = join(process.cwd(), filePath);

    // Double-check after normalization
    if (normalizedPath.includes('..') || normalizedPath.includes('~')) {
      throw new Error('Path traversal not allowed');
    }

    // Ensure the path is within the allowed directory structure
    const baseDir = process.cwd();
    const absolutePath = normalizedPath.startsWith(baseDir)
      ? normalizedPath
      : join(baseDir, normalizedPath);

    // Check if the path is within the allowed directory
    if (!absolutePath.startsWith(baseDir)) {
      throw new Error('Access to paths outside the base directory is not allowed');
    }

    // 🚨 SECURITY FIX: Validate file extension if configured
    const allowedExtensions = this._fileConfig.custom?.allowedExtensions;
    if (allowedExtensions && Array.isArray(allowedExtensions)) {
      const ext = extname(normalizedPath);
      if (ext && !allowedExtensions.includes(ext)) {
        throw new Error(`File extension ${ext} not allowed`);
      }
    }
  }

  /**
   * Validate file data
   * @param data - Data to validate
   */
  private async _validateFileData(data: any): Promise<void> {
    if (data === null || data === undefined) {
      throw new Error('File data cannot be null or undefined');
    }

    // Check file size limit
    const content = typeof data === 'string' ? data : JSON.stringify(data);
    const size = Buffer.byteLength(content, 'utf8');
    const maxSize = this._fileConfig.custom?.maxFileSize || 104857600; // 100MB

    if (size > maxSize) {
      throw new Error(`File size ${size} exceeds maximum allowed size ${maxSize}`);
    }
  }

  /**
   * Check if cache entry is valid
   * @param result - Cached result
   * @returns True if cache is valid
   */
  private _isCacheValid(result: TFileOperationResult): boolean {
    const now = Date.now();
    const age = now - new Date(result.metadata.timestamp).getTime();
    const ttl = this._fileConfig.cache.ttl;

    return age < ttl;
  }
}