/**
 * @file Session Log Tracker Service
 * @filepath server/src/platform/tracking/core-data/SessionLogTracker.ts
 * @reference foundation-context.TRACKER.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-06-21
 * @modified 2025-06-21
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @depends-on shared/src/constants/platform/tracking/tracking-constants
 * @enables M0 session tracking and logging
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, session-tracking
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type session-tracker
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/trackers/session-log-tracker.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { BaseTrackingService } from './base/BaseTrackingService';
import { ResilientTimer } from '../../../../../shared/src/base/utils/ResilientTiming';
import { ResilientMetricsCollector } from '../../../../../shared/src/base/utils/ResilientMetrics';
import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';
import {
  ISessionTracking,
  IAuditableService,
  ITrackingService
} from '../../../../../shared/src/types/platform/tracking/core/tracking-service-types';

import {
  TTrackingData,
  TValidationResult,
  TTrackingContext,
  TTrackingMetadata,
  TAuthorityData,
  TValidationError,
  TValidationWarning,
  TTrackingHistory,
  TAuditResult
} from '../../../../../shared/src/types/platform/tracking/core/tracking-data-types';

import {
  TComponentStatus,
  TRealtimeData,
  TRealtimeCallback,
  TValidationStatus,
  THealthStatus,
  TServiceHealthStatus,
  TRetryConfig,
  TAlertThresholds
} from '../../../../../shared/src/types/platform/tracking/core/base-types';

import {
  VALIDATION_ERROR_CODES,
  VALIDATION_WARNING_CODES,
  ERROR_MESSAGES,
  WARNING_MESSAGES,
  AUTHORITY_VALIDATOR,
  DEFAULT_AUTHORITY_LEVEL,
  DEFAULT_LOG_LEVEL,
  MAX_LOG_FILE_SIZE,
  LOG_ROTATION_INTERVAL,
  getMaxActiveSessions,
  getMaxTrackingHistorySize,
  getMaxSubscriptions,
  getSecurityIntegrationStatus,
  getMemoryUsageThreshold,
  getCpuUsageThreshold,
  getCurrentEnvironmentConstants,
  forceEnvironmentRecalculation,
  getEnvironmentCalculationSummary
} from '../../../../../shared/src/constants/platform/tracking/tracking-constants';

import * as fs from 'fs';
import * as path from 'path';

/**
 * Violation Attempt Interface
 */
interface IViolationAttempt {
  count: number;
  lastAttempt: number;
}

/**
 * Session Log Entry Interface
 * Represents a single log entry in a session
 */
export interface ISessionLogEntry {
  /** Session identifier */
  sessionId: string;

  /** Entry timestamp */
  timestamp: string;

  /** Log level */
  level: 'info' | 'warn' | 'error' | 'debug';

  /** Event type */
  eventType: string;

  /** Event message */
  message: string;

  /** Event context */
  context: Record<string, unknown>;

  /** Error information */
  error?: {
    code: string;
    message: string;
    stack?: string;
    context?: Record<string, unknown>;
  };

  /** Component that generated the event */
  component: string;

  /** Actor associated with the event */
  actor: string;

  /** Performance metrics for this log entry */
  performance?: {
    /** Duration of the event processing in milliseconds */
    duration?: number;
    /** Start time of the event processing */
    startTime?: string;
    /** End time of the event processing */
    endTime?: string;
    /** Additional performance metrics */
    metrics?: Record<string, number>;
  };

  /** Authority validation information */
  authority?: {
    /** Validation status */
    validationStatus?: 'pending' | 'validated' | 'rejected';
    /** Authority level */
    level?: string;
    /** Validator identity */
    validator?: string;
    /** Validation timestamp */
    timestamp?: string;
    /** Validation context */
    context?: Record<string, unknown>;
  };
}

/**
 * Session Log Data Interface
 * Extended tracking data specific to session logging
 */
export interface ISessionLogData extends TTrackingData {
  /** Session-specific data */
  session: {
    /** Current session ID */
    sessionId: string;

    /** Session start time */
    startTime: string;

    /** Session end time (if ended) */
    endTime?: string;

    /** Session duration in milliseconds */
    duration?: number;

    /** Session status */
    status: 'active' | 'ended' | 'expired' | 'terminated';

    /** User or system associated with session */
    actor: string;

    /** Session metadata */
    metadata: {
      /** IP address (if applicable) */
      ipAddress?: string;

      /** User agent (if applicable) */
      userAgent?: string;

      /** Session type */
      type: 'user' | 'system' | 'api' | 'background';

      /** Environment */
      environment: 'development' | 'staging' | 'production';

      /** Additional custom metadata */
      custom?: Record<string, unknown>;
    };

    /** Session events log */
    events: ISessionLogEntry[];

    /** Session performance metrics */
    performance: {
      /** Total events logged */
      totalEvents: number;

      /** Events by level */
      eventsByLevel: Record<string, number>;

      /** Average event processing time */
      avgProcessingTime: number;

      /** Peak memory usage */
      peakMemoryUsage: number;

      /** Session efficiency score */
      efficiencyScore: number;
    };

    /** Session quality metrics */
    quality: {
      /** Error rate percentage */
      errorRate: number;

      /** Warning rate percentage */
      warningRate: number;

      /** Governance compliance score */
      complianceScore: number;

      /** Authority validation rate */
      authorityValidationRate: number;
    };
  };
}

/**
 * Environment type for session metadata
 */
type TEnvironmentType = 'development' | 'staging' | 'production';

/**
 * Interface for session security metrics
 */
interface ISessionSecurityMetrics {
  floodAttempts: number;
  blacklistedActors: number;
  rateLimitViolations: number;
  violationsByType: Map<string, number>;
  totalChecks: number;
  lastCheck: string;
  violations: number;
  lastViolation: {
    actor: string;
    type: string;
    timestamp: string;
  };
  lastEventCheck: string;
  totalEventChecks: number;
}

/**
 * Session Log Tracker Service
 *
 * Tracks and manages session logs across the OA Framework.
 * This is the second M0 task implementation, providing comprehensive session
 * logging capabilities with real-time monitoring and governance compliance.
 *
 * Features:
 * - Real-time session logging
 * - Structured log entry management
 * - Session lifecycle tracking
 * - Performance and quality metrics
 * - Governance compliance logging
 * - Log rotation and archival
 * - Real-time log streaming
 * - Session analytics and reporting
 *
 * @extends BaseTrackingService
 * @implements ISessionTracking
 * @implements IAuditableService

 */
export class SessionLogTracker extends BaseTrackingService implements ISessionTracking, IAuditableService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  // P1: Resilient timing integration
  private _resilientTimer!: ResilientTimer;
  private _metricsCollector!: ResilientMetricsCollector;

  /** Active sessions storage with M0 Security Integration */
  private _activeSessions: Map<string, ISessionLogData> = new Map();
  private readonly _maxActiveSessions = getMaxActiveSessions();

  /** Session history storage with M0 Security Integration */
  private _sessionHistory: Map<string, ISessionLogEntry[]> = new Map();

  /** Real-time subscribers with M0 Security Integration */
  private _realtimeSubscribers: Map<string, TRealtimeCallback[]> = new Map();
  private readonly _maxSubscribers = getMaxSubscriptions();

  /** Log file paths */
  private _logPaths = {
    current: path.join(process.cwd(), 'docs', 'tracking', '.oa-session-log.jsonl'),
    archive: path.join(process.cwd(), 'docs', 'tracking', 'archive'),
    error: path.join(process.cwd(), 'docs', 'tracking', '.oa-session-errors.jsonl')
  };

  /** Log rotation configuration */
  private _rotationConfig = {
    maxFileSize: MAX_LOG_FILE_SIZE * 1024 * 1024, // Convert MB to bytes
    rotationInterval: LOG_ROTATION_INTERVAL * 60 * 60 * 1000, // Convert hours to milliseconds
    maxArchiveFiles: 30,
    compressionEnabled: true
  };

  /** Session analytics */
  private _analytics = {
    totalSessions: 0,
    activeSessions: 0,
    totalEvents: 0,
    errorCount: 0,
    warningCount: 0,
    averageSessionDuration: 0,
    peakConcurrentSessions: 0
  };

  /** Performance monitoring */
  private _performanceMonitor = {
    lastRotation: Date.now(),
    logWriteTime: 0,
    avgLogWriteTime: 0,
    totalWrites: 0,
    failedWrites: 0
  };

  // Session Flood Protection Configuration
  private readonly _maxSessionsPerActor = 5;
  private readonly _maxEventsPerSession = 1000;
  private readonly _maxEventsPerMinute = 100;
  private readonly _sessionTimeoutMs = 30 * 60 * 1000; // 30 minutes
  private readonly _rateLimitWindowMs = 60000; // 1 minute
  private readonly _maxFailedAttempts = 5;
  private readonly _blacklistDurationMs = 3600000; // 1 hour
  private readonly _maxEventSize = 1024 * 1024; // 1MB

  // Session Flood Protection State
  private _actorSessions: Map<string, Set<string>> = new Map();
  private _sessionEventCounts: Map<string, number> = new Map();
  private _sessionEventRates: Map<string, { events: number; resetTime: number }> = new Map();
  private _blacklistedActors: Set<string> = new Set();

  /** Violation attempts tracking */
  private _violationAttempts: Map<string, IViolationAttempt> = new Map();

  // Additional Security Metrics
  private _sessionSecurityMetrics: ISessionSecurityMetrics = {
    floodAttempts: 0,
    blacklistedActors: 0,
    rateLimitViolations: 0,
    violationsByType: new Map(),
    totalChecks: 0,
    lastCheck: '',
    violations: 0,
    lastViolation: {
      actor: '',
      type: '',
      timestamp: ''
    },
    lastEventCheck: '',
    totalEventChecks: 0
  };

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  /**
   * Initialize Session Log Tracker
   */
  constructor() {
    super({
      service: {
        name: 'session-log-tracker',
        version: '1.0.0',
        environment: 'development',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 10000
        }
      },
      logging: {
        level: DEFAULT_LOG_LEVEL,
        format: 'json',
        rotation: true,
        maxFileSize: MAX_LOG_FILE_SIZE
      }
    });

    this._initializeLogDirectories();
  }

  // ============================================================================
  // BASETRACKINGSERVICE IMPLEMENTATION
  // ============================================================================

  /**
   * Get service name
   */
  protected getServiceName(): string {
    return 'session-log-tracker';
  }

  /**
   * Get service version
   */
  protected getServiceVersion(): string {
    return '1.0.0';
  }

  /**
   * Perform service-specific initialization
   */
  protected async doInitialize(): Promise<void> {
    try {
      this.logOperation('doInitialize', 'start');

      // Initialize log directories
      await this._initializeLogDirectories();

      // Load existing session data
      await this._loadSessionData();

      // Initialize real-time monitoring
      await this._initializeRealtimeMonitoring();

      // Start log rotation scheduler (moved from constructor for MEM-SAFE-002 compliance)
      this._startLogRotationScheduler();

      // Start performance monitoring
      await this._startPerformanceMonitoring();
    // Initialize resilient timing infrastructure (synchronous pattern)
    this._initializeResilientTimingSync();


      // Validate log file integrity
      await this._validateLogFileIntegrity();

      this.logOperation('doInitialize', 'complete');
      this.incrementCounter('initializations');

    } catch (error) {
      this.logError('doInitialize', error);
      throw error;
    }
  }

  /**
   * Perform service-specific tracking
   */
  protected async doTrack(data: TTrackingData): Promise<void> {
    const _ctx = this._resilientTimer?.start();
    try {
      this.logOperation('doTrack', 'start', { componentId: data.componentId });

      // Convert to session log data
      const sessionData = await this._convertToSessionData(data);

      // Validate session data
      await this._validateSessionData(sessionData);

      // Process session tracking
      await this._processSessionTracking(sessionData);

      // Update analytics
      await this._updateSessionAnalytics(sessionData);

      // Notify real-time subscribers
      await this._notifyRealtimeSubscribers(sessionData);

      // Persist session data
      await this._persistSessionData(sessionData);

      this.logOperation('doTrack', 'complete', {
        componentId: data.componentId,
        sessionId: sessionData.session.sessionId
      });
      this.incrementCounter('track_operations');

    } catch (error) {
      this.logError('doTrack', error, { componentId: data.componentId });
      throw error;
    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('doTrack', _ctx.end());
    }
  }

  /**
   * Perform service-specific validation
   */
  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: TValidationError[] = [];
      const warnings: TValidationWarning[] = [];

      // Validate active sessions
      await this._validateActiveSessions(errors, warnings);

      // Validate log file integrity
      await this._validateLogFiles(errors, warnings);

      // Validate session analytics
      await this._validateSessionAnalytics(errors, warnings);

      // Validate performance metrics
      await this._validatePerformanceMetrics(errors, warnings);

      const result: TValidationResult = {
        validationId: `session-log-val-${Date.now()}`,
        componentId: 'session-log-tracker',
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(), // Will be updated with actual execution time
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 10) - (warnings.length * 5)),
        checks: [],
        references: {
          componentId: 'session-log-tracker',
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings.map(w => w.message),
        warnings: warnings.map(w => w.message),
        errors: errors.map(e => e.message),
        metadata: {
          validationMethod: 'session-log-validation-check',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', {
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });
      this.incrementCounter('validations');

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      return {
        validationId: `session-log-val-error-${Date.now()}`,
        componentId: 'session-log-tracker',
        timestamp: new Date(),
        executionTime: 0,
        status: 'invalid',
        overallScore: 0,
        checks: [],
        references: {
          componentId: 'session-log-tracker',
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: [],
        warnings: [],
        errors: [ERROR_MESSAGES.CONFIGURATION_ERROR],
        metadata: {
          validationMethod: 'session-log-validation-error',
          rulesApplied: 1,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };
    }
  }

  /**
   * Perform service-specific shutdown
   */
  protected async doShutdown(): Promise<void> {
    try {
      this.logOperation('doShutdown', 'start');

      // Coordinated timer cleanup by serviceId
      try {
        const timerCoordinator = getTimerCoordinator();
        if (typeof (timerCoordinator as any).clearServiceTimers === 'function') {
          (timerCoordinator as any).clearServiceTimers('SessionLogTracker');
        } else if (typeof (timerCoordinator as any).clearAllTimers === 'function') {
          (timerCoordinator as any).clearAllTimers();
        }
      } catch (e) {
        this.logWarning?.('timer_cleanup', 'Timer cleanup error during shutdown', { error: e instanceof Error ? e.message : String(e) });
      }

      // End all active sessions
      await this._endAllActiveSessions();

      // Flush pending log writes
      await this._flushPendingLogs();

      // Save session analytics
      await this._saveSessionAnalytics();

      // Clear real-time subscribers
      this._realtimeSubscribers.clear();

      // Stop performance monitoring (ensures coordinator timers are cleared)
      await this._stopPerformanceMonitoring();

      this.logOperation('doShutdown', 'complete');
      this.incrementCounter('shutdowns');

    } catch (error) {
      this.logError('doShutdown', error);
      throw error;
    }
  }

  // ============================================================================
  // PUBLIC SESSION LOG INTERFACE
  // ============================================================================

  /**
   * 🚨 M0 SECURITY INTEGRATION - Session boundary enforcement
   * Prevents memory exhaustion attacks by maintaining session limits
   */
  private async enforceSessionBoundaries(): Promise<void> {
    // Remove oldest sessions to maintain boundary (LRU cleanup)
    const sessions = Array.from(this._activeSessions.entries());
    const toRemove = Math.floor(this._maxActiveSessions * 0.1); // Remove 10% when limit reached

    // Sort by session start time (oldest first)
    sessions.sort((a, b) =>
      new Date(a[1].session.startTime).getTime() - new Date(b[1].session.startTime).getTime()
    );

    for (let i = 0; i < toRemove && sessions.length > 0; i++) {
      const session = sessions[i];
      if (!session) continue;

      const [sessionId, sessionData] = session;
      try {
        // End the session gracefully with security enforcement reason
        await this.endSession(sessionId, 'Memory boundary enforcement - session limit exceeded');
      } catch (error) {
        // If graceful ending fails, force remove from active sessions
        this._activeSessions.delete(sessionId);
        this.logError('enforceSessionBoundaries', error, { sessionId });
      }
    }

    // Log security boundary enforcement
    this.logInfo('Session boundary enforced', {
      mapType: 'activeSessions',
      maxSize: this._maxActiveSessions,
      currentSize: this._activeSessions.size,
      removedSessions: toRemove,
      securityIntegration: 'M0-emergency-protocol'
    });

    // Warn about eviction
    this.addWarning?.(
      'active_sessions_eviction',
      `Active sessions at capacity (${this._maxActiveSessions}); evicted oldest ${toRemove} sessions`,
      'warning'
    );
  }

  /**
   * 🚨 M0 SECURITY INTEGRATION - Session history boundary enforcement
   * Prevents memory exhaustion attacks by maintaining history size limits
   */
  private async enforceHistoryBoundaries(): Promise<void> {
    // Remove oldest history entries to maintain boundary (LRU cleanup)
    const historyEntries = Array.from(this._sessionHistory.entries());
    const maxHistory = getMaxTrackingHistorySize();
    const toRemove = Math.floor(maxHistory * 0.1); // Remove 10% when limit reached

    // Sort by the timestamp of the first event in each history (oldest first)
    historyEntries.sort((a, b) => {
      const aTime = a[1][0]?.timestamp || '0';
      const bTime = b[1][0]?.timestamp || '0';
      return new Date(aTime).getTime() - new Date(bTime).getTime();
    });

    for (let i = 0; i < toRemove && historyEntries.length > 0; i++) {
      const historyEntry = historyEntries[i];
      if (!historyEntry) continue;

      const [sessionId] = historyEntry;
      this._sessionHistory.delete(sessionId);
    }

    // Log security boundary enforcement
    this.logOperation('enforceHistoryBoundaries', 'complete', {
      mapType: 'sessionHistory',
      maxSize: maxHistory,
      currentSize: this._sessionHistory.size,
      removedEntries: toRemove,
      securityIntegration: 'M0-emergency-protocol'
    });

    // Warn about eviction
    this.addWarning?.(
      'history_eviction',
      `Session history at capacity (${maxHistory}); evicted oldest ${toRemove} entries`,
      'warning'
    );
  }

  /**
   * 🚨 M0 SECURITY INTEGRATION - Subscriber boundary enforcement
   * Prevents memory exhaustion attacks by maintaining subscriber limits
   */
  private async enforceSubscriberBoundaries(): Promise<void> {
    // Remove oldest subscriber entries to maintain boundary
    const subscribers = Array.from(this._realtimeSubscribers.entries());
    const toRemove = Math.floor(this._maxSubscribers * 0.1); // Remove 10% when limit reached

    for (let i = 0; i < toRemove && subscribers.length > 0; i++) {
      const subscriber = subscribers[i];
      if (!subscriber) continue;

      const [sessionId] = subscriber;
      this._realtimeSubscribers.delete(sessionId);
    }

    // Log security boundary enforcement
    this.logInfo('Subscriber boundary enforced', {
      mapType: 'realtimeSubscribers',
      maxSize: this._maxSubscribers,
      currentSize: this._realtimeSubscribers.size,
      removedEntries: toRemove,
      securityIntegration: 'M0-emergency-protocol'
    });

    // Warn about eviction
    this.addWarning?.(
      'subscriber_eviction',
      `Realtime subscribers at capacity (${this._maxSubscribers}); evicted oldest ${toRemove} entries`,
      'warning'
    );
  }

  /**
   * Start a new session
   */
  public async startSession(
    sessionId: string,
    actor: string,
    sessionType: 'user' | 'system' | 'api' | 'background' = 'system',
    metadata?: Record<string, unknown>
  ): Promise<ISessionLogData> {
    const _ctx = this._resilientTimer?.start();
    try {


    // Enforce security measures
    try {
      await this.enforceSessionSecurity(actor, sessionId, 'start');
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : 'Security measures prevented session start');
    }

    // Create session with boundary enforcement
    if (this._activeSessions.size >= this._maxActiveSessions) {
      await this.enforceSessionBoundaries();
    }

    // Initialize session data
    const sessionData: ISessionLogData = {
      componentId: sessionId,
      status: 'active' as TComponentStatus,
      timestamp: new Date().toISOString(),
      metadata: {
        phase: 'active',
        progress: 0,
        priority: 'P2',
        tags: ['session'],
        custom: metadata || {}
      },
      context: {
        contextId: 'session-tracking',
        milestone: 'M0',
        category: 'session',
        dependencies: [],
        dependents: []
      },
      progress: {
        completion: 0,
        tasksCompleted: 0,
        totalTasks: 0,
        timeSpent: 0,
        estimatedTimeRemaining: 0,
        quality: {
          codeCoverage: 0,
          testCount: 0,
          bugCount: 0,
          qualityScore: 0,
          performanceScore: 0
        }
      },
      authority: {
        level: DEFAULT_AUTHORITY_LEVEL,
        validator: actor,
        validationStatus: 'pending',
        complianceScore: 100
      },
      session: {
        sessionId,
        startTime: new Date().toISOString(),
        status: 'active',
        actor,
        metadata: {
          type: sessionType,
          environment: (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'staging') ?
            process.env.NODE_ENV as 'production' | 'staging' : 'development',
          custom: metadata
        },
        events: [],
        performance: {
          totalEvents: 0,
          eventsByLevel: {},
          avgProcessingTime: 0,
          peakMemoryUsage: 0,
          efficiencyScore: 100
        },
        quality: {
          errorRate: 0,
          warningRate: 0,
          complianceScore: 100,
          authorityValidationRate: 100
        }
      }
    };

    // Store session
    this._activeSessions.set(sessionId, sessionData);

    // Update analytics for new session
    this._analytics.totalSessions++;
    this._analytics.activeSessions = this._activeSessions.size;
    this._analytics.peakConcurrentSessions = Math.max(
      this._analytics.peakConcurrentSessions,
      this._activeSessions.size
    );

    // Track actor's sessions
    let actorSessions = this._actorSessions.get(actor);
    if (!actorSessions) {
      actorSessions = new Set();
      this._actorSessions.set(actor, actorSessions);
    }
    actorSessions.add(sessionId);

    // Log SESSION_START event
    const startEvent: ISessionLogEntry = {
      sessionId,
      timestamp: new Date().toISOString(),
      level: 'info',
      eventType: 'SESSION_START',
      message: `Session started by ${actor}`,
      context: { sessionType, metadata },
      component: 'session-log-tracker',
      actor
    };

    // Add the start event to the session
    sessionData.session.events.push(startEvent);
    sessionData.session.performance.totalEvents = 1;


    sessionData.session.performance.eventsByLevel.info = 1;

    // Initialize counters AFTER SESSION_START event is counted
    this._sessionEventCounts.set(sessionId, 1); // Start with 1 to include SESSION_START
    this._sessionEventRates.set(sessionId, { events: 1, resetTime: Date.now() }); // Include SESSION_START in rate limit

    // Write to log file
    await this._writeLogToFile(startEvent);

    this.logInfo('session_started', {
      sessionId,
      actor,
      type: sessionType
    });

    return sessionData;
    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('startSession', _ctx.end());
    }
  }

  /**
   * End a session
   */
  public async endSession(sessionId: string, reason?: string): Promise<void> {
    const _ctx = this._resilientTimer?.start();
    try {
      const session = this._activeSessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      try {
        await this.enforceSessionSecurity(session.session.actor, sessionId, 'end');
      } catch (error) {
        if (reason === 'security_violation' || reason?.includes('Memory boundary enforcement')) {
          // Allow session end for security violations
        } else {
          throw new Error(error instanceof Error ? error.message : 'Security measures prevented session end');
        }
      }

      // Update session data
      session.session.endTime = new Date().toISOString();
      session.session.status = 'ended';
      session.session.duration =
        new Date(session.session.endTime).getTime() -
        new Date(session.session.startTime).getTime();

      // Log SESSION_END event
      const endEvent: ISessionLogEntry = {
        sessionId,
        timestamp: session.session.endTime,
        level: 'info',
        eventType: 'SESSION_END',
        message: `Session ended${reason ? ': ' + reason : ''}`,
        context: {
          duration: session.session.duration,
          reason,
          totalEvents: session.session.performance.totalEvents
        },
        component: 'session-log-tracker',
        actor: session.session.actor
      };

      // Add the end event to the session
      session.session.events.push(endEvent);
      session.session.performance.totalEvents++;
      session.session.performance.eventsByLevel.info =
        (session.session.performance.eventsByLevel.info || 0) + 1;

      // Write to log file
      await this._writeLogToFile(endEvent);

      // Calculate final metrics
      await this._calculateFinalSessionMetrics(session);

      // Update analytics for ended session
      if (session.session.duration) {
        const sessionCount = this._analytics.totalSessions;
        const currentAvg = this._analytics.averageSessionDuration;
        this._analytics.averageSessionDuration =
          ((currentAvg * (sessionCount - 1)) + session.session.duration) / sessionCount;
      }

      // Move session to history before removing from active sessions
      this._sessionHistory.set(sessionId, [...session.session.events]);

      // Remove from active sessions and update counts
      this._activeSessions.delete(sessionId);
      this._analytics.activeSessions = this._activeSessions.size;

      // Remove from actor sessions
      const actorSessions = this._actorSessions.get(session.session.actor);
      if (actorSessions) {
        actorSessions.delete(sessionId);
        if (actorSessions.size === 0) {
          this._actorSessions.delete(session.session.actor);
        }
      }

      // Cleanup session tracking
      this._sessionEventCounts.delete(sessionId);
      this._sessionEventRates.delete(sessionId);

      // Cleanup subscribers
      this._realtimeSubscribers.delete(sessionId);

      this.logInfo('session_ended', {
        sessionId,
        actor: session.session.actor,
        duration: session.session.duration,
        reason
      });
    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('endSession', _ctx.end());
    }
  }

  /**
   * Log an event to a session
   */
  public async logSessionEvent(
    sessionId: string,
    level: 'info' | 'warn' | 'error' | 'debug',
    eventType: string,
    message: string,
    context?: Record<string, unknown>,
    error?: { code: string; message: string; stack?: string; context?: Record<string, unknown> }
  ): Promise<void> {
    const _ctx = this._resilientTimer?.start();
    try {
      // Validate event size first to avoid unnecessary security checks
      const eventSize = JSON.stringify({ level, eventType, message, context, error }).length;
      if (eventSize > this._maxEventSize) {
        throw new Error(`Event size ${eventSize} exceeds maximum allowed size ${this._maxEventSize}`);
      }

      // Get session first to avoid unnecessary security checks
      const session = this._activeSessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Perform security checks - this will throw if any check fails
      // and update event counts and rate limits
      await this.enforceSessionSecurity(
        session.session.actor,
        sessionId,
        'event'
      );

      // Create the log entry with all required fields
      const logEntry: ISessionLogEntry = {
        sessionId,
        timestamp: new Date().toISOString(),
        level,
        eventType,
        message,
        context: context || {},
        ...(error ? { error } : {}),
        component: 'session-log-tracker',
        actor: session.session.actor
      };

      // Add to session events
      session.session.events.push(logEntry);

      // Update session performance metrics
      session.session.performance.totalEvents++;
      session.session.performance.eventsByLevel[level] =
        (session.session.performance.eventsByLevel[level] || 0) + 1;

      // Update global analytics
      this._analytics.totalEvents++;
      if (level === 'error') {
        this._analytics.errorCount++;
      }
      if (level === 'warn') {
        this._analytics.warningCount++;
      }

      // Update session quality metrics
      const totalEvents = session.session.performance.totalEvents;
      const errorEvents = session.session.performance.eventsByLevel.error || 0;
      const warningEvents = session.session.performance.eventsByLevel.warn || 0;

      session.session.quality.errorRate = (errorEvents / totalEvents) * 100;
      session.session.quality.warningRate = (warningEvents / totalEvents) * 100;

      // Write to log file
      await this._writeLogToFile(logEntry);

      // Notify subscribers immediately
      try {
        await this._notifyRealtimeSubscribers(session);
      } catch (error) {
        this.logError('notify_subscribers', error, { sessionId });
      }
    } finally {
      if (_ctx) this._metricsCollector?.recordTiming('logSessionEvent', _ctx.end());
    }
  }

  /**
   * Get session data
   */
  public async getSessionData(sessionId: string): Promise<ISessionLogData | null> {
    try {
      this.logOperation('getSessionData', 'start', { sessionId });

      const sessionData = this._activeSessions.get(sessionId);

      this.logOperation('getSessionData', 'complete', {
        sessionId,
        found: !!sessionData
      });
      this.incrementCounter('session_data_requests');

      return sessionData ? { ...sessionData } : null;

    } catch (error) {
      this.logError('getSessionData', error, { sessionId });
      throw error;
    }
  }

  /**
   * Get session history
   */
  public async getSessionHistory(sessionId: string): Promise<ISessionLogEntry[]> {
    try {
      this.logOperation('getSessionHistory', 'start', { sessionId });

      // Check if session is active first
      const activeSession = this._activeSessions.get(sessionId);
      if (activeSession) {
        const events = activeSession.session.events || [];
        this.logOperation('getSessionHistory', 'complete', {
          sessionId,
          eventsCount: events.length,
          source: 'active'
        });
        this.incrementCounter('session_history_requests');
        return [...events];
      }

      // Check session history for ended sessions
      const history = this._sessionHistory.get(sessionId) || [];

      this.logOperation('getSessionHistory', 'complete', {
        sessionId,
        eventsCount: history.length,
        source: 'history'
      });
      this.incrementCounter('session_history_requests');

      return [...history];

    } catch (error) {
      this.logError('getSessionHistory', error, { sessionId });
      throw error;
    }
  }

  /**
   * Get active sessions
   */
  public async getActiveSessions(): Promise<ISessionLogData[]> {
    try {
      this.logOperation('getActiveSessions', 'start');

      const sessions = Array.from(this._activeSessions.values()).map(session => ({ ...session }));

      this.logOperation('getActiveSessions', 'complete', {
        count: sessions.length
      });
      this.incrementCounter('active_sessions_requests');

      return sessions;

    } catch (error) {
      this.logError('getActiveSessions', error);
      throw error;
    }
  }

  /**
   * Get session analytics
   */
  public async getSessionAnalytics(): Promise<typeof this._analytics> {
    try {
      this.logOperation('getSessionAnalytics', 'start');

      const analytics = { ...this._analytics };

      this.logOperation('getSessionAnalytics', 'complete');
      this.incrementCounter('analytics_requests');

      return analytics;

    } catch (error) {
      this.logError('getSessionAnalytics', error);
      throw error;
    }
  }

  /**
   * Subscribe to real-time session events
   */
  public async subscribeToRealtimeEvents(
    sessionId: string,
    callback: TRealtimeCallback
  ): Promise<string> {
    try {
      this.logOperation('subscribeToRealtimeEvents', 'start', { sessionId });

      // 🚨 M0 SECURITY INTEGRATION - Memory boundary enforcement for subscribers
      if (this._realtimeSubscribers.size >= this._maxSubscribers) {
        await this.enforceSubscriberBoundaries();
      }

      const subscriptionId = `sub-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      if (!this._realtimeSubscribers.has(sessionId)) {
        this._realtimeSubscribers.set(sessionId, []);
      }

      const subscribers = this._realtimeSubscribers.get(sessionId)!;
      if (subscribers.length >= 10) { // Max 10 subscribers per session
        throw new Error('Maximum subscribers per session limit reached');
      }

      subscribers.push(callback);

      this.logOperation('subscribeToRealtimeEvents', 'complete', {
        sessionId,
        subscriptionId
      });
      this.incrementCounter('realtime_subscriptions');

      return subscriptionId;

    } catch (error) {
      this.logError('subscribeToRealtimeEvents', error, { sessionId });
      throw error;
    }
  }

  // ============================================================================
  // IAUDITABLESERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Generate comprehensive audit trail
   * Implements IAuditableService.generateAuditTrail()
   */
  public async generateAuditTrail(options?: {
    startDate?: Date;
    endDate?: Date;
    includeDetails?: boolean;
  }): Promise<any> {
    try {
      this.logOperation('generateAuditTrail', 'start', options);

      const startDate = options?.startDate || new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours
      const endDate = options?.endDate || new Date();
      const includeDetails = options?.includeDetails ?? true;

      const auditTrail = {
        auditId: `session-audit-${Date.now()}`,
        generatedAt: new Date().toISOString(),
        timeRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        sessionData: {
          totalSessions: this._analytics.totalSessions,
          activeSessions: this._analytics.activeSessions,
          completedSessions: this._analytics.totalSessions - this._analytics.activeSessions
        },
        analytics: this._analytics,
        performanceMetrics: this._performanceMonitor,
        logFiles: Object.keys(this._logPaths),
        details: includeDetails ? {
          activeSessions: Array.from(this._activeSessions.values()),
          sessionHistory: Array.from(this._sessionHistory.entries()).slice(-100), // Last 100 entries
          realtimeSubscribers: this._realtimeSubscribers.size
        } : undefined
      };

      this.logOperation('generateAuditTrail', 'complete', {
        auditId: auditTrail.auditId,
        sessionsIncluded: auditTrail.sessionData.totalSessions
      });
      this.incrementCounter('audit_trails_generated');

      return auditTrail;

    } catch (error) {
      this.logError('generateAuditTrail', error, options);
      throw error;
    }
  }

  /**
   * Get audit history
   * Implements IAuditableService.getAuditHistory()
   */
  public async getAuditHistory(limit?: number): Promise<any[]> {
    try {
      this.logOperation('getAuditHistory', 'start', { limit });

      const maxLimit = limit || 50;
      const auditEntries: any[] = [];

      // Get recent session events as audit entries
      for (const [sessionId, events] of Array.from(this._sessionHistory.entries()).slice(-maxLimit)) {
        const sessionAuditEntry = {
          auditEntryId: `session-${sessionId}-audit`,
          timestamp: events[0]?.timestamp || new Date().toISOString(),
          sessionId,
          eventCount: events.length,
          auditType: 'session-tracking',
          summary: `Session ${sessionId} with ${events.length} events`,
          events: events.slice(-10) // Last 10 events per session
        };
        auditEntries.push(sessionAuditEntry);
      }

      // Sort by timestamp, most recent first
      auditEntries.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      this.logOperation('getAuditHistory', 'complete', {
        entriesReturned: auditEntries.length
      });
      this.incrementCounter('audit_history_requests');

      return auditEntries.slice(0, maxLimit);

    } catch (error) {
      this.logError('getAuditHistory', error, { limit });
      throw error;
    }
  }

  /**
   * Export audit data in specified format
   * Implements IAuditableService.exportAuditData()
   */
  public async exportAuditData(
    format: 'json' | 'csv' | 'xml',
    options?: {
      startDate?: Date;
      endDate?: Date;
      includeMetadata?: boolean;
    }
  ): Promise<string> {
    try {
      this.logOperation('exportAuditData', 'start', { format, options });

      const auditTrail = await this.generateAuditTrail({
        ...(options?.startDate ? { startDate: options.startDate } : {}),
        ...(options?.endDate ? { endDate: options.endDate } : {}),
        includeDetails: options?.includeMetadata ?? true
      });

      let exportedData: string;

      switch (format) {
        case 'json':
          exportedData = JSON.stringify(auditTrail, null, 2);
          break;

        case 'csv':
          // Convert to CSV format
          const csvHeaders = ['Timestamp', 'Session ID', 'Event Type', 'Message', 'Actor'];
          const csvRows = [csvHeaders.join(',')];

          if (auditTrail.details?.activeSessions) {
            for (const session of auditTrail.details.activeSessions) {
              for (const event of session.session.events || []) {
                const row = [
                  event.timestamp,
                  event.sessionId,
                  event.eventType,
                  `"${event.message.replace(/"/g, '""')}"`, // Escape quotes
                  event.actor
                ].join(',');
                csvRows.push(row);
              }
            }
          }

          // Add session history data
          if (auditTrail.details?.sessionHistory) {
            for (const [sessionId, events] of auditTrail.details.sessionHistory) {
              for (const event of events) {
                const row = [
                  event.timestamp,
                  sessionId,
                  event.eventType,
                  `"${event.message.replace(/"/g, '""')}"`, // Escape quotes
                  event.actor
                ].join(',');
                csvRows.push(row);
              }
            }
          }

          exportedData = csvRows.join('\n');
          break;

        case 'xml':
          // Convert to XML format
          exportedData = `<?xml version="1.0" encoding="UTF-8"?>
<sessionAuditTrail>
  <auditId>${auditTrail.auditId}</auditId>
  <generatedAt>${auditTrail.generatedAt}</generatedAt>
  <timeRange>
    <start>${auditTrail.timeRange.start}</start>
    <end>${auditTrail.timeRange.end}</end>
  </timeRange>
  <sessionData>
    <totalSessions>${auditTrail.sessionData.totalSessions}</totalSessions>
    <activeSessions>${auditTrail.sessionData.activeSessions}</activeSessions>
    <completedSessions>${auditTrail.sessionData.completedSessions}</completedSessions>
  </sessionData>
</sessionAuditTrail>`;
          break;

        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      this.logOperation('exportAuditData', 'complete', {
        format,
        dataSize: exportedData.length
      });
      this.incrementCounter('audit_data_exports');

      return exportedData;

    } catch (error) {
      this.logError('exportAuditData', error, { format, options });
      throw error;
    }
  }

  /**
   * Perform compliance audit
   * Implements IAuditableService.performComplianceAudit()
   */
  public async performComplianceAudit(auditType?: 'full' | 'security' | 'governance' | 'performance'): Promise<TAuditResult> {
    try {
      this.logOperation('performComplianceAudit', 'start', { auditType });

      const auditId = `session-compliance-audit-${Date.now()}`;
      const timestamp = new Date();
      const requestedType = auditType || 'full';
      // Map 'full' to 'compliance' for TAuditResult type compatibility
      const type: 'compliance' | 'security' | 'governance' | 'performance' =
        requestedType === 'full' ? 'compliance' : requestedType;

      // Perform governance validation first
      const governanceValidation = await this.validateGovernance();

      // Assess session-specific compliance
      const sessionCompliance = {
        activeSessionsWithinLimits: this._analytics.activeSessions <= this._analytics.peakConcurrentSessions,
        logFileIntegrityCheck: true, // Would check file integrity in real implementation
        performanceWithinThresholds: this._performanceMonitor.avgLogWriteTime < 100,
        errorRateAcceptable: this._performanceMonitor.failedWrites / Math.max(this._performanceMonitor.totalWrites, 1) < 0.05
      };

      const findings: any[] = [];
      let overallScore = 100;

      // Type-specific findings
      if (requestedType === 'security' || requestedType === 'full') {
        findings.push({
          findingId: `${auditId}-security-001`,
          type: 'violation',
          severity: 'high',
          description: 'Security boundaries enforced',
          evidence: [`Blacklisted actors: ${this._sessionSecurityMetrics.blacklistedActors}`],
          impact: 'Security compliance maintained',
          recommendation: 'Continue monitoring',
          status: 'resolved'
        });
      }

      if (requestedType === 'governance' || requestedType === 'full') {
        findings.push({
          findingId: `${auditId}-gov-001`,
          type: 'compliance',
          severity: 'low',
          description: 'Governance standards maintained',
          evidence: [`Active sessions: ${this._analytics.activeSessions}`],
          impact: 'Governance compliance maintained',
          recommendation: 'Continue monitoring',
          status: 'resolved'
        });
      }

      if (requestedType === 'performance' || requestedType === 'full') {
        findings.push({
          findingId: `${auditId}-perf-001`,
          type: 'improvement',
          severity: 'medium',
          description: 'Performance within acceptable range',
          evidence: [`Average write time: ${this._performanceMonitor.avgLogWriteTime}ms`],
          impact: 'Performance maintained',
          recommendation: 'Monitor performance trends',
          status: 'open'
        });
      }

      // Check session compliance issues
      if (!sessionCompliance.activeSessionsWithinLimits) {
        findings.push({
          findingId: `${auditId}-finding-001`,
          type: 'compliance',
          severity: 'medium',
          description: 'Active sessions exceed recommended limits',
          evidence: [`Active sessions: ${this._analytics.activeSessions}`, `Peak concurrent: ${this._analytics.peakConcurrentSessions}`],
          impact: 'May affect system performance',
          recommendation: 'Implement session cleanup policies',
          status: 'open'
        });
        overallScore -= 15;
      }

      if (!sessionCompliance.performanceWithinThresholds) {
        findings.push({
          findingId: `${auditId}-finding-002`,
          type: 'performance',
          severity: 'medium',
          description: 'Session logging performance below thresholds',
          evidence: [`Average write time: ${this._performanceMonitor.avgLogWriteTime}ms`],
          impact: 'Degraded logging performance',
          recommendation: 'Optimize log writing operations',
          status: 'open'
        });
        overallScore -= 10;
      }

      if (!sessionCompliance.errorRateAcceptable) {
        findings.push({
          findingId: `${auditId}-finding-003`,
          type: 'reliability',
          severity: 'high',
          description: 'High session logging error rate detected',
          evidence: [`Failed writes: ${this._performanceMonitor.failedWrites}`, `Total writes: ${this._performanceMonitor.totalWrites}`],
          impact: 'Potential data loss in session logs',
          recommendation: 'Investigate and fix logging errors',
          status: 'open'
        });
        overallScore -= 25;
      }

      // Include governance findings
      findings.push(...governanceValidation.violations.map(violation => ({
        findingId: `${auditId}-gov-${violation.violationId}`,
        type: 'governance',
        severity: violation.severity,
        description: violation.description,
        evidence: [`Violation ID: ${violation.violationId}`],
        impact: 'Governance compliance affected',
        recommendation: violation.remediation || 'Address governance violation',
        status: violation.status
      })));

      const auditResult: TAuditResult = {
        auditId,
        timestamp,
        auditType: type, // Use the mapped type that fits TAuditResult constraints
        status: overallScore >= 80 ? 'passed' : overallScore >= 60 ? 'warning' : 'failed',
        score: Math.max(overallScore - (governanceValidation.violations.length * 10), 0),
        findings,
        recommendations: [
          'Maintain session limits within recommended thresholds',
          'Monitor and optimize logging performance',
          'Implement automated session cleanup',
          'Regular compliance monitoring',
          ...governanceValidation.recommendations
        ],
        remediation: [
          'Review session management policies',
          'Optimize logging infrastructure',
          'Implement performance monitoring alerts',
          'Address all governance violations'
        ],
        nextAuditDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // Next audit in 24 hours
      };

      this.logOperation('performComplianceAudit', 'complete', {
        auditId,
        status: auditResult.status,
        score: auditResult.score
      });
      this.incrementCounter('compliance_audits');

      return auditResult;

    } catch (error) {
      this.logError('performComplianceAudit', error, { auditType });
      throw error;
    }
  }

  /**
   * Get audit metrics
   * Implements IAuditableService.getAuditMetrics()
   */
  public async getAuditMetrics(): Promise<any> {
    try {
      this.logOperation('getAuditMetrics', 'start');

      const metrics = {
        sessionMetrics: {
          totalSessions: this._analytics.totalSessions,
          activeSessions: this._analytics.activeSessions,
          averageSessionDuration: this._analytics.averageSessionDuration,
          peakConcurrentSessions: this._analytics.peakConcurrentSessions
        },
        performanceMetrics: {
          averageLogWriteTime: this._performanceMonitor.avgLogWriteTime,
          totalWrites: this._performanceMonitor.totalWrites,
          failedWrites: this._performanceMonitor.failedWrites,
          writeSuccessRate: this._performanceMonitor.totalWrites > 0
            ? ((this._performanceMonitor.totalWrites - this._performanceMonitor.failedWrites) / this._performanceMonitor.totalWrites) * 100
            : 100
        },
        auditingMetrics: {
          lastRotation: new Date(this._performanceMonitor.lastRotation),
          logFilesManaged: Object.keys(this._logPaths).length,
          realtimeSubscriptions: Array.from(this._realtimeSubscribers.values()).reduce((sum, subs) => sum + subs.length, 0)
        },
        complianceMetrics: {
          errorRate: this._analytics.errorCount,
          warningRate: this._analytics.warningCount,
          overallHealthScore: this._calculateOverallHealthScore()
        }
      };

      this.logOperation('getAuditMetrics', 'complete');
      this.incrementCounter('audit_metrics_requests');

      return metrics;

    } catch (error) {
      this.logError('getAuditMetrics', error);
      throw error;
    }
  }

  /**
   * Schedule automatic audit
   * Implements IAuditableService.scheduleAudit()
   */
  public async scheduleAudit(frequency: number, auditType?: string): Promise<string> {
    try {
      this.logOperation('scheduleAudit', 'start', { frequency, auditType });

      const scheduleId = `session-audit-schedule-${Date.now()}`;
      const type = auditType || 'full';

      // Schedule periodic audit using coordinated timers
      const timerCoordinator = getTimerCoordinator();
      const timerId = timerCoordinator.createCoordinatedInterval(
        async () => {
          try {
            await this.performComplianceAudit(type as any);
            this.logOperation('scheduledAudit', 'completed', { scheduleId, auditType: type });
          } catch (error) {
            this.logError('scheduledAudit', error, { scheduleId, auditType: type });
          }
        },
        frequency * 60 * 60 * 1000, // Convert hours to milliseconds
        'SessionLogTracker',
        `audit-${scheduleId}`
      );

      // Store the interval ID for cleanup (in a real implementation)
      // this._scheduledAudits.set(scheduleId, intervalId);

      this.logOperation('scheduleAudit', 'complete', {
        scheduleId,
        frequency,
        auditType: type
      });
      this.incrementCounter('scheduled_audits');

      return scheduleId;

    } catch (error) {
      this.logError('scheduleAudit', error, { frequency, auditType });
      throw error;
    }
  }

  /**
   * Calculate overall health score for compliance metrics
   */
  private _calculateOverallHealthScore(): number {
    const writeSuccessRate = this._performanceMonitor.totalWrites > 0
      ? ((this._performanceMonitor.totalWrites - this._performanceMonitor.failedWrites) / this._performanceMonitor.totalWrites) * 100
      : 100;

    const performanceScore = this._performanceMonitor.avgLogWriteTime < 100 ? 100 : Math.max(0, 100 - (this._performanceMonitor.avgLogWriteTime - 100));
    const sessionHealthScore = this._analytics.activeSessions <= this._analytics.peakConcurrentSessions ? 100 : 80;

    return Math.round((writeSuccessRate + performanceScore + sessionHealthScore) / 3);
  }

  // ============================================================================
  // PRIVATE IMPLEMENTATION METHODS
  // ============================================================================

  /**
   * Initialize log directories
   */
  private async _initializeLogDirectories(): Promise<void> {
    try {
      // Ensure tracking directory exists
      const trackingDir = path.dirname(this._logPaths.current);
      if (!fs.existsSync(trackingDir)) {
        fs.mkdirSync(trackingDir, { recursive: true });
      }

      // Ensure archive directory exists
      if (!fs.existsSync(this._logPaths.archive)) {
        fs.mkdirSync(this._logPaths.archive, { recursive: true });
      }

      // Initialize log files if they don't exist
      if (!fs.existsSync(this._logPaths.current)) {
        fs.writeFileSync(this._logPaths.current, '');
      }

      if (!fs.existsSync(this._logPaths.error)) {
        fs.writeFileSync(this._logPaths.error, '');
      }

    } catch (error) {
      throw new Error(`Failed to initialize log directories: ${error}`);
    }
  }

  /**
   * Write log entry to file system
   */
  private async _writeLogToFile(logEntry: ISessionLogEntry): Promise<void> {
    try {
      const startTime = Date.now();

      // Write to main log file
      const logLine = JSON.stringify(logEntry) + '\n';
      fs.appendFileSync(this._logPaths.current, logLine);

      // Handle errors separately
      if (logEntry.level === 'error' && logEntry.error) {
        const errorLine = JSON.stringify({
          ...logEntry,
          errorDetails: logEntry.error
        }) + '\n';
        fs.appendFileSync(this._logPaths.error, errorLine);
      }

      // Update performance metrics
      const writeTime = Date.now() - startTime;
      this._performanceMonitor.logWriteTime += writeTime;
      this._performanceMonitor.totalWrites++;

      // Calculate average
      this._performanceMonitor.avgLogWriteTime =
        this._performanceMonitor.logWriteTime / this._performanceMonitor.totalWrites;

    } catch (error) {
      this._performanceMonitor.failedWrites++;
      this.logError('_writeLogToFile', error, {
        sessionId: logEntry.sessionId,
        eventType: logEntry.eventType
      });
      throw error;
    }
  }

  /**
   * Start log rotation scheduler
   */
  private _startLogRotationScheduler(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performLogRotation().catch(error => {
          this.logError('logRotation', error);
        });
      },
      this._rotationConfig.rotationInterval,
      'SessionLogTracker',
      'log-rotation'
    );
  }

  /**
   * Perform log rotation
   */
  private async _performLogRotation(): Promise<void> {
    try {
      const stats = fs.statSync(this._logPaths.current);

      if (stats.size >= this._rotationConfig.maxFileSize) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const archivePath = path.join(this._logPaths.archive, `session-log-${timestamp}.jsonl`);

        // Move current log to archive
        fs.renameSync(this._logPaths.current, archivePath);

        // Create new log file
        fs.writeFileSync(this._logPaths.current, '');

        // Compress archived log if enabled
        if (this._rotationConfig.compressionEnabled) {
          await this._compressLogFile(archivePath);
        }

        // Clean up old archives
        await this._cleanupOldArchives();

        this._performanceMonitor.lastRotation = Date.now();
      }
    } catch (error) {
      this.logError('performLogRotation', error);
    }
  }

  /**
   * Compress log file
   */
  private async _compressLogFile(filePath: string): Promise<void> {
    try {
      const zlib = require('zlib');
      const gzip = zlib.createGzip();
      const source = fs.createReadStream(filePath);
      const destination = fs.createWriteStream(`${filePath}.gz`);

      await new Promise((resolve, reject) => {
        source.pipe(gzip).pipe(destination)
          .on('finish', resolve)
          .on('error', reject);
      });

      // Remove uncompressed file
      fs.unlinkSync(filePath);
    } catch (error) {
      this.logError('compressLogFile', error, { filePath });
    }
  }

  /**
   * Clean up old archives
   */
  private async _cleanupOldArchives(): Promise<void> {
    try {
      const files = fs.readdirSync(this._logPaths.archive)
        .filter(file => file.startsWith('session-log-'))
        .map(file => ({
          name: file,
          path: path.join(this._logPaths.archive, file),
          stats: fs.statSync(path.join(this._logPaths.archive, file))
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());

      // Remove old files beyond the limit
      if (files.length > this._rotationConfig.maxArchiveFiles) {
        const filesToDelete = files.slice(this._rotationConfig.maxArchiveFiles);
        for (const file of filesToDelete) {
          fs.unlinkSync(file.path);
        }
      }
    } catch (error) {
      this.logError('cleanupOldArchives', error);
    }
  }

  /**
   * Load existing session data
   */
  private async _loadSessionData(): Promise<void> {
    try {
      if (fs.existsSync(this._logPaths.current)) {
        const content = fs.readFileSync(this._logPaths.current, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const entry = JSON.parse(line) as ISessionLogEntry;
            // Process existing log entries if needed
            this._analytics.totalEvents++;
          } catch (parseError) {
            // Skip invalid log entries
          }
        }
      }
    } catch (error) {
      this.logError('loadSessionData', error);
    }
  }

  /**
   * Initialize real-time monitoring
   */
  private async _initializeRealtimeMonitoring(): Promise<void> {
    // Initialize real-time event monitoring
    // This would integrate with WebSocket or Server-Sent Events in a full implementation
  }

  /**
   * Start performance monitoring
   */
  private async _startPerformanceMonitoring(): Promise<void> {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        try {
          this._updateSessionPerformanceMetrics();
        } catch (error) {
          this.logError('performance_monitoring', error as Error);
        }
      },
      30000, // Update every 30 seconds
      'SessionLogTracker',
      'performance-monitoring'
    );
  }

  /**
   * Update session performance metrics
   */
  private _updateSessionPerformanceMetrics(): void {
    // Calculate average log write time
    if (this._performanceMonitor.totalWrites > 0) {
      this._performanceMonitor.avgLogWriteTime =
        this._performanceMonitor.logWriteTime / this._performanceMonitor.totalWrites;
    }
  }

  /**
   * Validate log file integrity
   */
  private async _validateLogFileIntegrity(): Promise<void> {
    try {
      if (fs.existsSync(this._logPaths.current)) {
        const content = fs.readFileSync(this._logPaths.current, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());

        let validEntries = 0;
        let invalidEntries = 0;

        for (const line of lines) {
          try {
            JSON.parse(line);
            validEntries++;
          } catch (parseError) {
            invalidEntries++;
          }
        }

        if (invalidEntries > 0) {
          this.addWarning(
            VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
            `Found ${invalidEntries} invalid log entries out of ${validEntries + invalidEntries} total`,
            'warning'
          );
        }
      }
    } catch (error) {
      this.logError('validateLogFileIntegrity', error);
    }
  }

  /**
   * Convert tracking data to session data
   */
  private async _convertToSessionData(data: TTrackingData): Promise<ISessionLogData> {
    const sessionId = `session-${data.componentId}-${Date.now()}`;
    const currentEnv = process.env.NODE_ENV || 'development';
    const validatedEnv =
      (currentEnv === 'production' || currentEnv === 'staging') ?
      currentEnv as 'production' | 'staging' :
      'development' as const;

    const sessionData: ISessionLogData = {
      ...data,
      status: 'initialized' as TComponentStatus,
      session: {
        sessionId,
        startTime: data.timestamp,
        status: 'active',
        actor: data.authority?.validator || 'system',
        metadata: {
          type: 'system',
          environment: validatedEnv,
          custom: {}
        },
        events: [],
        performance: {
          totalEvents: 0,
          eventsByLevel: {},
          avgProcessingTime: 0,
          peakMemoryUsage: 0,
          efficiencyScore: 100
        },
        quality: {
          errorRate: 0,
          warningRate: 0,
          complianceScore: 100,
          authorityValidationRate: 100
        }
      }
    };

    return sessionData;
  }

  /**
   * Validate session data
   */
  private async _validateSessionData(data: ISessionLogData): Promise<void> {
    if (!data.session.sessionId) {
      throw new Error('Session ID is required');
    }

    if (!data.session.actor) {
      throw new Error('Session actor is required');
    }

    if (!data.session.startTime) {
      throw new Error('Session start time is required');
    }
  }

  /**
   * Process session tracking
   */
  private async _processSessionTracking(data: ISessionLogData): Promise<void> {
    // Update or create session
    this._activeSessions.set(data.session.sessionId, data);

    // Log tracking event
    const logEntry: ISessionLogEntry = {
      sessionId: data.session.sessionId,
      timestamp: data.timestamp,
      level: 'info',
      eventType: 'TRACKING_UPDATE',
      message: `Component ${data.componentId} tracked`,
      component: 'SessionLogTracker',
      actor: data.session.actor,
      context: {
        componentId: data.componentId,
        status: data.status
      }
    };

    // Add to session events
    data.session.events.push(logEntry);
    data.session.performance.totalEvents++;
    data.session.performance.eventsByLevel.info =
      (data.session.performance.eventsByLevel.info || 0) + 1;

    // Write to log file
    await this._writeLogToFile(logEntry);
  }

  /**
   * Update session analytics
   */
  private async _updateSessionAnalytics(data: ISessionLogData): Promise<void> {
    this._analytics.totalEvents++;

    // Update session-specific analytics
    const session = data.session;
    session.performance.totalEvents++;

    // Update quality metrics
    const errorEvents = session.events.filter(e => e.level === 'error').length;
    const warningEvents = session.events.filter(e => e.level === 'warn').length;

    if (session.performance.totalEvents > 0) {
      session.quality.errorRate = (errorEvents / session.performance.totalEvents) * 100;
      session.quality.warningRate = (warningEvents / session.performance.totalEvents) * 100;
    }

    // Update global analytics (increment by new errors/warnings, not set to session total)
    this._analytics.errorCount += errorEvents;
    this._analytics.warningCount += warningEvents;
  }

  /**
   * Notify real-time subscribers
   */
  private async _notifyRealtimeSubscribers(data: ISessionLogData): Promise<void> {
    const subscribers = this._realtimeSubscribers.get(data.session.sessionId);
    if (!subscribers || subscribers.length === 0) {
      return;
    }

    const realtimeData: TRealtimeData = {
      sessionId: data.session.sessionId,
      timestamp: new Date().toISOString(),
      actor: data.session.actor,
      eventCount: data.session.performance.totalEvents,
      status: data.session.status,
      performance: data.session.performance,
      quality: data.session.quality
    };

    const notificationPromises = subscribers.map(async (callback) => {
      try {
        await callback(realtimeData);
      } catch (error) {
        this.logError('realtime_notification', error as Error, {
          sessionId: data.session.sessionId,
          subscriber: callback.name || 'anonymous'
        });
      }
    });

    // Use Promise.allSettled to prevent one failed notification from blocking others
    await Promise.allSettled(notificationPromises);
  }

  /**
   * Persist session data
   */
  private async _persistSessionData(data: ISessionLogData): Promise<void> {
    // Session data is persisted through individual log entries
    // This method can be extended for additional persistence needs
  }

  /**
   * Calculate final session metrics
   */
  private async _calculateFinalSessionMetrics(sessionData: ISessionLogData): Promise<void> {
    const session = sessionData.session;
    const events = session.events;

    if (events.length > 0) {
      // Calculate average processing time
      const processingTimes = events
        .filter(e => e.performance?.duration)
        .map(e => e.performance!.duration!);

      if (processingTimes.length > 0) {
        session.performance.avgProcessingTime =
          processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
      }

      // Calculate efficiency score
      const errorRate = session.quality.errorRate;
      const warningRate = session.quality.warningRate;
      session.performance.efficiencyScore = Math.max(0, 100 - (errorRate * 2) - warningRate);

      // Update compliance score
      const authorityValidatedEvents = events.filter(e => e.authority?.validationStatus === 'validated').length;
      session.quality.authorityValidationRate =
        events.length > 0 ? (authorityValidatedEvents / events.length) * 100 : 100;

      session.quality.complianceScore =
        (session.quality.authorityValidationRate + session.performance.efficiencyScore) / 2;
    }
  }

  /**
   * End all active sessions
   */
  private async _endAllActiveSessions(): Promise<void> {
    const sessionIds = Array.from(this._activeSessions.keys());

    for (const sessionId of sessionIds) {
      try {
        await this.endSession(sessionId, 'Service shutdown');
      } catch (error) {
        this.logError('endAllActiveSessions', error, { sessionId });
      }
    }
  }

  /**
   * Flush pending log writes
   */
  private async _flushPendingLogs(): Promise<void> {
    // Ensure all pending writes are completed
    // In a production implementation, this would handle async write queues
  }

  /**
   * Save session analytics
   */
  private async _saveSessionAnalytics(): Promise<void> {
    try {
      const analyticsPath = path.join(
        path.dirname(this._logPaths.current),
        '.oa-session-analytics.json'
      );

      const analyticsData = {
        ...this._analytics,
        timestamp: new Date().toISOString(),
        performance: this._performanceMonitor
      };

      fs.writeFileSync(analyticsPath, JSON.stringify(analyticsData, null, 2));
    } catch (error) {
      this.logError('saveSessionAnalytics', error);
    }
  }

  /**
   * Stop performance monitoring
   */
  private async _stopPerformanceMonitoring(): Promise<void> {
    try {
      const timerCoordinator = getTimerCoordinator();
      if (typeof (timerCoordinator as any).clearServiceTimers === 'function') {
        (timerCoordinator as any).clearServiceTimers('SessionLogTracker');
      } else if (typeof (timerCoordinator as any).clearAllTimers === 'function') {
        (timerCoordinator as any).clearAllTimers();
      }
    } catch (e) {
      this.logWarning?.('timer_cleanup', 'Timer cleanup error during stopPerformanceMonitoring', { error: e instanceof Error ? e.message : String(e) });
    }
  }

  /**
   * Validate active sessions
   */
  private async _validateActiveSessions(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    for (const [sessionId, sessionData] of Array.from(this._activeSessions.entries())) {
      // Check session timeout
      const sessionAge = Date.now() - new Date(sessionData.session.startTime).getTime();
      const maxSessionAge = 24 * 60 * 60 * 1000; // 24 hours

      if (sessionAge > maxSessionAge) {
        warnings.push({
          code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
          message: `Session ${sessionId} has been active for over 24 hours`,
          severity: 'warning',
          field: 'sessionAge',
          timestamp: new Date(),
          component: 'session-log-tracker'
        });
      }

      // Check session quality
      if (sessionData.session.quality.errorRate > 10) {
        warnings.push({
          code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
          message: `Session ${sessionId} has high error rate: ${sessionData.session.quality.errorRate}%`,
          severity: 'warning',
          field: 'errorRate',
          timestamp: new Date(),
          component: 'session-log-tracker'
        });
      }
    }
  }

  /**
   * Validate log files
   */
  private async _validateLogFiles(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    try {
      // Check if log files exist
      if (!fs.existsSync(this._logPaths.current)) {
        errors.push({
          code: VALIDATION_ERROR_CODES.CONFIGURATION_ERROR,
          message: 'Session log file does not exist',
          severity: 'error',
          field: 'logFile',
          timestamp: new Date(),
          component: 'session-log-tracker'
        });
      }

      // Check log file size
      if (fs.existsSync(this._logPaths.current)) {
        const stats = fs.statSync(this._logPaths.current);
        if (stats.size > this._rotationConfig.maxFileSize * 0.9) {
          warnings.push({
            code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
            message: 'Session log file is approaching rotation size',
            severity: 'warning',
            field: 'logFileSize',
            timestamp: new Date(),
            component: 'session-log-tracker'
          });
        }
      }
    } catch (error) {
      errors.push({
        code: VALIDATION_ERROR_CODES.CONFIGURATION_ERROR,
        message: `Failed to validate log files: ${error}`,
        severity: 'error',
        field: 'logFileValidation',
        timestamp: new Date(),
        component: 'session-log-tracker'
      });
    }
  }

  /**
   * Validate session analytics
   */
  private async _validateSessionAnalytics(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Check analytics consistency
    if (this._analytics.activeSessions < 0) {
      errors.push({
        code: VALIDATION_ERROR_CODES.INVALID_TRACKING_DATA,
        message: 'Active sessions count cannot be negative',
        severity: 'error',
        field: 'activeSessions',
        timestamp: new Date(),
        component: 'session-log-tracker'
      });
    }

    if (this._analytics.activeSessions > this._analytics.totalSessions) {
      errors.push({
        code: VALIDATION_ERROR_CODES.INVALID_TRACKING_DATA,
        message: 'Active sessions cannot exceed total sessions',
        severity: 'error',
        field: 'sessionCounts',
        timestamp: new Date(),
        component: 'session-log-tracker'
      });
    }
  }

  /**
   * Validate performance metrics
   */
  private async _validatePerformanceMetrics(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    try {
      // Check memory usage
      const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024; // MB
      if (memoryUsage > getMemoryUsageThreshold()) {
        warnings.push({
          code: VALIDATION_WARNING_CODES.PERFORMANCE_DEGRADED,
          message: 'High memory usage detected',
          severity: 'warning',
          component: 'SessionLogTracker',
          timestamp: new Date(),
          field: 'memory'
        });
      }

      // Check CPU usage
      const cpuUsage = process.cpuUsage().user / 1000000; // Convert to seconds
      if (cpuUsage > getCpuUsageThreshold()) {
        warnings.push({
          code: VALIDATION_WARNING_CODES.PERFORMANCE_DEGRADED,
          message: 'High CPU usage detected',
          severity: 'warning',
          component: 'SessionLogTracker',
          timestamp: new Date(),
          field: 'cpu'
        });
      }

      // Check active sessions
      if (this._activeSessions.size >= this._maxActiveSessions) {
        errors.push({
          code: VALIDATION_ERROR_CODES.INVALID_INPUT,
          message: 'Maximum number of active sessions exceeded',
          severity: 'error',
          component: 'SessionLogTracker',
          timestamp: new Date(),
          field: 'activeSessions'
        });
      }

      // Check performance metrics
      if (this._performanceMonitor.avgLogWriteTime > 100) {
        warnings.push({
          code: VALIDATION_WARNING_CODES.PERFORMANCE_DEGRADED,
          message: 'Slow log write performance detected',
          severity: 'warning',
          component: 'SessionLogTracker',
          timestamp: new Date(),
          field: 'writePerformance'
        });
      }
    } catch (error) {
      errors.push({
        code: VALIDATION_ERROR_CODES.INVALID_FORMAT,
        message: 'Failed to validate performance metrics',
        severity: 'error',
        component: 'SessionLogTracker',
        timestamp: new Date(),
        field: 'performanceMetrics'
      });
    }
  }

  // ============================================================================
  // 🚨 M0 SECURITY INTEGRATION - SESSION FLOOD PROTECTION
  // ============================================================================

  /**
   * Validate session input parameters
   */
  private validateSessionInput(
    sessionId: string,
    actor: string,
    operation: 'start' | 'event' | 'end'
  ): { isValid: boolean; error?: string } {
    // Validate session ID
    if (!sessionId || typeof sessionId !== 'string') {
      return { isValid: false, error: 'Invalid session ID' };
    }

    if (sessionId.trim().length === 0) {
      return { isValid: false, error: 'Invalid session ID' };
    }

    if (sessionId.length > 128) {
      return { isValid: false, error: 'Session ID too long' };
    }

    // Validate actor
    if (!actor || typeof actor !== 'string') {
      return { isValid: false, error: 'Invalid actor' };
    }

    if (actor.trim().length === 0) {
      return { isValid: false, error: 'Invalid actor' };
    }

    if (actor.length > 128) {
      return { isValid: false, error: 'Actor name too long' };
    }

    // Operation-specific validation
    if (operation === 'event' || operation === 'end') {
      const session = this._activeSessions.get(sessionId);
      if (!session) {
        return { isValid: false, error: 'Session not found' };
      }

      if (session.session.status !== 'active') {
        return { isValid: false, error: 'Session is not active' };
      }

      if (session.session.actor !== actor) {
        return { isValid: false, error: 'Actor mismatch' };
      }
    }

    return { isValid: true };
  }

  /**
   * 🚨 M0 SECURITY INTEGRATION - Enforce session security measures
   */
  private async enforceSessionSecurity(
    actor: string,
    sessionId: string,
    operation: 'start' | 'event' | 'end'
  ): Promise<boolean> {
    try {
      // Validate input parameters first - most critical security check
      const validation = this.validateSessionInput(sessionId, actor, operation);
      if (!validation.isValid) {
        this.handleViolationAttempt(actor, 'invalid_input');
        throw new Error(validation.error || 'Invalid input parameters');
      }

      // Check if actor is blacklisted - second most critical security check
      if (this._blacklistedActors.has(actor)) {
        this.handleViolationAttempt(actor, 'blacklisted');
        throw new Error('Actor is blacklisted');
      }

      // For start operations, check maximum sessions per actor
      if (operation === 'start') {
        const actorSessions = this._actorSessions.get(actor);
        if (actorSessions && actorSessions.size >= this._maxSessionsPerActor) {
          this.handleViolationAttempt(actor, 'session_limit');
          throw new Error('Maximum sessions per actor limit reached');
        }
      }

      // For event operations, check rate limits and event counts
      if (operation === 'event') {
        const session = this._activeSessions.get(sessionId);
        if (!session) {
          throw new Error('Session not found');
        }

        // Check session timeout first
        const sessionStartTime = new Date(session.session.startTime).getTime();
        if (Date.now() - sessionStartTime > this._sessionTimeoutMs) {
          // Mark the session as expired
          session.session.status = 'expired';
          session.session.endTime = new Date().toISOString();
          session.session.duration = Date.now() - sessionStartTime;

          this.handleViolationAttempt(actor, 'session_timeout');
          throw new Error('Session has expired');
        }

        // Check rate limit before event count
        const now = Date.now();
        const rate = this._sessionEventRates.get(sessionId) || {
          events: 0,
          resetTime: now
        };

        // Reset rate if window has passed
        if (now - rate.resetTime >= this._rateLimitWindowMs) {
          rate.events = 0;
          rate.resetTime = now;
        }

        // Check if rate limit is exceeded
        if (rate.events >= this._maxEventsPerMinute) {
          this.handleViolationAttempt(actor, 'rate_limit');
          this._sessionSecurityMetrics.rateLimitViolations++;
          throw new Error('Event rate limit exceeded');
        }

        // Check total events limit
        const eventCount = this._sessionEventCounts.get(sessionId) || 0;
        if (eventCount >= this._maxEventsPerSession) {
          this.handleViolationAttempt(actor, 'event_limit');
          throw new Error('Maximum events per session limit reached');
        }

        // Update rate and event counts only after all checks pass
        rate.events++;
        this._sessionEventRates.set(sessionId, rate);
        this._sessionEventCounts.set(sessionId, eventCount + 1);
      }

      // Update security metrics
      this._sessionSecurityMetrics.totalChecks++;
      this._sessionSecurityMetrics.lastCheck = new Date().toISOString();
      if (operation === 'event') {
        this._sessionSecurityMetrics.lastEventCheck = new Date().toISOString();
        this._sessionSecurityMetrics.totalEventChecks++;
      }

      return true;
    } catch (error: unknown) {
      // Log security violation with detailed context
      this.logError('session_security', error instanceof Error ? error : new Error('Security check failed'), {
        actor,
        sessionId,
        operation,
        timestamp: new Date().toISOString(),
        securityMetrics: { ...this._sessionSecurityMetrics }
      });

      // Update violation metrics
      this._sessionSecurityMetrics.violations++;
      this._sessionSecurityMetrics.lastViolation = {
        actor,
        type: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };

      throw error;
    }
  }

  /**
   * Handle security violation attempts
   */
  private handleViolationAttempt(actor: string, violationType: string): void {
    const attempt = this._violationAttempts.get(actor) || { count: 0, lastAttempt: Date.now() };
    attempt.count++;
    attempt.lastAttempt = Date.now();
    this._violationAttempts.set(actor, attempt);

    // Update security metrics
    this._sessionSecurityMetrics.violations++;
    this._sessionSecurityMetrics.lastViolation = {
      actor,
      type: violationType,
      timestamp: new Date().toISOString()
    };

    // Check for blacklisting
    if (attempt.count >= this._maxFailedAttempts) {
      this.blacklistActor(actor);
    }
  }

  /**
   * 🚨 M0 SECURITY INTEGRATION - Blacklist actor
   */
  private blacklistActor(actor: string): void {
    this._blacklistedActors.add(actor);
    this._sessionSecurityMetrics.blacklistedActors++;

    // End all actor's sessions without triggering security enforcement
    const actorSessions = this._actorSessions.get(actor);
    if (actorSessions) {
      actorSessions.forEach(sessionId => {
        // Directly end session without security checks to avoid recursion
        const session = this._activeSessions.get(sessionId);
        if (session) {
          session.session.status = 'terminated';
          session.session.endTime = new Date().toISOString();
          session.session.duration = Date.now() - new Date(session.session.startTime).getTime();

          // Remove from active sessions and cleanup
          this._activeSessions.delete(sessionId);
          this._sessionEventCounts.delete(sessionId);
          this._sessionEventRates.delete(sessionId);
          this._realtimeSubscribers.delete(sessionId);

          // Log the termination
          this.logInfo('Session terminated due to security violation', {
            sessionId,
            actor,
            reason: 'security_violation'
          });
        }
      });
      this._actorSessions.delete(actor);
    }

    this.logInfo('Actor blacklisted', {
      actor,
      reason: 'Multiple security violations',
      duration: `${this._blacklistDurationMs / 1000 / 60} minutes`
    });

    // Schedule removal from blacklist using coordinated timeout
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._blacklistedActors.delete(actor);
        this.logInfo('Actor unblacklisted', { actor });
        // Remove this timer after execution
        timerCoordinator.removeCoordinatedTimer(`SessionLogTracker:unblacklist-${actor}`);
      },
      this._blacklistDurationMs,
      'SessionLogTracker',
      `unblacklist-${actor}`
    );
  }

  /**
   * Perform emergency cleanup
   */
  private async performEmergencyCleanup(): Promise<void> {
    try {
      // Enforce all boundaries
      await this.enforceSessionBoundaries();
      await this.enforceHistoryBoundaries();
      await this.enforceSubscriberBoundaries();

      // Clear rate limiting data
      this._sessionEventRates.clear();

      // Reset security metrics
      this._sessionSecurityMetrics.floodAttempts = 0;
      this._sessionSecurityMetrics.rateLimitViolations = 0;
      this._sessionSecurityMetrics.violationsByType.clear();

      this.logInfo('Emergency cleanup performed', {
        securityIntegration: 'M0-emergency-protocol',
        boundariesEnforced: true,
        lastEnforcement: new Date().toISOString()
      });
    } catch (error: unknown) {
      this.logError('emergency_cleanup', error instanceof Error ? error : new Error(String(error)));
    }
  }

  private _initializeResilientTimingSync(): void {
    try {
      this._resilientTimer = new ResilientTimer({ enableFallbacks: true, maxExpectedDuration: 10000, unreliableThreshold: 3, estimateBaseline: 5 });
      this._metricsCollector = new ResilientMetricsCollector({ enableFallbacks: true, cacheUnreliableValues: false, maxMetricsAge: 300000, defaultEstimates: new Map([
        ['doTrack', 5],
        ['startSession', 5],
        ['endSession', 5],
        ['logSessionEvent', 4],
        ['generateAuditTrail', 10],
        ['performComplianceAudit', 12]
      ]) });
    } catch (e) {
      this._resilientTimer = new ResilientTimer();
      this._metricsCollector = new ResilientMetricsCollector({ enableFallbacks: true, cacheUnreliableValues: true, maxMetricsAge: 300000, defaultEstimates: new Map() });
    }
  }

  // ============================================================================
  // PROTECTED METHODS FOR BASETRACKINGSERVICE COMPATIBILITY
  // ============================================================================

  /**
   * Get maximum history size
   */
  protected getMaxHistorySize(): number {
    return getMaxTrackingHistorySize();
  }
}