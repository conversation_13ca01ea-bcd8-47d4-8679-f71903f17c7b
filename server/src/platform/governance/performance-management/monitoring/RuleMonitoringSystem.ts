/**
 * @file Rule Monitoring System
 * @filepath server/src/platform/governance/performance-management/monitoring/RuleMonitoringSystem.ts
 * @task-id G-TSK-03.SUB-03.1.IMP-06
 * @component governance-rule-monitoring-system
 * @reference foundation-context.GOVERNANCE.PERFORMANCE.006
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier G1
 * @context foundation-context
 * @category Governance Performance
 * @created 2025-06-28
 * @modified 2025-06-28
 *
 * @description
 * Enterprise Rule Monitoring System providing:
 * - Real-time rule monitoring and analysis
 * - Security-first monitoring data access
 * - Comprehensive monitoring metrics collection
 * - Integration with governance systems
 * - Enterprise-grade error handling
 * - Predictive monitoring capabilities
 * - Performance impact analysis
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/interfaces/tracking/core-interfaces
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService
 * @enables server/src/platform/governance/performance-management
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, governance-performance
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-performance-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @security-level high
 * @documentation docs/contexts/foundation-context/governance/performance/rule-monitoring-system.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   security-enhanced: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-06-28) - Initial implementation with enterprise-grade monitoring and security features
 */

import { BaseTrackingService } from '../../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import { IManagementService } from '../../../../../../shared/src/interfaces/tracking/core-interfaces';
import {
  TValidationResult,
  TTrackingData,
  TTrackingConfig
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';
import * as crypto from 'crypto';

// ============================================================================
// SECURITY CONSTANTS AND CONFIGURATION
// ============================================================================

/**
 * Security configuration for monitoring system
 */
const MONITORING_SECURITY_CONFIG = {
  MAX_MONITORING_SESSIONS: 10000,
  SESSION_TIMEOUT: 30000, // 30 seconds
  ACCESS_TOKEN_EXPIRY: 3600000, // 1 hour
  AUDIT_RETENTION_DAYS: 90,
  MAX_AUDIT_ENTRIES: 1000,
  SECURITY_SCAN_INTERVAL: 300000, // 5 minutes
  MAX_METRICS_PER_SESSION: 1000,
  MAX_ALERTS_PER_SESSION: 100,
  ENCRYPTION_ALGORITHM: 'aes-256-gcm',
  MIN_PASSWORD_LENGTH: 16,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 900000 // 15 minutes
} as const;

/**
 * Monitoring operation types for audit trails
 */
const MONITORING_OPERATIONS = {
  START_MONITORING: 'start_monitoring',
  STOP_MONITORING: 'stop_monitoring',
  GET_METRICS: 'get_metrics',
  UPDATE_CONFIG: 'update_config',
  ACCESS_DENIED: 'access_denied',
  SECURITY_SCAN: 'security_scan',
  ALERT_TRIGGERED: 'alert_triggered',
  ALERT_RESOLVED: 'alert_resolved'
} as const;

/**
 * Monitoring status levels
 */
const MONITORING_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  WARNING: 'warning',
  ERROR: 'error',
  SUSPENDED: 'suspended'
} as const;

type MonitoringStatus = typeof MONITORING_STATUS[keyof typeof MONITORING_STATUS];

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

/**
 * Monitoring session
 */
interface IMonitoringSession {
  sessionId: string;
  ruleId: string;
  startTime: Date;
  lastUpdateTime: Date;
  status: MonitoringStatus;
  metrics: IMonitoringMetrics;
  alerts: IMonitoringAlert[];
  config: IMonitoringConfig;
  securityContext: ISecurityContext;
}

/**
 * Monitoring metrics
 */
interface IMonitoringMetrics {
  executionCount: number;
  successRate: number;
  averageResponseTime: number;
  cpuUsage: number;
  memoryUsage: number;
  errorRate: number;
  throughput: number;
  concurrentExecutions: number;
  lastExecutionTime?: Date;
}

/**
 * Monitoring alert
 */
interface IMonitoringAlert {
  alertId: string;
  type: 'performance' | 'security' | 'resource' | 'error' | 'compliance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolvedAt?: Date;
  metadata: Record<string, any>;
}

/**
 * Monitoring configuration
 */
interface IMonitoringConfig {
  enabled: boolean;
  metricsInterval: number;
  alertingEnabled: boolean;
  retentionPeriod: number;
  securityLevel: string;
  encryptionEnabled: boolean;
  thresholds: {
    cpu: number;
    memory: number;
    errorRate: number;
    responseTime: number;
  };
}

/**
 * Security context
 */
interface ISecurityContext {
  accessToken: string;
  permissions: string[];
  encryptionKey: Buffer;
  iv: Buffer;
  lastAuthTime: Date;
  loginAttempts: number;
}

/**
 * Enterprise Rule Monitoring System
 *
 * Provides comprehensive monitoring capabilities for rules with security-first
 * approach and enterprise-grade features.
 */
export class RuleMonitoringSystem extends BaseTrackingService implements IManagementService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-monitoring-system';

  // Monitoring management
  private readonly _monitoringSessions = new Map<string, IMonitoringSession>();
  private readonly _securityContexts = new Map<string, ISecurityContext>();
  private readonly _encryptionKeys = new Map<string, Buffer>();

  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
  private _initialized = false;

  // Metrics tracking
  private _totalSessions = 0;
  private _activeSessions = 0;
  private _totalAlerts = 0;
  private _totalMonitoringOperations = 0;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  constructor() {
    // Initialize with default TTrackingConfig structure
    const defaultTrackingConfig: TTrackingConfig = {
      service: {
        name: 'governance-rule-monitoring-system',
        version: '1.0.0',
        environment: 'production',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 5000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'security-policy'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 60000,
        monitoringEnabled: true,
        alertThresholds: {
          cpuUsage: 80,
          memoryUsage: 70,
          responseTime: 5000,
          errorRate: 5
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 100
      }
    };

    super(defaultTrackingConfig);
  }

  // ============================================================================
  // BASETRACKINGSERVICE IMPLEMENTATION
  // ============================================================================

  protected getServiceName(): string {
    return this._componentType;
  }

  protected getServiceVersion(): string {
    return this._version;
  }

  protected async doInitialize(): Promise<void> {
    try {
      this.logOperation('doInitialize', 'start');

      // Generate system security contexts
      await this._generateSystemSecurityContexts();

      // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
      const timerCoordinator = getTimerCoordinator();

      // Start monitoring interval
      timerCoordinator.createCoordinatedInterval(
        () => this._performMonitoring(),
        MONITORING_SECURITY_CONFIG.SESSION_TIMEOUT,
        'RuleMonitoringSystem',
        'monitoring'
      );

      // Start security scan interval
      timerCoordinator.createCoordinatedInterval(
        () => this._performSecurityScan(),
        MONITORING_SECURITY_CONFIG.SECURITY_SCAN_INTERVAL,
        'RuleMonitoringSystem',
        'security-scan'
      );

      this._initialized = true;

      this.logOperation('doInitialize', 'complete');
    } catch (error) {
      this.logError('doInitialize', error);
      throw error;
    }
  }

  protected async doTrack(data: TTrackingData): Promise<void> {
    try {
      // Update monitoring-specific metrics
      const metrics = {
        totalSessions: this._totalSessions,
        activeSessions: this._activeSessions,
        totalAlerts: this._totalAlerts,
        sessionRate: this._totalSessions > 0 ? (this._activeSessions / this._totalSessions) * 100 : 100
      };

      // Update base tracking metrics
      this.updatePerformanceMetric('queryExecutionTimes', metrics.totalSessions);
      this.incrementCounter('totalOperations', metrics.totalSessions);
      this.incrementCounter('successfulOperations', metrics.activeSessions);

      if (metrics.sessionRate < 80) {
        this.addWarning(
          'LOW_SESSION_RATE',
          `Low session rate detected: ${metrics.sessionRate.toFixed(2)}%`,
          'warning'
        );
      }

      // Update tracking data
      data.metadata.custom = {
        ...data.metadata.custom,
        ...metrics
      };

    } catch (error) {
      this.logError('doTrack', error);
      throw error;
    }
  }

  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate monitoring system state
      if (!this._initialized) {
        errors.push('Monitoring system not initialized');
      }

      // Validate session capacity
      if (this._monitoringSessions.size > MONITORING_SECURITY_CONFIG.MAX_MONITORING_SESSIONS * 0.9) {
        warnings.push('Monitoring session capacity approaching limit');
      }

      // Validate security contexts
      const expiredContexts = Array.from(this._securityContexts.values())
        .filter(context => this._isSecurityContextExpired(context)).length;
      
      if (expiredContexts > 0) {
        warnings.push(`${expiredContexts} expired security contexts detected`);
      }

      const result: TValidationResult = {
        validationId: `monitoring-system-val-${Date.now()}`,
        componentId: this._componentType,
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(),
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 20) - (warnings.length * 10)),
        checks: [],
        references: {
          componentId: this._componentType,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings,
        warnings,
        errors,
        metadata: {
          validationMethod: 'monitoring-system-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', { 
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  protected async doShutdown(): Promise<void> {
    try {
      this.logOperation('doShutdown', 'start');

      // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

      // Clear data
      this._monitoringSessions.clear();
      this._securityContexts.clear();
      this._encryptionKeys.clear();

      this._initialized = false;

      this.logOperation('doShutdown', 'complete');
    } catch (error) {
      this.logError('doShutdown', error);
      throw error;
    }
  }

  // ============================================================================
  // IMANAGEMENTSERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  public async initialize(): Promise<void> {
    await super.initialize();
  }

  public async getHealth(): Promise<any> {
    try {
      this.validateInitialized();

      const health = {
        service: this._componentType,
        version: this._version,
        status: this._initialized ? 'healthy' : 'not_initialized',
        timestamp: new Date(),
        metrics: {
          totalSessions: this._totalSessions,
          activeSessions: this._activeSessions,
          totalAlerts: this._totalAlerts,
          sessionRate: this._totalSessions > 0 ? (this._activeSessions / this._totalSessions) * 100 : 100
        }
      };

      return health;
    } catch (error) {
      this.logError('getHealth', error);
      throw error;
    }
  }

  public async getMetrics(): Promise<any> {
    try {
      this.validateInitialized();

      const metrics = {
        timestamp: new Date().toISOString(),
        service: this._componentType,
        performance: {
          totalSessions: this._totalSessions,
          activeSessions: this._activeSessions,
          totalAlerts: this._totalAlerts,
          sessionRate: this._totalSessions > 0 ? (this._activeSessions / this._totalSessions) * 100 : 100
        },
        security: {
          activeSecurityContexts: this._securityContexts.size,
          activeEncryptionKeys: this._encryptionKeys.size
        }
      };

      return metrics;
    } catch (error) {
      this.logError('getMetrics', error);
      throw error;
    }
  }

  public async shutdown(): Promise<void> {
    await super.shutdown();
  }

  // ============================================================================
  // PUBLIC MONITORING METHODS
  // ============================================================================

  /**
   * Start monitoring a rule
   */
  public async startMonitoring(
    ruleId: string,
    accessToken?: string,
    config?: Partial<IMonitoringConfig>
  ): Promise<string> {
    try {
      this.validateInitialized();
      this.validateRuleId(ruleId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, MONITORING_OPERATIONS.START_MONITORING);
      if (!tokenValidation.valid) {
        await this._auditOperation(ruleId, MONITORING_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      // Generate session ID
      const sessionId = this._generateSessionId();

      // Create monitoring session
      const session: IMonitoringSession = {
        sessionId,
        ruleId,
        startTime: new Date(),
        lastUpdateTime: new Date(),
        status: MONITORING_STATUS.ACTIVE,
        metrics: {
          executionCount: 0,
          successRate: 100,
          averageResponseTime: 0,
          cpuUsage: 0,
          memoryUsage: 0,
          errorRate: 0,
          throughput: 0,
          concurrentExecutions: 0
        },
        alerts: [],
        config: {
          enabled: true,
          metricsInterval: config?.metricsInterval ?? 60000,
          alertingEnabled: config?.alertingEnabled ?? true,
          retentionPeriod: config?.retentionPeriod ?? 86400000,
          securityLevel: config?.securityLevel ?? 'high',
          encryptionEnabled: config?.encryptionEnabled ?? true,
          thresholds: {
            cpu: config?.thresholds?.cpu ?? 80,
            memory: config?.thresholds?.memory ?? 70,
            errorRate: config?.thresholds?.errorRate ?? 5,
            responseTime: config?.thresholds?.responseTime ?? 5000
          }
        },
        securityContext: tokenValidation.token ?? {
          accessToken: 'anonymous',
          permissions: [],
          encryptionKey: crypto.randomBytes(32),
          iv: crypto.randomBytes(16),
          lastAuthTime: new Date(),
          loginAttempts: 0
        }
      };

      // Store session
      this._monitoringSessions.set(sessionId, session);
      this._totalSessions++;
      this._activeSessions++;

      // Audit operation
      await this._auditOperation(sessionId, MONITORING_OPERATIONS.START_MONITORING, accessToken, true);

      this.logOperation('startMonitoring', 'complete', {
        sessionId,
        ruleId
      });

      return sessionId;

    } catch (error) {
      this.logError('startMonitoring', error);
      throw error;
    }
  }

  /**
   * Stop monitoring a rule
   */
  public async stopMonitoring(sessionId: string, accessToken?: string): Promise<void> {
    try {
      this.validateInitialized();
      this.validateSessionId(sessionId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, MONITORING_OPERATIONS.STOP_MONITORING);
      if (!tokenValidation.valid) {
        await this._auditOperation(sessionId, MONITORING_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      // Get session
      const session = this._monitoringSessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Update session status
      session.status = MONITORING_STATUS.INACTIVE;
      this._monitoringSessions.delete(sessionId);
      this._activeSessions--;

      // Audit operation
      await this._auditOperation(sessionId, MONITORING_OPERATIONS.STOP_MONITORING, accessToken, true);

      this.logOperation('stopMonitoring', 'complete', {
        sessionId
      });

    } catch (error) {
      this.logError('stopMonitoring', error);
      throw error;
    }
  }

  /**
   * Update monitoring metrics
   */
  public async updateMetrics(
    sessionId: string,
    metrics: Partial<IMonitoringMetrics>,
    accessToken?: string
  ): Promise<void> {
    try {
      this.validateInitialized();
      this.validateSessionId(sessionId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, MONITORING_OPERATIONS.UPDATE_CONFIG);
      if (!tokenValidation.valid) {
        await this._auditOperation(sessionId, MONITORING_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      // Get session
      const session = this._monitoringSessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Update metrics
      session.metrics = {
        ...session.metrics,
        ...metrics,
        lastExecutionTime: new Date()
      };

      // Check thresholds and generate alerts
      await this._checkThresholds(session);

      // Update session
      session.lastUpdateTime = new Date();
      this._monitoringSessions.set(sessionId, session);

      this.logOperation('updateMetrics', 'complete', {
        sessionId
      });

    } catch (error) {
      this.logError('updateMetrics', error);
      throw error;
    }
  }

  /**
   * Get monitoring session status
   */
  public async getSessionStatus(sessionId: string, accessToken?: string): Promise<{
    status: MonitoringStatus;
    metrics: IMonitoringMetrics;
    alerts: IMonitoringAlert[];
  }> {
    try {
      this.validateInitialized();
      this.validateSessionId(sessionId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, MONITORING_OPERATIONS.GET_METRICS);
      if (!tokenValidation.valid) {
        await this._auditOperation(sessionId, MONITORING_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Invalid token'}`);
      }

      // Get session
      const session = this._monitoringSessions.get(sessionId);
      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      return {
        status: session.status,
        metrics: session.metrics,
        alerts: session.alerts
      };

    } catch (error) {
      this.logError('getSessionStatus', error);
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  private async _generateSystemSecurityContexts(): Promise<void> {
    try {
      this.logOperation('_generateSystemSecurityContexts', 'start');

      // Generate admin security context
      const adminContext: ISecurityContext = {
        accessToken: crypto.randomBytes(32).toString('hex'),
        permissions: ['*'],
        encryptionKey: crypto.randomBytes(32),
        iv: crypto.randomBytes(16),
        lastAuthTime: new Date(),
        loginAttempts: 0
      };

      this._securityContexts.set(adminContext.accessToken, adminContext);
      this._encryptionKeys.set(adminContext.accessToken, adminContext.encryptionKey);

      // Generate read-only security context
      const readContext: ISecurityContext = {
        accessToken: crypto.randomBytes(32).toString('hex'),
        permissions: [MONITORING_OPERATIONS.GET_METRICS],
        encryptionKey: crypto.randomBytes(32),
        iv: crypto.randomBytes(16),
        lastAuthTime: new Date(),
        loginAttempts: 0
      };

      this._securityContexts.set(readContext.accessToken, readContext);
      this._encryptionKeys.set(readContext.accessToken, readContext.encryptionKey);

      this.logOperation('_generateSystemSecurityContexts', 'complete', {
        contextsGenerated: 2
      });

    } catch (error) {
      this.logError('_generateSystemSecurityContexts', error);
      throw error;
    }
  }

  private async _performMonitoring(): Promise<void> {
    try {
      this.logOperation('_performMonitoring', 'start');

      // Perform monitoring for each session
      const sessions = Array.from(this._monitoringSessions.entries());
      for (const [sessionId, session] of sessions) {
        await this._monitorSession(session);
      }

      this._totalMonitoringOperations++;

      this.logOperation('_performMonitoring', 'complete', {
        sessionsMonitored: this._monitoringSessions.size
      });

    } catch (error) {
      this.logError('_performMonitoring', error);
    }
  }

  private async _performSecurityScan(): Promise<void> {
    try {
      this.logOperation('_performSecurityScan', 'start');

      // Clean up expired security contexts
      const now = new Date();
      let expiredCount = 0;
      
      const contexts = Array.from(this._securityContexts.entries());
      for (const [token, context] of contexts) {
        if (this._isSecurityContextExpired(context)) {
          this._securityContexts.delete(token);
          this._encryptionKeys.delete(token);
          expiredCount++;
        }
      }

      this.logOperation('_performSecurityScan', 'complete', {
        expiredContextsRemoved: expiredCount,
        activeContexts: this._securityContexts.size
      });

    } catch (error) {
      this.logError('_performSecurityScan', error);
    }
  }

  private _isSecurityContextExpired(context: ISecurityContext): boolean {
    const now = new Date();
    return now.getTime() - context.lastAuthTime.getTime() > MONITORING_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY;
  }

  private validateInitialized(): void {
    if (!this._initialized) {
      throw new Error('Monitoring system not initialized');
    }
  }

  private async _validateAccessToken(
    accessToken?: string,
    operation?: string
  ): Promise<{ valid: boolean; token?: ISecurityContext; error?: string }> {
    try {
      if (!accessToken) {
        return { valid: true };
      }

      const context = this._securityContexts.get(accessToken);
      if (!context) {
        return { valid: false, error: 'Token not found' };
      }

      if (this._isSecurityContextExpired(context)) {
        this._securityContexts.delete(accessToken);
        return { valid: false, error: 'Token expired' };
      }

      if (context.loginAttempts >= MONITORING_SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
        return { valid: false, error: 'Account locked due to too many attempts' };
      }

      if (operation && !context.permissions.includes(operation) && !context.permissions.includes('*')) {
        context.loginAttempts++;
        return { valid: false, error: 'Insufficient permissions' };
      }

      // Reset login attempts on successful validation
      context.loginAttempts = 0;
      context.lastAuthTime = new Date();

      return { valid: true, token: context };

    } catch (error) {
      this.logError('_validateAccessToken', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  private async _checkThresholds(session: IMonitoringSession): Promise<void> {
    try {
      const { metrics, config } = session;

      // Check CPU usage
      if (metrics.cpuUsage > config.thresholds.cpu) {
        await this._addAlert(session, {
          type: 'resource',
          severity: metrics.cpuUsage > config.thresholds.cpu * 1.2 ? 'critical' : 'high',
          message: `High CPU usage detected: ${metrics.cpuUsage.toFixed(1)}%`,
          timestamp: new Date(),
          metadata: { metric: 'cpu', value: metrics.cpuUsage, threshold: config.thresholds.cpu }
        });
      }

      // Check memory usage
      if (metrics.memoryUsage > config.thresholds.memory) {
        await this._addAlert(session, {
          type: 'resource',
          severity: metrics.memoryUsage > config.thresholds.memory * 1.2 ? 'critical' : 'high',
          message: `High memory usage detected: ${metrics.memoryUsage.toFixed(1)}%`,
          timestamp: new Date(),
          metadata: { metric: 'memory', value: metrics.memoryUsage, threshold: config.thresholds.memory }
        });
      }

      // Check error rate
      if (metrics.errorRate > config.thresholds.errorRate) {
        await this._addAlert(session, {
          type: 'error',
          severity: metrics.errorRate > config.thresholds.errorRate * 2 ? 'critical' : 'high',
          message: `High error rate detected: ${metrics.errorRate.toFixed(1)}%`,
          timestamp: new Date(),
          metadata: { metric: 'errorRate', value: metrics.errorRate, threshold: config.thresholds.errorRate }
        });
      }

      // Check response time
      if (metrics.averageResponseTime > config.thresholds.responseTime) {
        await this._addAlert(session, {
          type: 'performance',
          severity: metrics.averageResponseTime > config.thresholds.responseTime * 2 ? 'critical' : 'high',
          message: `High response time detected: ${metrics.averageResponseTime.toFixed(1)}ms`,
          timestamp: new Date(),
          metadata: { metric: 'responseTime', value: metrics.averageResponseTime, threshold: config.thresholds.responseTime }
        });
      }

    } catch (error) {
      this.logError('_checkThresholds', error);
    }
  }

  private async _addAlert(session: IMonitoringSession, alert: Omit<IMonitoringAlert, 'alertId'>): Promise<void> {
    try {
      const alertId = crypto.randomBytes(16).toString('hex');
      const fullAlert: IMonitoringAlert = {
        ...alert,
        alertId
      };

      session.alerts.push(fullAlert);
      this._totalAlerts++;

      // Keep only recent alerts
      if (session.alerts.length > MONITORING_SECURITY_CONFIG.MAX_ALERTS_PER_SESSION) {
        session.alerts = session.alerts
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
          .slice(0, MONITORING_SECURITY_CONFIG.MAX_ALERTS_PER_SESSION);
      }

      // Audit alert
      await this._auditOperation(session.sessionId, MONITORING_OPERATIONS.ALERT_TRIGGERED, session.securityContext.accessToken, true, undefined, {
        alertId,
        type: alert.type,
        severity: alert.severity
      });

    } catch (error) {
      this.logError('_addAlert', error);
    }
  }

  private async _auditOperation(
    sessionId: string,
    operation: string,
    accessToken?: string,
    success: boolean = true,
    errorMessage?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Log audit event
      this.logOperation('audit', operation, {
        sessionId,
        success,
        accessToken: accessToken ?? 'anonymous',
        errorMessage,
        metadata
      });

    } catch (error) {
      this.logError('_auditOperation', error);
    }
  }

  private _generateSessionId(): string {
    return `monitoring-session-${Date.now()}-${crypto.randomBytes(8).toString('hex')}`;
  }

  private validateSessionId(sessionId: string): void {
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('Invalid session ID: must be a non-empty string');
    }
  }

  private validateRuleId(ruleId: string): void {
    if (!ruleId || typeof ruleId !== 'string') {
      throw new Error('Invalid rule ID: must be a non-empty string');
    }
  }

  private async _monitorSession(session: IMonitoringSession): Promise<void> {
    try {
      // Skip inactive sessions
      if (session.status !== MONITORING_STATUS.ACTIVE) {
        return;
      }

      // Check session timeout
      const now = new Date();
      const timeSinceLastUpdate = now.getTime() - session.lastUpdateTime.getTime();
      if (timeSinceLastUpdate > MONITORING_SECURITY_CONFIG.SESSION_TIMEOUT) {
        session.status = MONITORING_STATUS.SUSPENDED;
        await this._addAlert(session, {
          type: 'performance',
          severity: 'high',
          message: 'Session suspended due to inactivity',
          timestamp: now,
          metadata: {
            timeSinceLastUpdate,
            sessionTimeout: MONITORING_SECURITY_CONFIG.SESSION_TIMEOUT
          }
        });
        return;
      }

      // Update session metrics
      session.metrics = {
        ...session.metrics,
        lastExecutionTime: now
      };

      // Check thresholds
      await this._checkThresholds(session);

      // Update session
      session.lastUpdateTime = now;
      this._monitoringSessions.set(session.sessionId, session);

    } catch (error) {
      this.logError('_monitorSession', error);
      
      // Add error alert
      await this._addAlert(session, {
        type: 'error',
        severity: 'high',
        message: 'Error during session monitoring',
        timestamp: new Date(),
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }
} 