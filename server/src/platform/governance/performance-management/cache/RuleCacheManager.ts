/**
 * @file Rule Cache Manager
 * @filepath server/src/platform/governance/performance-management/cache/RuleCacheManager.ts
 * @task-id G-TSK-03.SUB-03.1.IMP-01
 * @component governance-rule-cache-manager
 * @reference foundation-context.GOVERNANCE.PERFORMANCE.001
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier G1
 * @context foundation-context
 * @category Governance Performance
 * @created 2025-06-28
 * @modified 2025-06-28 03:06:41 +03
 * 
 * @description
 * Enterprise Rule Cache Management System providing:
 * - High-performance rule caching with memory protection
 * - Security-first cache access control and validation
 * - Intelligent cache invalidation and refresh strategies
 * - Comprehensive audit trails and monitoring
 * - Memory boundary enforcement and leak prevention
 * - Encrypted cache storage for sensitive rule data
 * - Performance optimization with configurable TTL
 * - Enterprise-grade scalability and reliability
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/interfaces/tracking/core-interfaces
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService
 * @enables server/src/platform/governance/performance-management
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, governance-performance
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-performance-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @security-level high
 * @documentation docs/contexts/foundation-context/governance/performance/rule-cache-manager.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   security-enhanced: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-06-28) - Initial implementation with enterprise-grade caching and security features
 */

import { EventEmitter } from 'events';
import * as crypto from 'crypto';
import { BaseTrackingService } from '../../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import { 
  ICacheManager,
  IPerformanceService 
} from '../../../../../../shared/src/interfaces/tracking/core-interfaces';
import {
  TValidationResult,
  TGovernanceValidation,
  TAuditResult,
  TMetrics,
  TTrackingData,
  TTrackingConfig,
  TRetryConfig
} from '../../../../../../shared/src/types/platform/tracking';
import {
  TGovernanceRule,
  TRuleExecutionResult,
  TGovernanceRuleSet,
  TCacheStatistics,
  TRulePerformanceMetrics
} from '../../../../../../shared/src/types/platform/governance/rule-management-types';

// ============================================================================
// SECURITY CONSTANTS AND CONFIGURATION
// ============================================================================

/**
 * Security configuration for rule cache
 */
const CACHE_SECURITY_CONFIG = {
  ENCRYPTION_ALGORITHM: 'aes-256-gcm',
  KEY_DERIVATION_ITERATIONS: 100000,
  SALT_LENGTH: 32,
  IV_LENGTH: 16,
  TAG_LENGTH: 16,
  MAX_CACHE_SIZE: 1000,
  MAX_MEMORY_USAGE: 512 * 1024 * 1024, // 512MB
  ACCESS_TOKEN_EXPIRY: 3600000, // 1 hour
  AUDIT_RETENTION_DAYS: 90
} as const;

/**
 * Cache operation types for audit trails
 */
const CACHE_OPERATIONS = {
  GET: 'cache_get',
  SET: 'cache_set',
  DELETE: 'cache_delete',
  CLEAR: 'cache_clear',
  INVALIDATE: 'cache_invalidate',
  ENCRYPT: 'cache_encrypt',
  DECRYPT: 'cache_decrypt',
  ACCESS_DENIED: 'cache_access_denied'
} as const;

/**
 * Security levels for cached rules - Fixed to use uppercase constants
 */
const SECURITY_LEVELS = {
  PUBLIC: 'PUBLIC',
  INTERNAL: 'INTERNAL',
  CONFIDENTIAL: 'CONFIDENTIAL',
  RESTRICTED: 'RESTRICTED'
} as const;

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

/**
 * Cache entry with security metadata
 */
interface ICacheEntry {
  key: string;
  value: any;
  encrypted: boolean;
  securityLevel: keyof typeof SECURITY_LEVELS;
  accessCount: number;
  createdAt: Date;
  lastAccessed: Date;
  expiresAt: Date;
  checksum: string;
  accessTokens: Set<string>;
  auditTrail: ICacheAuditEntry[];
}

/**
 * Cache audit entry
 */
interface ICacheAuditEntry {
  operation: string;
  timestamp: Date;
  accessToken: string;
  success: boolean;
  errorMessage?: string;
  metadata: Record<string, any>;
}

/**
 * Cache access token
 */
interface ICacheAccessToken {
  token: string;
  permissions: string[];
  expiresAt: Date;
  issuedFor: string;
  securityLevel: keyof typeof SECURITY_LEVELS;
}

/**
 * Cache configuration - Compatible with TTrackingConfig
 */
interface ICacheConfig {
  maxSize: number;
  defaultTTL: number;
  enableEncryption: boolean;
  enableAuditTrail: boolean;
  memoryThreshold: number;
  cleanupInterval: number;
  compressionEnabled: boolean;
}

/**
 * Cache metrics structure
 */
interface ICacheMetrics {
  totalEntries: number;
  hitCount: number;
  missCount: number;
  evictionCount: number;
  memoryUsage: number;
  averageAccessTime: number;
  encryptionOperations: number;
  securityViolations: number;
  lastCleanup: Date;
  cacheEfficiency: number;
}

/**
 * Performance metrics structure
 */
interface IPerformanceMetrics {
  totalExecutions: number;
  averageExecutionTime: number;
  successRate: number;
  errorRate: number;
  throughput: number;
  peakMemoryUsage: number;
  cacheHitRatio: number;
  optimizationSavings: number;
}

// ============================================================================
// MAIN IMPLEMENTATION
// ============================================================================

/**
 * Enterprise Rule Cache Manager
 * 
 * High-performance caching system with enterprise security features:
 * - Memory-protected cache storage with boundary enforcement
 * - Encrypted storage for sensitive governance rules
 * - Access control with token-based authentication
 * - Comprehensive audit trails and monitoring
 * - Intelligent cache invalidation strategies
 * - Performance optimization with configurable policies
 * - Memory leak prevention and resource management
 */
export class RuleCacheManager extends BaseTrackingService implements ICacheManager, IPerformanceService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-cache-manager';
  
  // Cache storage and management
  private readonly _cache = new Map<string, ICacheEntry>();
  private readonly _accessTokens = new Map<string, ICacheAccessToken>();
  private readonly _encryptionKeys = new Map<string, Buffer>();
  
  // Security and access control
  private readonly _masterKey: Buffer;
  private readonly _securitySalt: Buffer;
  private _currentMemoryUsage: number = 0;
  
  // Configuration and monitoring
  private _cacheConfig: ICacheConfig;
  private _cacheMetrics: ICacheMetrics;
  private _performanceMetrics: IPerformanceMetrics;
  
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
  private _initialized: boolean = false;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  constructor(config?: Partial<ICacheConfig>) {
    // Initialize with default TTrackingConfig structure - Fixed configuration
    const defaultTrackingConfig: Partial<TTrackingConfig> = {
      service: {
        name: 'governance-rule-cache-manager',
        version: '1.0.0',
        environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 5000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'security-policy'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 60000,
        monitoringEnabled: true,
        alertThresholds: {
          cpuUsage: 80,
          memoryUsage: 70,
          responseTime: 5000,
          errorRate: 5
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 100
      }
    };

    super(defaultTrackingConfig);
    
    // Initialize security components
    this._masterKey = crypto.randomBytes(32);
    this._securitySalt = crypto.randomBytes(CACHE_SECURITY_CONFIG.SALT_LENGTH);
    
    // Initialize cache configuration with nullish coalescing for defaults
    this._cacheConfig = {
      maxSize: config?.maxSize ?? CACHE_SECURITY_CONFIG.MAX_CACHE_SIZE,
      defaultTTL: config?.defaultTTL ?? 3600000, // 1 hour
      enableEncryption: config?.enableEncryption ?? true,
      enableAuditTrail: config?.enableAuditTrail ?? true,
      memoryThreshold: config?.memoryThreshold ?? CACHE_SECURITY_CONFIG.MAX_MEMORY_USAGE,
      cleanupInterval: config?.cleanupInterval ?? 300000, // 5 minutes
      compressionEnabled: config?.compressionEnabled ?? true
    };
    
    // Initialize metrics
    this._cacheMetrics = {
      totalEntries: 0,
      hitCount: 0,
      missCount: 0,
      evictionCount: 0,
      memoryUsage: 0,
      averageAccessTime: 0,
      encryptionOperations: 0,
      securityViolations: 0,
      lastCleanup: new Date(),
      cacheEfficiency: 0
    };
    
    this._performanceMetrics = {
      totalExecutions: 0,
      averageExecutionTime: 0,
      successRate: 0,
      errorRate: 0,
      throughput: 0,
      peakMemoryUsage: 0,
      cacheHitRatio: 0,
      optimizationSavings: 0
    };
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS FROM BASETRACKINGSERVICE
  // ============================================================================

  /**
   * Get service name
   */
  protected getServiceName(): string {
    return this._componentType;
  }

  /**
   * Get service version
   */
  protected getServiceVersion(): string {
    return this._version;
  }

  /**
   * Perform service-specific initialization
   */
  protected async doInitialize(): Promise<void> {
    try {
      await super.doInitialize();
      this.logOperation('doInitialize', 'start');

      // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
      const timerCoordinator = getTimerCoordinator();

      // Start cleanup interval
      timerCoordinator.createCoordinatedInterval(
        () => this._performCleanup(),
        this._cacheConfig.cleanupInterval,
        'RuleCacheManager',
        'cache-cleanup'
      );

      // Start metrics collection
      timerCoordinator.createCoordinatedInterval(
        () => this._updateCacheMetrics(),
        60000, // 1 minute
        'RuleCacheManager',
        'metrics-collection'
      );

      // Generate initial access tokens
      await this._generateSystemAccessTokens();

      this._initialized = true;
      this.logOperation('doInitialize', 'complete');

    } catch (error) {
      this.logError('doInitialize', error);
      throw error;
    }
  }

  /**
   * Perform service-specific tracking
   */
  protected async doTrack(data: TTrackingData): Promise<void> {
    try {
      this.logOperation('doTrack', 'start', { componentId: data.componentId });

      // Track cache operations - Fixed metadata handling
      if (data.metadata && typeof data.metadata === 'object') {
        const metadata = data.metadata as Record<string, unknown>;
        if (metadata.cacheOperation) {
          await this._auditCacheOperation(
            (metadata.cacheKey as string) || 'unknown',
            metadata.cacheOperation as string,
            metadata.accessToken as string,
            (metadata.success as boolean) || false,
            metadata.errorMessage as string
          );
        }
      }

      this.logOperation('doTrack', 'complete');

    } catch (error) {
      this.logError('doTrack', error);
      throw error;
    }
  }

  /**
   * Perform service-specific validation
   */
  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate cache health
      if (!this._initialized) {
        errors.push('Cache manager not initialized');
      }

      // Validate memory usage
      if (this._currentMemoryUsage > this._cacheConfig.memoryThreshold) {
        errors.push(`Memory usage exceeds threshold: ${this._currentMemoryUsage} > ${this._cacheConfig.memoryThreshold}`);
      }

      // Validate cache size
      if (this._cache.size > this._cacheConfig.maxSize) {
        errors.push(`Cache size exceeds limit: ${this._cache.size} > ${this._cacheConfig.maxSize}`);
      }

      // Validate security
      if (this._cacheMetrics.securityViolations > 0) {
        warnings.push(`Security violations detected: ${this._cacheMetrics.securityViolations}`);
      }

      // Validate performance
      if (this._cacheMetrics.cacheEfficiency < 50) {
        warnings.push(`Low cache efficiency: ${this._cacheMetrics.cacheEfficiency}%`);
      }

      const result: TValidationResult = {
        validationId: `cache-manager-val-${Date.now()}`,
        componentId: this._componentType,
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(),
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 20) - (warnings.length * 10)),
        checks: [],
        references: {
          componentId: this._componentType,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings,
        warnings,
        errors,
        metadata: {
          validationMethod: 'cache-manager-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', { 
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  /**
   * Perform service-specific shutdown
   */
  protected async doShutdown(): Promise<void> {
    try {
      this.logOperation('doShutdown', 'start');

      // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

      // Clear sensitive data
      this._cache.clear();
      this._accessTokens.clear();
      this._encryptionKeys.clear();

      this._initialized = false;
      this.logOperation('doShutdown', 'complete');
      await super.doShutdown();

    } catch (error) {
      this.logError('doShutdown', error);
      throw error;
    }
  }

  // ============================================================================
  // ICACHEMANAGER INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Get cached rule with security validation
   */
  public async get(key: string, accessToken?: string): Promise<any> {
    try {
      this.validateInitialized();
      this.validateCacheKey(key);

      const startTime = Date.now();
      
      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, 'read');
      if (!tokenValidation.valid) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        this._cacheMetrics.securityViolations = (this._cacheMetrics.securityViolations ?? 0) + 1;
        throw new Error(`Cache access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      // Get cache entry
      const entry = this._cache.get(key);
      if (!entry) {
        this._cacheMetrics.missCount = (this._cacheMetrics.missCount ?? 0) + 1;
        await this._auditCacheOperation(key, CACHE_OPERATIONS.GET, accessToken, false, 'Cache miss');
        return null;
      }

      // Check expiration
      if (entry.expiresAt < new Date()) {
        this._cache.delete(key);
        this._cacheMetrics.evictionCount = (this._cacheMetrics.evictionCount ?? 0) + 1;
        this._cacheMetrics.missCount = (this._cacheMetrics.missCount ?? 0) + 1;
        await this._auditCacheOperation(key, CACHE_OPERATIONS.GET, accessToken, false, 'Entry expired');
        return null;
      }

      // Validate security level access
      if (!this._canAccessSecurityLevel(tokenValidation.token!, entry.securityLevel)) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.ACCESS_DENIED, accessToken, false, 'Insufficient security clearance');
        this._cacheMetrics.securityViolations++;
        throw new Error('Insufficient security clearance for cache entry');
      }

      // Update access metadata
      entry.lastAccessed = new Date();
      entry.accessCount = (entry.accessCount ?? 0) + 1;
      entry.accessTokens.add(accessToken ?? 'anonymous');

      // Decrypt if necessary
      let value = entry.value;
      if (entry.encrypted) {
        value = await this._decryptCacheValue(entry.value, key);
        this._cacheMetrics.encryptionOperations = (this._cacheMetrics.encryptionOperations ?? 0) + 1;
      }

      // Validate checksum
      const expectedChecksum = this._calculateChecksum(value);
      if (entry.checksum !== expectedChecksum) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.GET, accessToken, false, 'Checksum validation failed');
        this._cacheMetrics.securityViolations = (this._cacheMetrics.securityViolations ?? 0) + 1;
        throw new Error('Cache integrity validation failed');
      }

      // Update metrics
      this._cacheMetrics.hitCount = (this._cacheMetrics.hitCount ?? 0) + 1;
      this._cacheMetrics.averageAccessTime = ((this._cacheMetrics.averageAccessTime ?? 0) + (Date.now() - startTime)) / 2;

      await this._auditCacheOperation(key, CACHE_OPERATIONS.GET, accessToken, true);
      
      this.logOperation('get', 'success', { key, encrypted: entry.encrypted });
      return value;

    } catch (error) {
      this.logError('get', error);
      throw error;
    }
  }

  /**
   * Set cached rule with security features
   */
  public async set(
    key: string, 
    value: any, 
    options?: { 
      ttl?: number; 
      securityLevel?: string;
      accessToken?: string;
      encrypt?: boolean;
    }
  ): Promise<void> {
    try {
      this.validateInitialized();
      this.validateCacheKey(key);

      const accessToken = options?.accessToken;
      
      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, 'write');
      if (!tokenValidation.valid) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        this._cacheMetrics.securityViolations = (this._cacheMetrics.securityViolations ?? 0) + 1;
        throw new Error(`Cache access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      // Check memory limits
      const estimatedSize = this._estimateValueSize(value);
      if (this._currentMemoryUsage + estimatedSize > this._cacheConfig.memoryThreshold) {
        await this._performMemoryCleanup();
        
        // Check again after cleanup
        if (this._currentMemoryUsage + estimatedSize > this._cacheConfig.memoryThreshold) {
          throw new Error('Cache memory threshold exceeded');
        }
      }

      // Check cache size limits
      if (this._cache.size >= this._cacheConfig.maxSize) {
        await this._evictLeastRecentlyUsed();
      }

      const securityLevel = (options?.securityLevel as keyof typeof SECURITY_LEVELS) ?? SECURITY_LEVELS.INTERNAL;
      const shouldEncrypt = options?.encrypt ?? this._cacheConfig.enableEncryption;
      const ttl = options?.ttl ?? this._cacheConfig.defaultTTL;

      // Prepare cache entry
      let cacheValue = value;
      let encrypted = false;

      if (shouldEncrypt && this._shouldEncryptForSecurityLevel(securityLevel)) {
        cacheValue = await this._encryptCacheValue(value, key);
        encrypted = true;
        this._cacheMetrics.encryptionOperations = (this._cacheMetrics.encryptionOperations ?? 0) + 1;
      }

      const checksum = this._calculateChecksum(value);
      const expiresAt = new Date(Date.now() + ttl);

      const entry: ICacheEntry = {
        key,
        value: cacheValue,
        encrypted,
        securityLevel,
        accessCount: 0,
        createdAt: new Date(),
        lastAccessed: new Date(),
        expiresAt,
        checksum,
        accessTokens: new Set([accessToken || 'anonymous']),
        auditTrail: []
      };

      // Store entry
      this._cache.set(key, entry);
      this._currentMemoryUsage += estimatedSize;
      this._cacheMetrics.totalEntries = this._cache.size;

      await this._auditCacheOperation(key, CACHE_OPERATIONS.SET, accessToken, true, undefined, {
        securityLevel,
        encrypted,
        ttl
      });

      this.logOperation('set', 'success', { 
        key, 
        securityLevel, 
        encrypted, 
        size: estimatedSize 
      });

    } catch (error) {
      this.logError('set', error);
      throw error;
    }
  }

  /**
   * Delete cached rule
   */
  public async delete(key: string, accessToken?: string): Promise<boolean> {
    try {
      this.validateInitialized();
      this.validateCacheKey(key);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, 'delete');
      if (!tokenValidation.valid) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        this._cacheMetrics.securityViolations = (this._cacheMetrics.securityViolations ?? 0) + 1;
        throw new Error(`Cache access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      const entry = this._cache.get(key);
      if (!entry) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.DELETE, accessToken, false, 'Entry not found');
        return false;
      }

      // Validate security level access
      if (!this._canAccessSecurityLevel(tokenValidation.token!, entry.securityLevel)) {
        await this._auditCacheOperation(key, CACHE_OPERATIONS.ACCESS_DENIED, accessToken, false, 'Insufficient security clearance');
        this._cacheMetrics.securityViolations++;
        throw new Error('Insufficient security clearance for cache entry');
      }

      // Remove entry
      this._cache.delete(key);
      this._currentMemoryUsage -= this._estimateValueSize(entry.value);
      this._cacheMetrics.totalEntries = this._cache.size;

      await this._auditCacheOperation(key, CACHE_OPERATIONS.DELETE, accessToken, true);

      this.logOperation('delete', 'success', { key });
      return true;

    } catch (error) {
      this.logError('delete', error);
      throw error;
    }
  }

  /**
   * Clear all cached rules
   */
  public async clear(accessToken?: string): Promise<void> {
    try {
      this.validateInitialized();

      // Validate access token with admin permissions
      const tokenValidation = await this._validateAccessToken(accessToken, 'admin');
      if (!tokenValidation.valid) {
        await this._auditCacheOperation('*', CACHE_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        this._cacheMetrics.securityViolations = (this._cacheMetrics.securityViolations ?? 0) + 1;
        throw new Error(`Cache access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      const entriesCount = this._cache.size;
      
      // Clear cache
      this._cache.clear();
      this._currentMemoryUsage = 0;
      this._cacheMetrics.totalEntries = 0;
      this._cacheMetrics.evictionCount += entriesCount;

      await this._auditCacheOperation('*', CACHE_OPERATIONS.CLEAR, accessToken, true, undefined, {
        entriesCleared: entriesCount
      });

      this.logOperation('clear', 'success', { entriesCleared: entriesCount });

    } catch (error) {
      this.logError('clear', error);
      throw error;
    }
  }

  /**
   * Get cache statistics
   */
  public async getStatistics(): Promise<TCacheStatistics> {
    try {
      this.validateInitialized();

      // Update real-time metrics
      this._cacheMetrics.memoryUsage = this._currentMemoryUsage;
      this._cacheMetrics.cacheEfficiency = this._calculateCacheEfficiency();

      // Convert ICacheMetrics to TCacheStatistics
      const statistics: TCacheStatistics = {
        totalEntries: this._cacheMetrics.totalEntries,
        hitCount: this._cacheMetrics.hitCount,
        missCount: this._cacheMetrics.missCount,
        evictionCount: this._cacheMetrics.evictionCount,
        memoryUsage: this._cacheMetrics.memoryUsage,
        averageAccessTime: this._cacheMetrics.averageAccessTime,
        encryptionOperations: this._cacheMetrics.encryptionOperations,
        securityViolations: this._cacheMetrics.securityViolations,
        lastCleanup: this._cacheMetrics.lastCleanup.toISOString(),
        cacheEfficiency: this._cacheMetrics.cacheEfficiency
      };

      return statistics;

    } catch (error) {
      this.logError('getStatistics', error);
      throw error;
    }
  }

  // ============================================================================
  // IPERFORMANCESERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Get performance metrics
   */
  public async getPerformanceMetrics(): Promise<TRulePerformanceMetrics> {
    try {
      this.validateInitialized();

      // Update performance calculations
      this._performanceMetrics.cacheHitRatio = this._calculateCacheHitRatio();
      this._performanceMetrics.peakMemoryUsage = Math.max(
        this._performanceMetrics.peakMemoryUsage,
        this._currentMemoryUsage
      );

      // Convert IPerformanceMetrics to TRulePerformanceMetrics
      const metrics: TRulePerformanceMetrics = {
        totalExecutions: this._performanceMetrics.totalExecutions,
        averageExecutionTime: this._performanceMetrics.averageExecutionTime,
        successRate: this._performanceMetrics.successRate,
        errorRate: this._performanceMetrics.errorRate,
        throughput: this._performanceMetrics.throughput,
        peakMemoryUsage: this._performanceMetrics.peakMemoryUsage,
        cacheHitRatio: this._performanceMetrics.cacheHitRatio,
        optimizationSavings: this._performanceMetrics.optimizationSavings
      };

      return metrics;

    } catch (error) {
      this.logError('getPerformanceMetrics', error);
      throw error;
    }
  }

  /**
   * Optimize cache performance
   */
  public async optimize(): Promise<void> {
    try {
      this.validateInitialized();

      this.logOperation('optimize', 'start');

      // Perform memory optimization
      await this._performMemoryCleanup();

      // Optimize encryption keys
      await this._optimizeEncryptionKeys();

      // Update performance metrics
      await this._updatePerformanceMetrics();

      this.logOperation('optimize', 'complete');

    } catch (error) {
      this.logError('optimize', error);
      throw error;
    }
  }

  // ============================================================================
  // SECURITY AND ENCRYPTION METHODS
  // ============================================================================

  /**
   * Encrypt cache value using Node.js crypto API
   */
  private async _encryptCacheValue(value: any, key: string): Promise<string> {
    try {
      const serialized = JSON.stringify(value);
      const iv = crypto.randomBytes(CACHE_SECURITY_CONFIG.IV_LENGTH);
      
      // Derive encryption key
      const encryptionKey = crypto.pbkdf2Sync(
        this._masterKey,
        Buffer.concat([this._securitySalt, Buffer.from(key)]),
        CACHE_SECURITY_CONFIG.KEY_DERIVATION_ITERATIONS,
        32,
        'sha256'
      );

      const cipher = crypto.createCipher('aes-256-gcm', encryptionKey);
      cipher.setAAD(Buffer.from(key)); // Additional authenticated data
      
      let encrypted = cipher.update(serialized, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      // Store encryption key for decryption
      this._encryptionKeys.set(key, encryptionKey);
      
      return JSON.stringify({
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      });

    } catch (error) {
      this.logError('_encryptCacheValue', error);
      throw new Error('Cache encryption failed');
    }
  }

  /**
   * Decrypt cache value using Node.js crypto API
   */
  private async _decryptCacheValue(encryptedData: string, key: string): Promise<any> {
    try {
      const { encrypted, iv, tag } = JSON.parse(encryptedData);
      
      const encryptionKey = this._encryptionKeys.get(key);
      if (!encryptionKey) {
        throw new Error('Encryption key not found');
      }

      const decipher = crypto.createDecipher('aes-256-gcm', encryptionKey);
      decipher.setAAD(Buffer.from(key)); // Additional authenticated data
      decipher.setAuthTag(Buffer.from(tag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return JSON.parse(decrypted);

    } catch (error) {
      this.logError('_decryptCacheValue', error);
      throw new Error('Cache decryption failed');
    }
  }

  // ============================================================================
  // ACCESS CONTROL AND VALIDATION METHODS
  // ============================================================================

  /**
   * Validate access token
   */
  private async _validateAccessToken(
    accessToken?: string, 
    operation?: string
  ): Promise<{ valid: boolean; token?: ICacheAccessToken; error?: string }> {
    try {
      if (!accessToken) {
        // Allow anonymous access for public operations
        return { 
          valid: true, 
          token: {
            token: 'anonymous',
            permissions: ['read'],
            expiresAt: new Date(Date.now() + 3600000),
            issuedFor: 'anonymous',
            securityLevel: SECURITY_LEVELS.PUBLIC
          }
        };
      }

      const token = this._accessTokens.get(accessToken);
      if (!token) {
        return { valid: false, error: 'Invalid access token' };
      }

      if (token.expiresAt < new Date()) {
        this._accessTokens.delete(accessToken);
        return { valid: false, error: 'Access token expired' };
      }

      // Enhanced optional chaining with nullish coalescing for operation check
      if (operation && !token.permissions?.includes(operation) && !token.permissions?.includes('admin')) {
        return { valid: false, error: `Insufficient permissions for operation: ${operation}` };
      }

      return { valid: true, token };

    } catch (error) {
      this.logError('_validateAccessToken', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  /**
   * Check if token can access security level
   */
  private _canAccessSecurityLevel(
    token: ICacheAccessToken, 
    requiredLevel: keyof typeof SECURITY_LEVELS
  ): boolean {
    const levels = [
      SECURITY_LEVELS.PUBLIC,
      SECURITY_LEVELS.INTERNAL,
      SECURITY_LEVELS.CONFIDENTIAL,
      SECURITY_LEVELS.RESTRICTED
    ];

    const tokenLevelIndex = levels.indexOf(token.securityLevel);
    const requiredLevelIndex = levels.indexOf(requiredLevel);

    return tokenLevelIndex >= requiredLevelIndex;
  }

  /**
   * Generate system access tokens
   */
  private async _generateSystemAccessTokens(): Promise<void> {
    try {
      // Generate admin token
      const adminToken: ICacheAccessToken = {
        token: crypto.randomBytes(32).toString('hex'),
        permissions: ['admin', 'read', 'write', 'delete'],
        expiresAt: new Date(Date.now() + CACHE_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY),
        issuedFor: 'system-admin',
        securityLevel: SECURITY_LEVELS.RESTRICTED
      };

      this._accessTokens.set(adminToken.token, adminToken);

      // Generate read-only token
      const readToken: ICacheAccessToken = {
        token: crypto.randomBytes(32).toString('hex'),
        permissions: ['read'],
        expiresAt: new Date(Date.now() + CACHE_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY),
        issuedFor: 'system-reader',
        securityLevel: SECURITY_LEVELS.INTERNAL
      };

      this._accessTokens.set(readToken.token, readToken);

      this.logOperation('_generateSystemAccessTokens', 'complete', {
        tokensGenerated: 2
      });

    } catch (error) {
      this.logError('_generateSystemAccessTokens', error);
      throw error;
    }
  }

  // ============================================================================
  // UTILITY AND HELPER METHODS
  // ============================================================================

  /**
   * Validate cache key
   */
  private validateCacheKey(key: string): void {
    if (!key || typeof key !== 'string') {
      throw new Error('Invalid cache key: must be a non-empty string');
    }

    if (key.length > 256) {
      throw new Error('Invalid cache key: maximum length is 256 characters');
    }

    // Security check for key injection
    if (key.includes('\0') || key.includes('\n') || key.includes('\r')) {
      throw new Error('Invalid cache key: contains illegal characters');
    }
  }

  /**
   * Validate service is initialized
   */
  private validateInitialized(): void {
    if (!this._initialized) {
      throw new Error('Cache manager not initialized');
    }
  }

  /**
   * Calculate checksum for integrity validation
   */
  private _calculateChecksum(value: any): string {
    const serialized = JSON.stringify(value);
    return crypto.createHash('sha256').update(serialized).digest('hex');
  }

  /**
   * Estimate value size in bytes
   */
  private _estimateValueSize(value: any): number {
    const serialized = JSON.stringify(value);
    return Buffer.byteLength(serialized, 'utf8');
  }

  /**
   * Check if value should be encrypted for security level
   */
  private _shouldEncryptForSecurityLevel(securityLevel: keyof typeof SECURITY_LEVELS): boolean {
    return securityLevel === SECURITY_LEVELS.CONFIDENTIAL || 
           securityLevel === SECURITY_LEVELS.RESTRICTED;
  }

  /**
   * Calculate cache efficiency
   */
  private _calculateCacheEfficiency(): number {
    const hitCount = this._cacheMetrics.hitCount ?? 0;
    const missCount = this._cacheMetrics.missCount ?? 0;
    const totalRequests = hitCount + missCount;
    return totalRequests > 0 ? (hitCount / totalRequests) * 100 : 0;
  }

  /**
   * Calculate cache hit ratio
   */
  private _calculateCacheHitRatio(): number {
    const hitCount = this._cacheMetrics.hitCount ?? 0;
    const missCount = this._cacheMetrics.missCount ?? 0;
    const totalRequests = hitCount + missCount;
    return totalRequests > 0 ? hitCount / totalRequests : 0;
  }

  // ============================================================================
  // MAINTENANCE AND CLEANUP METHODS
  // ============================================================================

  /**
   * Perform cache cleanup
   */
  private async _performCleanup(): Promise<void> {
    try {
      this.logOperation('_performCleanup', 'start');

      const now = new Date();
      let expiredCount = 0;

      // Replace for...of with Array.from
      Array.from(this._cache).forEach(([key, entry]) => {
        if (entry.expiresAt < now) {
          this._cache.delete(key);
          this._currentMemoryUsage -= this._estimateValueSize(entry.value);
          expiredCount++;
        }
      });

      // Clean up expired access tokens
      let expiredTokens = 0;
      Array.from(this._accessTokens).forEach(([token, tokenData]) => {
        if (tokenData.expiresAt < now) {
          this._accessTokens.delete(token);
          expiredTokens++;
        }
      });

      // Update metrics
      const currentEvictionCount = this._cacheMetrics.evictionCount ?? 0;
      this._cacheMetrics.evictionCount = currentEvictionCount + expiredCount;
      this._cacheMetrics.totalEntries = this._cache.size;
      this._cacheMetrics.lastCleanup = now;

      this.logOperation('_performCleanup', 'complete', {
        expiredEntries: expiredCount,
        expiredTokens,
        remainingEntries: this._cache.size
      });

    } catch (error) {
      this.logError('_performCleanup', error);
    }
  }

  /**
   * Perform memory cleanup
   */
  private async _performMemoryCleanup(): Promise<void> {
    try {
      if (this._currentMemoryUsage <= this._cacheConfig.memoryThreshold * 0.8) {
        return; // No cleanup needed
      }

      this.logOperation('_performMemoryCleanup', 'start');

      // Sort entries by access patterns (LRU)
      const entries = Array.from(this._cache)
        .sort((a, b) => a[1].lastAccessed.getTime() - b[1].lastAccessed.getTime());

      let removedCount = 0;
      const targetMemory = this._cacheConfig.memoryThreshold * 0.7; // Target 70% of threshold

      entries.forEach(([key, entry]) => {
        if (this._currentMemoryUsage > targetMemory) {
          this._cache.delete(key);
          this._currentMemoryUsage -= this._estimateValueSize(entry.value);
          this._encryptionKeys.delete(key);
          removedCount++;
        }
      });

      const currentEvictionCount = this._cacheMetrics.evictionCount ?? 0;
      this._cacheMetrics.evictionCount = currentEvictionCount + removedCount;
      this._cacheMetrics.totalEntries = this._cache.size;

      this.logOperation('_performMemoryCleanup', 'complete', {
        removedEntries: removedCount,
        currentMemory: this._currentMemoryUsage,
        targetMemory
      });

    } catch (error) {
      this.logError('_performMemoryCleanup', error);
    }
  }

  /**
   * Evict least recently used entry
   */
  private async _evictLeastRecentlyUsed(): Promise<void> {
    try {
      let oldestKey: string | null = null;
      let oldestTime = Date.now();

      // Use Map's forEach method directly
      this._cache.forEach((entry, key) => {
        if (entry.lastAccessed.getTime() < oldestTime) {
          oldestTime = entry.lastAccessed.getTime();
          oldestKey = key;
        }
      });

      if (oldestKey) {
        const entry = this._cache.get(oldestKey)!;
        this._cache.delete(oldestKey);
        this._currentMemoryUsage -= this._estimateValueSize(entry.value);
        this._encryptionKeys.delete(oldestKey);
        
                 const currentEvictionCount = this._cacheMetrics.evictionCount ?? 0;
         this._cacheMetrics.evictionCount = currentEvictionCount + 1;
        this._cacheMetrics.totalEntries = this._cache.size;

        this.logOperation('_evictLeastRecentlyUsed', 'success', { key: oldestKey });
      }

    } catch (error) {
      this.logError('_evictLeastRecentlyUsed', error);
    }
  }

  /**
   * Optimize encryption keys
   */
  private async _optimizeEncryptionKeys(): Promise<void> {
    try {
      // Remove encryption keys for non-existent cache entries
      const cacheKeys = new Set(this._cache.keys());
      let removedKeys = 0;

      // Replace for...of with Array.from
      Array.from(this._encryptionKeys.keys()).forEach(key => {
        if (!cacheKeys.has(key)) {
          this._encryptionKeys.delete(key);
          removedKeys++;
        }
      });

      this.logOperation('_optimizeEncryptionKeys', 'complete', {
        removedKeys,
        remainingKeys: this._encryptionKeys.size
      });

    } catch (error) {
      this.logError('_optimizeEncryptionKeys', error);
    }
  }

  /**
   * Update cache metrics - Renamed to avoid conflict with BaseTrackingService
   */
  private async _updateCacheMetrics(): Promise<void> {
    try {
      this._cacheMetrics.memoryUsage = this._currentMemoryUsage;
      this._cacheMetrics.cacheEfficiency = this._calculateCacheEfficiency();
      
      // Update performance metrics
      this._performanceMetrics.cacheHitRatio = this._calculateCacheHitRatio();
      this._performanceMetrics.peakMemoryUsage = Math.max(
        this._performanceMetrics.peakMemoryUsage,
        this._currentMemoryUsage
      );

    } catch (error) {
      this.logError('_updateCacheMetrics', error);
    }
  }

  /**
   * Update performance metrics
   */
  private async _updatePerformanceMetrics(): Promise<void> {
    try {
      const hitCount = this._cacheMetrics.hitCount ?? 0;
      const missCount = this._cacheMetrics.missCount ?? 0;
      const totalRequests = hitCount + missCount;
      
      this._performanceMetrics.totalExecutions = totalRequests;
      this._performanceMetrics.successRate = totalRequests > 0 ? 
        (hitCount / totalRequests) * 100 : 0;
      this._performanceMetrics.errorRate = 100 - this._performanceMetrics.successRate;
      this._performanceMetrics.cacheHitRatio = this._calculateCacheHitRatio();

    } catch (error) {
      this.logError('_updatePerformanceMetrics', error);
    }
  }

  /**
   * Audit cache operation
   */
  private async _auditCacheOperation(
    key: string,
    operation: string,
    accessToken?: string,
    success: boolean = true,
    errorMessage?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      if (!this._cacheConfig.enableAuditTrail) {
        return;
      }

      const auditEntry: ICacheAuditEntry = {
        operation,
        timestamp: new Date(),
        accessToken: accessToken ?? 'anonymous',
        success,
        errorMessage,
        metadata: metadata ?? {}
      };

      // Add to entry audit trail if entry exists
      const entry = this._cache.get(key);
      if (entry) {
        entry.auditTrail.push(auditEntry);
        
        // Keep only recent audit entries
        if (entry.auditTrail.length > 100) {
          entry.auditTrail = entry.auditTrail.slice(-50);
        }
      }

      // Log audit event
      this.logOperation('audit', operation, {
        key,
        success,
        accessToken: accessToken || 'anonymous',
        errorMessage,
        metadata
      });

    } catch (error) {
      this.logError('_auditCacheOperation', error);
    }
  }
}