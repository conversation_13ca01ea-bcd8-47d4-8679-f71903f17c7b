/**
 * @file Rule Performance Profiler
 * @filepath server/src/platform/governance/performance-management/analytics/RulePerformanceProfiler.ts
 * @task-id G-TSK-03.SUB-03.1.IMP-04
 * @component governance-rule-performance-profiler
 * @reference foundation-context.GOVERNANCE.PERFORMANCE.004
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier G1
 * @context foundation-context
 * @category Governance Performance
 * @created 2025-06-28
 * @modified 2025-06-28
 *
 * @description
 * Enterprise Rule Performance Profiler providing:
 * - High-precision rule execution profiling (timing, memory, CPU)
 * - Security-first profiling data access and audit trails
 * - Configurable profiling policies and thresholds
 * - Integration with governance and tracking systems
 * - Enterprise-grade error handling and monitoring
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/interfaces/tracking/core-interfaces
 * @depends-on shared/src/types/platform/tracking/tracking-types
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService
 * @enables server/src/platform/governance/performance-management
 * @related-contexts foundation-context
 * @governance-impact framework-foundation, governance-performance
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-performance-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @security-level high
 * @documentation docs/contexts/foundation-context/governance/performance/rule-performance-profiler.md
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   security-enhanced: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-06-28) - Initial implementation with enterprise-grade profiling and security features
 */

import { BaseTrackingService } from '../../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import { IPerformanceService } from '../../../../../../shared/src/interfaces/tracking/core-interfaces';
import {
  TValidationResult,
  TTrackingData,
  TTrackingConfig
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';
import * as crypto from 'crypto';

// ============================================================================
// SECURITY CONSTANTS AND CONFIGURATION
// ============================================================================

/**
 * Security configuration for performance profiler
 */
const PROFILER_SECURITY_CONFIG = {
  MAX_SESSIONS: 1000,
  SESSION_TIMEOUT: 300000, // 5 minutes
  ACCESS_TOKEN_EXPIRY: 3600000, // 1 hour
  AUDIT_RETENTION_DAYS: 90,
  MAX_AUDIT_ENTRIES: 100
} as const;

/**
 * Profiler operation types for audit trails
 */
const PROFILER_OPERATIONS = {
  START_PROFILING: 'start_profiling',
  END_PROFILING: 'end_profiling',
  GET_RESULT: 'get_result',
  LIST_SESSIONS: 'list_sessions',
  ACCESS_DENIED: 'access_denied',
  CLEANUP: 'cleanup'
} as const;

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

/**
 * Profiling result data
 */
interface IProfilingResult {
  sessionId: string;
  ruleId: string;
  startedAt: Date;
  endedAt: Date;
  durationMs: number;
  cpuUsage?: number;
  memoryUsage?: number;
  error?: string;
  metadata: Record<string, any>;
}

/**
 * Profiling configuration
 */
interface IProfilingConfig {
  enableAuditTrail: boolean;
  maxSessions: number;
  sessionTimeout: number;
  securityLevel: string;
  samplingRate: number;
  enableCpuProfiling: boolean;
  enableMemoryProfiling: boolean;
}

/**
 * Profiling session data
 */
interface IProfilingSession {
  sessionId: string;
  ruleId: string;
  startedAt: Date;
  endedAt?: Date;
  durationMs?: number;
  cpuUsage?: number;
  memoryUsage?: number;
  error?: string;
  accessToken: string;
  metadata: Record<string, any>;
  auditTrail: IProfilerAuditEntry[];
}

/**
 * Profiler audit entry
 */
interface IProfilerAuditEntry {
  operation: string;
  timestamp: Date;
  accessToken: string;
  success: boolean;
  errorMessage?: string;
  metadata: Record<string, any>;
}

/**
 * Access token data
 */
interface IAccessToken {
  token: string;
  permissions: string[];
  expiresAt: Date;
  issuedFor: string;
  securityLevel: string;
}

// ============================================================================
// MAIN IMPLEMENTATION
// ============================================================================

/**
 * Enterprise Rule Performance Profiler
 *
 * Provides high-precision profiling for rule execution with security-first access control,
 * audit trails, and configurable profiling policies.
 */
export class RulePerformanceProfiler extends BaseTrackingService implements IPerformanceService {
  // ============================================================================
  // PRIVATE PROPERTIES
  // ============================================================================

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-performance-profiler';

  // Profiling session management
  private readonly _sessions = new Map<string, IProfilingSession>();
  private readonly _accessTokens = new Map<string, IAccessToken>();
  private _profilerConfig: IProfilingConfig;

  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
  private _initialized = false;

  // ============================================================================
  // CONSTRUCTOR
  // ============================================================================

  constructor(config?: Partial<IProfilingConfig>) {
    // Initialize with default TTrackingConfig structure
    const defaultTrackingConfig: TTrackingConfig = {
      service: {
        name: 'governance-rule-performance-profiler',
        version: '1.0.0',
        environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',
        timeout: 30000,
        retry: {
          maxAttempts: 3,
          delay: 1000,
          backoffMultiplier: 2,
          maxDelay: 5000
        }
      },
      governance: {
        authority: 'President & CEO, E.Z. Consultancy',
        requiredCompliance: ['authority-validation', 'security-policy'],
        auditFrequency: 24,
        violationReporting: true
      },
      performance: {
        metricsEnabled: true,
        metricsInterval: 60000,
        monitoringEnabled: true,
        alertThresholds: {
          cpuUsage: 80,
          memoryUsage: 70,
          responseTime: 5000,
          errorRate: 5
        }
      },
      logging: {
        level: 'info',
        format: 'json',
        rotation: true,
        maxFileSize: 100
      }
    };

    super(defaultTrackingConfig);

    // Initialize profiler configuration
    this._profilerConfig = {
      enableAuditTrail: config?.enableAuditTrail ?? true,
      maxSessions: config?.maxSessions ?? PROFILER_SECURITY_CONFIG.MAX_SESSIONS,
      sessionTimeout: config?.sessionTimeout ?? PROFILER_SECURITY_CONFIG.SESSION_TIMEOUT,
      securityLevel: config?.securityLevel ?? 'internal',
      samplingRate: config?.samplingRate ?? 1.0,
      enableCpuProfiling: config?.enableCpuProfiling ?? true,
      enableMemoryProfiling: config?.enableMemoryProfiling ?? true
    };
  }

  // ============================================================================
  // ABSTRACT METHOD IMPLEMENTATIONS FROM BASETRACKINGSERVICE
  // ============================================================================

  /**
   * Get service name
   */
  protected getServiceName(): string {
    return this._componentType;
  }

  /**
   * Get service version
   */
  protected getServiceVersion(): string {
    return this._version;
  }

  /**
   * Perform service-specific initialization
   */
  protected async doInitialize(): Promise<void> {
    try {
      await super.doInitialize();
      this.logOperation('doInitialize', 'start');

      // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
      const timerCoordinator = getTimerCoordinator();
      timerCoordinator.createCoordinatedInterval(
        () => this._performCleanup(),
        this._profilerConfig.sessionTimeout,
        'RulePerformanceProfiler',
        'session-cleanup'
      );

      // Generate initial access tokens
      await this._generateSystemAccessTokens();

      this._initialized = true;
      this.logOperation('doInitialize', 'complete');

    } catch (error) {
      this.logError('doInitialize', error);
      throw error;
    }
  }

  /**
   * Perform service-specific tracking
   */
  protected async doTrack(data: TTrackingData): Promise<void> {
    try {
      this.logOperation('doTrack', 'start', { componentId: data.componentId });

      // Track profiling operations
      if (data.metadata && typeof data.metadata === 'object') {
        const metadata = data.metadata as Record<string, unknown>;
        if (metadata.profilingOperation) {
          await this._auditOperation(
            (metadata.sessionId as string) ?? 'unknown',
            metadata.profilingOperation as string,
            (metadata.accessToken as string) ?? 'anonymous',
            (metadata.success as boolean) ?? false,
            metadata.errorMessage as string,
            metadata
          );
        }
      }

      this.logOperation('doTrack', 'complete');

    } catch (error) {
      this.logError('doTrack', error);
      throw error;
    }
  }

  /**
   * Perform service-specific validation
   */
  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: string[] = [];
      const warnings: string[] = [];

      // Validate profiler health
      if (!this._initialized) {
        errors.push('Profiler not initialized');
      }

      // Validate session limits
      if (this._sessions.size > this._profilerConfig.maxSessions) {
        errors.push(`Session count exceeds limit: ${this._sessions.size} > ${this._profilerConfig.maxSessions}`);
      }

      // Validate configuration
      if (this._profilerConfig.samplingRate < 0 || this._profilerConfig.samplingRate > 1) {
        warnings.push(`Invalid sampling rate: ${this._profilerConfig.samplingRate}`);
      }

      const result: TValidationResult = {
        validationId: `profiler-val-${Date.now()}`,
        componentId: this._componentType,
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(),
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 20) - (warnings.length * 10)),
        checks: [],
        references: {
          componentId: this._componentType,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings,
        warnings,
        errors,
        metadata: {
          validationMethod: 'profiler-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', { 
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  /**
   * Perform service-specific shutdown
   */
  protected async doShutdown(): Promise<void> {
    try {
      this.logOperation('doShutdown', 'start');

      // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

      // Clear sensitive data
      this._sessions.clear();
      this._accessTokens.clear();

      this._initialized = false;
      this.logOperation('doShutdown', 'complete');
      await super.doShutdown();

    } catch (error) {
      this.logError('doShutdown', error);
      throw error;
    }
  }

  // ============================================================================
  // IPERFORMANCESERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Get performance metrics
   */
  public async getPerformanceMetrics(): Promise<any> {
    try {
      this.validateInitialized();

      const metrics = {
        activeSessions: this._sessions.size,
        totalSessions: this._sessions.size,
        averageSessionDuration: this._calculateAverageSessionDuration(),
        successRate: this._calculateSuccessRate(),
        timestamp: new Date()
      };

      return metrics;

    } catch (error) {
      this.logError('getPerformanceMetrics', error);
      throw error;
    }
  }

  /**
   * Optimize performance
   */
  public async optimize(): Promise<void> {
    try {
      this.validateInitialized();

      this.logOperation('optimize', 'start');

      // Perform cleanup and optimization
      await this._performCleanup();
      await this._optimizeSessions();

      this.logOperation('optimize', 'complete');

    } catch (error) {
      this.logError('optimize', error);
      throw error;
    }
  }

  // ============================================================================
  // PUBLIC PROFILER INTERFACE
  // ============================================================================

  /**
   * Start a profiling session for a rule
   */
  public async startProfiling(ruleId: string, accessToken?: string, metadata?: Record<string, any>): Promise<string> {
    try {
      this.validateInitialized();
      this.validateRuleId(ruleId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, 'profile');
      if (!tokenValidation.valid) {
        await this._auditOperation('', PROFILER_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      // Check session limits
      if (this._sessions.size >= this._profilerConfig.maxSessions) {
        await this._performCleanup(); // Try cleanup first
        
        if (this._sessions.size >= this._profilerConfig.maxSessions) {
          throw new Error('Maximum profiling sessions exceeded');
        }
      }

      const sessionId = crypto.randomUUID();
      const session: IProfilingSession = {
        sessionId,
        ruleId,
        startedAt: new Date(),
        accessToken: accessToken ?? 'anonymous',
        metadata: metadata ?? {},
        auditTrail: []
      };

      this._sessions.set(sessionId, session);

      await this._auditOperation(sessionId, PROFILER_OPERATIONS.START_PROFILING, accessToken, true, undefined, {
        ruleId,
        metadata
      });

      this.logOperation('startProfiling', 'success', { sessionId, ruleId });
      return sessionId;

    } catch (error) {
      this.logError('startProfiling', error);
      throw error;
    }
  }

  /**
   * End a profiling session and record results
   */
  public async endProfiling(sessionId: string, result: Partial<IProfilingResult>, accessToken?: string): Promise<IProfilingResult> {
    try {
      this.validateInitialized();
      this.validateSessionId(sessionId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, 'profile');
      if (!tokenValidation.valid) {
        await this._auditOperation(sessionId, PROFILER_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      const session = this._sessions.get(sessionId);
      if (!session) {
        throw new Error('Profiling session not found');
      }

      // Validate access to session
      if (session.accessToken !== (accessToken ?? 'anonymous') && !tokenValidation.token?.permissions?.includes('admin')) {
        await this._auditOperation(sessionId, PROFILER_OPERATIONS.ACCESS_DENIED, accessToken, false, 'Insufficient permissions');
        throw new Error('Access denied for this profiling session');
      }

      // Update session with results
      session.endedAt = new Date();
      session.durationMs = result.durationMs ?? (session.endedAt.getTime() - session.startedAt.getTime());
      session.cpuUsage = result.cpuUsage;
      session.memoryUsage = result.memoryUsage;
      session.error = result.error;

      const profilingResult: IProfilingResult = {
        sessionId,
        ruleId: session.ruleId,
        startedAt: session.startedAt,
        endedAt: session.endedAt,
        durationMs: session.durationMs,
        cpuUsage: session.cpuUsage,
        memoryUsage: session.memoryUsage,
        error: session.error,
        metadata: session.metadata
      };

      await this._auditOperation(sessionId, PROFILER_OPERATIONS.END_PROFILING, accessToken, true, undefined, result);

      this.logOperation('endProfiling', 'success', { sessionId });
      return profilingResult;

    } catch (error) {
      this.logError('endProfiling', error);
      throw error;
    }
  }

  /**
   * Get profiling results for a session
   */
  public async getProfilingResult(sessionId: string, accessToken?: string): Promise<IProfilingResult | null> {
    try {
      this.validateInitialized();
      this.validateSessionId(sessionId);

      // Validate access token
      const tokenValidation = await this._validateAccessToken(accessToken, 'read');
      if (!tokenValidation.valid) {
        await this._auditOperation(sessionId, PROFILER_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      const session = this._sessions.get(sessionId);
      if (!session) {
        return null;
      }

      // Validate access to session
      if (session.accessToken !== (accessToken ?? 'anonymous') && !tokenValidation.token?.permissions?.includes('admin')) {
        await this._auditOperation(sessionId, PROFILER_OPERATIONS.ACCESS_DENIED, accessToken, false, 'Insufficient permissions');
        throw new Error('Access denied for this profiling session');
      }

      if (!session.endedAt) {
        throw new Error('Profiling session not ended');
      }

      await this._auditOperation(sessionId, PROFILER_OPERATIONS.GET_RESULT, accessToken, true);

      return {
        sessionId,
        ruleId: session.ruleId,
        startedAt: session.startedAt,
        endedAt: session.endedAt,
        durationMs: session.durationMs!,
        cpuUsage: session.cpuUsage,
        memoryUsage: session.memoryUsage,
        error: session.error,
        metadata: session.metadata
      };

    } catch (error) {
      this.logError('getProfilingResult', error);
      throw error;
    }
  }

  /**
   * List all profiling sessions (admin only)
   */
  public async listSessions(accessToken?: string): Promise<IProfilingSession[]> {
    try {
      this.validateInitialized();

      // Validate access token with admin permissions
      const tokenValidation = await this._validateAccessToken(accessToken, 'admin');
      if (!tokenValidation.valid) {
        await this._auditOperation('*', PROFILER_OPERATIONS.ACCESS_DENIED, accessToken, false, tokenValidation.error);
        throw new Error(`Access denied: ${tokenValidation.error ?? 'Unknown error'}`);
      }

      await this._auditOperation('*', PROFILER_OPERATIONS.LIST_SESSIONS, accessToken, true);

      return Array.from(this._sessions.values());

    } catch (error) {
      this.logError('listSessions', error);
      throw error;
    }
  }

  // ============================================================================
  // SECURITY AND VALIDATION METHODS
  // ============================================================================

  /**
   * Validate access token
   */
  private async _validateAccessToken(
    accessToken?: string, 
    operation?: string
  ): Promise<{ valid: boolean; token?: IAccessToken; error?: string }> {
    try {
      if (!accessToken) {
        // Allow anonymous access for read operations
        if (operation === 'read') {
          return { 
            valid: true, 
            token: {
              token: 'anonymous',
              permissions: ['read'],
              expiresAt: new Date(Date.now() + 3600000),
              issuedFor: 'anonymous',
              securityLevel: 'public'
            }
          };
        }
        return { valid: false, error: 'Access token required for this operation' };
      }

      const token = this._accessTokens.get(accessToken);
      if (!token) {
        return { valid: false, error: 'Invalid access token' };
      }

      if (token.expiresAt < new Date()) {
        this._accessTokens.delete(accessToken);
        return { valid: false, error: 'Access token expired' };
      }

      if (operation && !token.permissions?.includes(operation) && !token.permissions?.includes('admin')) {
        return { valid: false, error: `Insufficient permissions for operation: ${operation}` };
      }

      return { valid: true, token };

    } catch (error) {
      this.logError('_validateAccessToken', error);
      return { valid: false, error: 'Token validation failed' };
    }
  }

  /**
   * Generate system access tokens
   */
  private async _generateSystemAccessTokens(): Promise<void> {
    try {
      // Generate admin token
      const adminToken: IAccessToken = {
        token: crypto.randomBytes(32).toString('hex'),
        permissions: ['admin', 'read', 'write', 'profile'],
        expiresAt: new Date(Date.now() + PROFILER_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY),
        issuedFor: 'system-admin',
        securityLevel: 'restricted'
      };

      this._accessTokens.set(adminToken.token, adminToken);

      // Generate read-only token
      const readToken: IAccessToken = {
        token: crypto.randomBytes(32).toString('hex'),
        permissions: ['read'],
        expiresAt: new Date(Date.now() + PROFILER_SECURITY_CONFIG.ACCESS_TOKEN_EXPIRY),
        issuedFor: 'system-reader',
        securityLevel: 'internal'
      };

      this._accessTokens.set(readToken.token, readToken);

      this.logOperation('_generateSystemAccessTokens', 'complete', {
        tokensGenerated: 2
      });

    } catch (error) {
      this.logError('_generateSystemAccessTokens', error);
      throw error;
    }
  }

  // ============================================================================
  // UTILITY AND HELPER METHODS
  // ============================================================================

  /**
   * Validate service is initialized
   */
  private validateInitialized(): void {
    if (!this._initialized) {
      throw new Error('Profiler not initialized');
    }
  }

  /**
   * Validate rule ID
   */
  private validateRuleId(ruleId: string): void {
    if (!ruleId || typeof ruleId !== 'string') {
      throw new Error('Invalid rule ID: must be a non-empty string');
    }
  }

  /**
   * Validate session ID
   */
  private validateSessionId(sessionId: string): void {
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('Invalid session ID: must be a non-empty string');
    }
  }

  /**
   * Calculate average session duration
   */
  private _calculateAverageSessionDuration(): number {
    const completedSessions = Array.from(this._sessions.values()).filter(s => s.endedAt && s.durationMs);
    if (completedSessions.length === 0) return 0;

    const totalDuration = completedSessions.reduce((sum, session) => sum + (session.durationMs ?? 0), 0);
    return totalDuration / completedSessions.length;
  }

  /**
   * Calculate success rate
   */
  private _calculateSuccessRate(): number {
    const completedSessions = Array.from(this._sessions.values()).filter(s => s.endedAt);
    if (completedSessions.length === 0) return 100;

    const successfulSessions = completedSessions.filter(s => !s.error);
    return (successfulSessions.length / completedSessions.length) * 100;
  }

  // ============================================================================
  // MAINTENANCE AND CLEANUP METHODS
  // ============================================================================

  /**
   * Perform session cleanup
   */
  private async _performCleanup(): Promise<void> {
    try {
      this.logOperation('_performCleanup', 'start');

      const now = new Date();
      const timeout = this._profilerConfig.sessionTimeout;
      let cleanedCount = 0;

      // Remove expired sessions
      const sessionEntries = Array.from(this._sessions.entries());
      for (let i = 0; i < sessionEntries.length; i++) {
        const [sessionId, session] = sessionEntries[i];
        const sessionAge = now.getTime() - session.startedAt.getTime();
        
        if (sessionAge > timeout) {
          this._sessions.delete(sessionId);
          cleanedCount++;
        }
      }

      // Clean up expired access tokens
      let expiredTokens = 0;
      const tokenEntries = Array.from(this._accessTokens.entries());
      for (let i = 0; i < tokenEntries.length; i++) {
        const [token, tokenData] = tokenEntries[i];
        if (tokenData.expiresAt < now) {
          this._accessTokens.delete(token);
          expiredTokens++;
        }
      }

      await this._auditOperation('*', PROFILER_OPERATIONS.CLEANUP, 'system', true, undefined, {
        cleanedSessions: cleanedCount,
        expiredTokens,
        remainingSessions: this._sessions.size
      });

      this.logOperation('_performCleanup', 'complete', {
        cleanedSessions: cleanedCount,
        expiredTokens,
        remainingSessions: this._sessions.size
      });

    } catch (error) {
      this.logError('_performCleanup', error);
    }
  }

  /**
   * Optimize sessions
   */
  private async _optimizeSessions(): Promise<void> {
    try {
      // Remove oldest sessions if over limit
      if (this._sessions.size > this._profilerConfig.maxSessions * 0.8) {
        const sessions = Array.from(this._sessions.entries())
          .sort((a, b) => a[1].startedAt.getTime() - b[1].startedAt.getTime());

        const removeCount = Math.floor(this._sessions.size * 0.2);
        for (let i = 0; i < removeCount && i < sessions.length; i++) {
          this._sessions.delete(sessions[i][0]);
        }

        this.logOperation('_optimizeSessions', 'complete', {
          removedSessions: removeCount,
          remainingSessions: this._sessions.size
        });
      }

    } catch (error) {
      this.logError('_optimizeSessions', error);
    }
  }

  /**
   * Audit profiler operation
   */
  private async _auditOperation(
    sessionId: string,
    operation: string,
    accessToken?: string,
    success: boolean = true,
    errorMessage?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      if (!this._profilerConfig.enableAuditTrail) {
        return;
      }

      const auditEntry: IProfilerAuditEntry = {
        operation,
        timestamp: new Date(),
        accessToken: accessToken ?? 'anonymous',
        success,
        errorMessage,
        metadata: metadata ?? {}
      };

      // Add to session audit trail if session exists
      const session = this._sessions.get(sessionId);
      if (session) {
        session.auditTrail.push(auditEntry);
        
        // Keep only recent audit entries
        if (session.auditTrail.length > PROFILER_SECURITY_CONFIG.MAX_AUDIT_ENTRIES) {
          session.auditTrail = session.auditTrail.slice(-50);
        }
      }

      // Log audit event
      this.logOperation('audit', operation, {
        sessionId,
        success,
        accessToken: accessToken ?? 'anonymous',
        errorMessage,
        metadata
      });

    } catch (error) {
      this.logError('_auditOperation', error);
    }
  }
}