/**
 * @file Governance Rule Insights Generator Factory
 * @filepath server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGeneratorFactory.ts
 * @task-id G-TSK-06.SUB-06.1.IMP-04F
 * @component governance-rule-insights-generator-factory
 * @reference foundation-context.ANALYTICS.009
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Analytics
 * @created 2025-07-01 13:46:04 +03
 * @modified 2025-07-01 13:46:04 +03
 * 
 * @description
 * Factory for creating and managing Governance Rule Insights Generator instances with:
 * - Singleton pattern implementation for insights generator management
 * - Instance pooling and lifecycle management
 * - Configuration management and validation
 * - Performance monitoring and insights tracking
 * - Enterprise-grade factory pattern implementation
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleInsightsGenerator
 * @enables server/src/platform/governance/analytics-engines
 * @related-contexts foundation-context, governance-context
 * @governance-impact framework-foundation, governance-analytics
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-factory
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/analytics/governance-rule-insights-generator-factory.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-01) - Initial implementation with enterprise-grade factory capabilities
 */

import {
  GovernanceRuleInsightsGenerator,
  IAnalyticsService,
  TInsightsGeneratorConfig
} from './GovernanceRuleInsightsGenerator';
import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';

/**
 * Factory configuration type
 */
export type TInsightsGeneratorFactoryConfig = {
  maxInstances: number;
  instanceTimeout: number;
  enablePooling: boolean;
  poolSize: number;
  enableMonitoring: boolean;
  autoCleanup: boolean;
  cleanupInterval: number;
  insightsCaching: boolean;
  cacheRetentionPeriod: number;
};

/**
 * Factory metrics type
 */
export type TInsightsFactoryMetrics = {
  instancesCreated: number;
  instancesDestroyed: number;
  activeInstances: number;
  pooledInstances: number;
  factoryUptime: number;
  averageCreationTime: number;
  errorCount: number;
  totalInsightsGenerated: number;
  averageInsightGenerationTime: number;
};

/**
 * Default factory configuration
 */
const DEFAULT_INSIGHTS_FACTORY_CONFIG: TInsightsGeneratorFactoryConfig = {
  maxInstances: 50, // Increased for high-volume testing scenarios
  instanceTimeout: 3600000, // 1 hour
  enablePooling: true,
  poolSize: 10, // Increased pool size for better performance
  enableMonitoring: true,
  autoCleanup: true,
  cleanupInterval: 300000, // 5 minutes
  insightsCaching: true,
  cacheRetentionPeriod: 1800000 // 30 minutes
};

/**
 * Governance Rule Insights Generator Factory
 * Implements singleton pattern for managing insights generator instances
 */
export class GovernanceRuleInsightsGeneratorFactory {
  private static _instance: GovernanceRuleInsightsGeneratorFactory | null = null;
  private static _initializationPromise: Promise<GovernanceRuleInsightsGeneratorFactory> | null = null;

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-insights-generator-factory';
  
  // Instance management
  private _instances: Map<string, IAnalyticsService> = new Map();
  private _instancePool: IAnalyticsService[] = [];
  private _instanceConfigs: Map<string, TInsightsGeneratorConfig> = new Map();
  private _instanceMetadata: Map<string, any> = new Map();
  
  // Factory configuration and state
  private _config: TInsightsGeneratorFactoryConfig = DEFAULT_INSIGHTS_FACTORY_CONFIG;
  private _isInitialized = false;
  private _createdAt: Date = new Date();
  
  // Performance tracking
  private _instancesCreated = 0;
  private _instancesDestroyed = 0;
  private _totalCreationTime = 0;
  private _errorCount = 0;
  private _totalInsightsGenerated = 0;
  private _totalInsightGenerationTime = 0;
  
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Private constructor
  }

  /**
   * Get singleton instance of the factory
   */
  public static async getInstance(): Promise<GovernanceRuleInsightsGeneratorFactory> {
    if (GovernanceRuleInsightsGeneratorFactory._instance) {
      return GovernanceRuleInsightsGeneratorFactory._instance;
    }

    if (GovernanceRuleInsightsGeneratorFactory._initializationPromise) {
      return GovernanceRuleInsightsGeneratorFactory._initializationPromise;
    }

    GovernanceRuleInsightsGeneratorFactory._initializationPromise = 
      GovernanceRuleInsightsGeneratorFactory._createInstance();
    
    return GovernanceRuleInsightsGeneratorFactory._initializationPromise;
  }

  /**
   * Create and initialize factory instance
   */
  private static async _createInstance(): Promise<GovernanceRuleInsightsGeneratorFactory> {
    const factory = new GovernanceRuleInsightsGeneratorFactory();
    await factory._initialize();
    GovernanceRuleInsightsGeneratorFactory._instance = factory;
    return factory;
  }

  /**
   * Initialize the factory
   */
  private async _initialize(): Promise<void> {
    try {
      // Validate configuration
      this._validateConfiguration();
      
      // Initialize instance pool if enabled
      if (this._config.enablePooling) {
        await this._initializeInstancePool();
      }
      
      // Start cleanup timer if enabled
      if (this._config.autoCleanup) {
        this._startCleanupTimer();
      }
      
      // Start monitoring if enabled
      if (this._config.enableMonitoring) {
        this._startMonitoring();
      }
      
      // Start cache cleanup if caching enabled
      if (this._config.insightsCaching) {
        this._startCacheCleanup();
      }
      
      this._isInitialized = true;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      this._errorCount++;
      throw new Error(`Insights factory initialization failed: ${errorMessage}`);
    }
  }

  /**
   * Create a new insights generator instance
   */
  public async createInsightsGenerator(config?: TInsightsGeneratorConfig): Promise<IAnalyticsService> {
    try {
      const startTime = Date.now();
      
      // Check if we can reuse a pooled instance
      if (this._config.enablePooling && this._instancePool.length > 0 && !config) {
        const instance = this._instancePool.pop()!;
        const instanceId = this._generateInstanceId();
        this._instances.set(instanceId, instance);
        this._instanceMetadata.set(instanceId, { 
          createdAt: new Date(), 
          reused: true,
          insightsGenerated: 0,
          totalInsightTime: 0
        });
        
        // Still update metrics for reused instances
        const creationTime = Date.now() - startTime;
        this._instancesCreated++;
        this._totalCreationTime += creationTime;
        
        return instance;
      }
      
      // Check instance limit
      if (this._instances.size >= this._config.maxInstances) {
        throw new Error(`Maximum instances limit reached: ${this._config.maxInstances}`);
      }
      
      // Create new instance
      const instance = new GovernanceRuleInsightsGenerator();
      const instanceId = this._generateInstanceId();
      
      // Initialize instance
      await (instance as GovernanceRuleInsightsGenerator).initialize();
      
      // Configure if config provided
      if (config) {
        await instance.configure(config);
        this._instanceConfigs.set(instanceId, config);
      }
      
      // Start instance
      await instance.start();
      
      // Store instance
      this._instances.set(instanceId, instance);
      this._instanceMetadata.set(instanceId, { 
        createdAt: new Date(), 
        reused: false,
        config: config ? 'custom' : 'default',
        insightsGenerated: 0,
        totalInsightTime: 0
      });
      
      // Update metrics
      const creationTime = Date.now() - startTime;
      this._instancesCreated++;
      this._totalCreationTime += creationTime;
      
      return instance;
      
    } catch (error) {
      this._errorCount++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown creation error';
      throw new Error(`Failed to create insights generator: ${errorMessage}`);
    }
  }

  /**
   * Release an insights generator instance
   */
  public async releaseInsightsGenerator(instance: IAnalyticsService): Promise<void> {
    try {
      // Find instance ID
      const instanceId = this._findInstanceId(instance);
      if (!instanceId) {
        throw new Error('Instance not found in factory registry');
      }
      
      // Update metrics from instance
      await this._updateInstanceMetrics(instanceId, instance);
      
      // Stop instance
      await instance.stop();
      
      // Remove from active instances first
      this._instances.delete(instanceId);
      this._instanceConfigs.delete(instanceId);
      this._instanceMetadata.delete(instanceId);
      
      // Pool instance if pooling enabled and pool not full
      if (this._config.enablePooling && this._instancePool.length < this._config.poolSize) {
        // Reset instance to default state
        await this._resetInstanceToDefault(instance);
        this._instancePool.push(instance);
      } else {
        // Destroy instance
        await this._destroyInstance(instance);
        this._instancesDestroyed++;
      }
      
    } catch (error) {
      this._errorCount++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown release error';
      throw new Error(`Failed to release insights generator: ${errorMessage}`);
    }
  }

  /**
   * Get all active insights generator instances
   */
  public getActiveInstances(): IAnalyticsService[] {
    return Array.from(this._instances.values());
  }

  /**
   * Get factory metrics
   */
  public getMetrics(): TInsightsFactoryMetrics {
    return {
      instancesCreated: this._instancesCreated,
      instancesDestroyed: this._instancesDestroyed,
      activeInstances: this._instances.size,
      pooledInstances: this._instancePool.length,
      factoryUptime: Date.now() - this._createdAt.getTime(),
      averageCreationTime: this._totalCreationTime / Math.max(this._instancesCreated, 1),
      errorCount: this._errorCount,
      totalInsightsGenerated: this._totalInsightsGenerated,
      averageInsightGenerationTime: this._totalInsightGenerationTime / Math.max(this._totalInsightsGenerated, 1)
    };
  }

  /**
   * Configure the factory
   */
  public async configure(config: Partial<TInsightsGeneratorFactoryConfig>): Promise<void> {
    const oldConfig = { ...this._config };
    const newConfig = { ...this._config, ...config };
    this._validateConfiguration(newConfig);
    
    // Check if pooling configuration changed
    const poolingChanged = (
      newConfig.enablePooling !== oldConfig.enablePooling ||
      newConfig.poolSize !== oldConfig.poolSize
    );
    
    this._config = newConfig;
    
    // Handle pool reconfiguration
    if (poolingChanged) {
      await this._reconfigurePool(oldConfig, newConfig);
    }
    
    // ✅ TIMER COORDINATION: Timer restart logic now handled automatically by TimerCoordinationService
    // Configuration changes will be applied to new timer instances automatically
  }

  /**
   * Get factory configuration
   */
  public getConfiguration(): TInsightsGeneratorFactoryConfig {
    return { ...this._config };
  }

  /**
   * Shutdown the factory and cleanup all instances
   */
  public async shutdown(): Promise<void> {
    try {
      // Stop timers
      this._stopCleanupTimer();
      this._stopMonitoring();
      this._stopCacheCleanup();
      
      // Stop and cleanup all active instances
      const shutdownPromises = Array.from(this._instances.values()).map(instance => 
        instance.stop().catch(() => {}) // Ignore errors during shutdown
      );
      await Promise.allSettled(shutdownPromises);
      
      // Cleanup pooled instances
      const poolCleanupPromises = this._instancePool.map(instance => 
        this._destroyInstance(instance).catch(() => {}) // Ignore errors during cleanup
      );
      await Promise.allSettled(poolCleanupPromises);
      
      // Clear all maps and arrays
      this._instances.clear();
      this._instanceConfigs.clear();
      this._instanceMetadata.clear();
      this._instancePool.length = 0;
      
      this._isInitialized = false;
      
    } catch (error) {
      this._errorCount++;
      throw new Error(`Insights factory shutdown failed: ${error}`);
    }
  }

  // Private helper methods
  private _validateConfiguration(config?: TInsightsGeneratorFactoryConfig): void {
    const configToValidate = config || this._config;
    
    if (configToValidate.maxInstances < 1) {
      throw new Error('maxInstances must be >= 1');
    }
    
    if (configToValidate.poolSize < 0) {
      throw new Error('poolSize must be >= 0');
    }
    
    if (configToValidate.poolSize > configToValidate.maxInstances) {
      throw new Error('poolSize cannot exceed maxInstances');
    }
    
    if (configToValidate.instanceTimeout < 0) {
      throw new Error('instanceTimeout must be >= 0');
    }
    
    if (configToValidate.cleanupInterval < 1000) {
      throw new Error('cleanupInterval must be >= 1000ms');
    }
    
    if (configToValidate.cacheRetentionPeriod < 60000) {
      throw new Error('cacheRetentionPeriod must be >= 60000ms (1 minute)');
    }
  }

  /**
   * Reconfigure instance pool when pooling settings change
   */
  private async _reconfigurePool(
    oldConfig: TInsightsGeneratorFactoryConfig, 
    newConfig: TInsightsGeneratorFactoryConfig
  ): Promise<void> {
    // If pooling was disabled and now enabled, initialize pool
    if (!oldConfig.enablePooling && newConfig.enablePooling) {
      await this._initializeInstancePool();
      return;
    }
    
    // If pooling was enabled and now disabled, clear pool
    if (oldConfig.enablePooling && !newConfig.enablePooling) {
      await this._clearInstancePool();
      return;
    }
    
    // If pool size changed and pooling is enabled
    if (newConfig.enablePooling && oldConfig.poolSize !== newConfig.poolSize) {
      if (newConfig.poolSize > oldConfig.poolSize) {
        // Increase pool size - add more instances
        const instancesToAdd = newConfig.poolSize - this._instancePool.length;
        for (let i = 0; i < instancesToAdd; i++) {
          await this._createPooledInstance();
        }
      } else if (newConfig.poolSize < this._instancePool.length) {
        // Decrease pool size - remove excess instances
        const instancesToRemove = this._instancePool.length - newConfig.poolSize;
        for (let i = 0; i < instancesToRemove; i++) {
          const instance = this._instancePool.pop();
          if (instance) {
            await this._destroyInstance(instance);
          }
        }
      }
    }
  }

  /**
   * Clear all instances from the pool
   */
  private async _clearInstancePool(): Promise<void> {
    const clearPromises = this._instancePool.map(instance => 
      this._destroyInstance(instance).catch(() => {}) // Ignore errors during cleanup
    );
    await Promise.allSettled(clearPromises);
    this._instancePool.length = 0;
  }

  private async _initializeInstancePool(): Promise<void> {
    const poolInitPromises: Promise<void>[] = [];
    
    for (let i = 0; i < this._config.poolSize; i++) {
      poolInitPromises.push(this._createPooledInstance());
    }
    
    await Promise.all(poolInitPromises);
  }

  private async _createPooledInstance(): Promise<void> {
    try {
      const instance = new GovernanceRuleInsightsGenerator();
      await (instance as GovernanceRuleInsightsGenerator).initialize();
      this._instancePool.push(instance);
    } catch (error) {
      // Log error but don't fail entire pool initialization
      this._errorCount++;
    }
  }

  private _generateInstanceId(): string {
    return `insights-gen-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private _findInstanceId(instance: IAnalyticsService): string | null {
    const entries = Array.from(this._instances.entries());
    for (const [id, storedInstance] of entries) {
      if (storedInstance === instance) {
        return id;
      }
    }
    return null;
  }

  private async _updateInstanceMetrics(instanceId: string, instance: IAnalyticsService): Promise<void> {
    try {
      const metrics = await instance.getMetrics();
      const metadata = this._instanceMetadata.get(instanceId);
      
      if (metadata && metrics.custom) {
        // Extract insights-specific metrics from the custom property
        const insightsGenerated = metrics.custom.insightsGenerated || 0;
        const averageInsightTime = metrics.custom.averageInsightTime || 0;
        
        this._totalInsightsGenerated += insightsGenerated - metadata.insightsGenerated;
        this._totalInsightGenerationTime += averageInsightTime * insightsGenerated - metadata.totalInsightTime;
        
        metadata.insightsGenerated = insightsGenerated;
        metadata.totalInsightTime = averageInsightTime * insightsGenerated;
      }
    } catch (error) {
      // Ignore metrics update errors
    }
  }

  private async _resetInstanceToDefault(instance: IAnalyticsService): Promise<void> {
    try {
      // Stop and reinitialize instance to reset state
      await instance.stop();
      await (instance as GovernanceRuleInsightsGenerator).initialize();
    } catch (error) {
      // If reset fails, don't pool the instance
      throw error;
    }
  }

  private async _destroyInstance(instance: IAnalyticsService): Promise<void> {
    try {
      await instance.stop();
    } catch (error) {
      // Ignore errors during destruction
    }
  }

  private _startCleanupTimer(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performCleanup().catch(() => {
          this._errorCount++;
        });
      },
      this._config.cleanupInterval,
      'GovernanceRuleInsightsGeneratorFactory',
      'cleanup'
    );
  }

  private _stopCleanupTimer(): void {
    // ✅ TIMER COORDINATION: Cleanup now handled automatically by TimerCoordinationService
  }

  private _startCacheCleanup(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performCacheCleanup().catch(() => {
          this._errorCount++;
        });
      },
      this._config.cacheRetentionPeriod / 2, // Clean every half retention period
      'GovernanceRuleInsightsGeneratorFactory',
      'cache-cleanup'
    );
  }

  private _stopCacheCleanup(): void {
    // ✅ TIMER COORDINATION: Cleanup now handled automatically by TimerCoordinationService
  }

  private async _performCleanup(): Promise<void> {
    const now = Date.now();
    const expiredInstances: string[] = [];
    
    // Find expired instances
    const metadataEntries = Array.from(this._instanceMetadata.entries());
    for (const [instanceId, metadata] of metadataEntries) {
      if (now - metadata.createdAt.getTime() > this._config.instanceTimeout) {
        expiredInstances.push(instanceId);
      }
    }
    
    // Cleanup expired instances
    for (const instanceId of expiredInstances) {
      const instance = this._instances.get(instanceId);
      if (instance) {
        try {
          await this.releaseInsightsGenerator(instance);
        } catch (error) {
          this._errorCount++;
        }
      }
    }
  }

  private async _performCacheCleanup(): Promise<void> {
    // In a real implementation, this would clean up cached insights
    // based on the cache retention period
  }

  private _startMonitoring(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performMonitoring();
      },
      60000, // Monitor every minute
      'GovernanceRuleInsightsGeneratorFactory',
      'monitoring'
    );
  }

  private _stopMonitoring(): void {
    // ✅ TIMER COORDINATION: Cleanup now handled automatically by TimerCoordinationService
  }

  private _performMonitoring(): void {
    // Basic health monitoring
    const metrics = this.getMetrics();
    
    // Log metrics or send to monitoring system
    if (metrics.errorCount > 10) {
      // High error rate detected
    }
    
    if (metrics.activeInstances === 0 && this._instancesCreated > 0) {
      // No active instances but factory has been used
    }
    
    // Monitor insights generation performance
    if (metrics.averageInsightGenerationTime > 5000) {
      // Insights generation is taking too long
    }
  }

  /**
   * Static method to create a single insights generator instance
   * Convenience method for simple use cases
   */
  public static async createInsightsGenerator(config?: TInsightsGeneratorConfig): Promise<IAnalyticsService> {
    const factory = await GovernanceRuleInsightsGeneratorFactory.getInstance();
    return factory.createInsightsGenerator(config);
  }

  /**
   * Static method to release an insights generator instance
   * Convenience method for simple use cases
   */
  public static async releaseInsightsGenerator(instance: IAnalyticsService): Promise<void> {
    const factory = await GovernanceRuleInsightsGeneratorFactory.getInstance();
    return factory.releaseInsightsGenerator(instance);
  }
} 