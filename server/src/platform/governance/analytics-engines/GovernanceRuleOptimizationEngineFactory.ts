/**
 * @file Governance Rule Optimization Engine Factory
 * @filepath server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngineFactory.ts
 * @task-id G-TSK-06.SUB-06.1.IMP-03F
 * @component governance-rule-optimization-engine-factory
 * @reference foundation-context.ANALYTICS.008
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Analytics
 * @created 2025-07-01 13:46:04 +03
 * @modified 2025-07-01 13:46:04 +03
 * 
 * @description
 * Factory for creating and managing Governance Rule Optimization Engine instances with:
 * - Singleton pattern implementation for optimization engine management
 * - Instance pooling and lifecycle management
 * - Configuration management and validation
 * - Performance monitoring and optimization
 * - Enterprise-grade factory pattern implementation
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleOptimizationEngine
 * @enables server/src/platform/governance/analytics-engines
 * @related-contexts foundation-context, governance-context
 * @governance-impact framework-foundation, governance-analytics
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-factory
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/analytics/governance-rule-optimization-engine-factory.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 * 
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-01) - Initial implementation with enterprise-grade factory capabilities
 */

import {
  GovernanceRuleOptimizationEngine,
  IOptimizationService,
  TOptimizationEngineConfig
} from './GovernanceRuleOptimizationEngine';
import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';

/**
 * Factory configuration type
 */
export type TOptimizationEngineFactoryConfig = {
  maxInstances: number;
  instanceTimeout: number;
  enablePooling: boolean;
  poolSize: number;
  enableMonitoring: boolean;
  autoCleanup: boolean;
  cleanupInterval: number;
};

/**
 * Factory metrics type
 */
export type TFactoryMetrics = {
  instancesCreated: number;
  instancesDestroyed: number;
  activeInstances: number;
  pooledInstances: number;
  factoryUptime: number;
  averageCreationTime: number;
  errorCount: number;
};

/**
 * Default factory configuration
 */
const DEFAULT_FACTORY_CONFIG: TOptimizationEngineFactoryConfig = {
  maxInstances: 50, // Increased for high-volume testing scenarios
  instanceTimeout: 3600000, // 1 hour
  enablePooling: true,
  poolSize: 10, // Increased pool size for better performance
  enableMonitoring: true,
  autoCleanup: true,
  cleanupInterval: 300000 // 5 minutes
};

/**
 * Governance Rule Optimization Engine Factory
 * Implements singleton pattern for managing optimization engine instances
 */
export class GovernanceRuleOptimizationEngineFactory {
  private static _instance: GovernanceRuleOptimizationEngineFactory | null = null;
  private static _initializationPromise: Promise<GovernanceRuleOptimizationEngineFactory> | null = null;

  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-optimization-engine-factory';
  
  // Instance management
  private _instances: Map<string, IOptimizationService> = new Map();
  private _instancePool: IOptimizationService[] = [];
  private _instanceConfigs: Map<string, TOptimizationEngineConfig> = new Map();
  private _instanceMetadata: Map<string, any> = new Map();
  
  // Factory configuration and state
  private _config: TOptimizationEngineFactoryConfig = DEFAULT_FACTORY_CONFIG;
  private _isInitialized = false;
  private _createdAt: Date = new Date();
  
  // Performance tracking
  private _instancesCreated = 0;
  private _instancesDestroyed = 0;
  private _totalCreationTime = 0;
  private _errorCount = 0;
  
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    // Private constructor
  }

  /**
   * Get singleton instance of the factory
   */
  public static async getInstance(): Promise<GovernanceRuleOptimizationEngineFactory> {
    if (GovernanceRuleOptimizationEngineFactory._instance) {
      return GovernanceRuleOptimizationEngineFactory._instance;
    }

    if (GovernanceRuleOptimizationEngineFactory._initializationPromise) {
      return GovernanceRuleOptimizationEngineFactory._initializationPromise;
    }

    GovernanceRuleOptimizationEngineFactory._initializationPromise = 
      GovernanceRuleOptimizationEngineFactory._createInstance();
    
    return GovernanceRuleOptimizationEngineFactory._initializationPromise;
  }

  /**
   * Create and initialize factory instance
   */
  private static async _createInstance(): Promise<GovernanceRuleOptimizationEngineFactory> {
    const factory = new GovernanceRuleOptimizationEngineFactory();
    await factory._initialize();
    GovernanceRuleOptimizationEngineFactory._instance = factory;
    return factory;
  }

  /**
   * Initialize the factory
   */
  private async _initialize(): Promise<void> {
    try {
      // Validate configuration
      this._validateConfiguration();
      
      // Initialize instance pool if enabled
      if (this._config.enablePooling) {
        await this._initializeInstancePool();
      }
      
      // Start cleanup timer if enabled
      if (this._config.autoCleanup) {
        this._startCleanupTimer();
      }
      
      // Start monitoring if enabled
      if (this._config.enableMonitoring) {
        this._startMonitoring();
      }
      
      this._isInitialized = true;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      this._errorCount++;
      throw new Error(`Factory initialization failed: ${errorMessage}`);
    }
  }

  /**
   * Create a new optimization engine instance
   */
  public async createOptimizationEngine(config?: TOptimizationEngineConfig): Promise<IOptimizationService> {
    try {
      const startTime = Date.now();
      
      // Check if we can reuse a pooled instance
      if (this._config.enablePooling && this._instancePool.length > 0 && !config) {
        const instance = this._instancePool.pop()!;
        const instanceId = this._generateInstanceId();
        this._instances.set(instanceId, instance);
        this._instanceMetadata.set(instanceId, { createdAt: new Date(), reused: true });
        return instance;
      }
      
      // Check instance limit
      if (this._instances.size >= this._config.maxInstances) {
        throw new Error(`Maximum instances limit reached: ${this._config.maxInstances}`);
      }
      
      // Create new instance
      const instance = new GovernanceRuleOptimizationEngine();
      const instanceId = this._generateInstanceId();
      
      // Initialize instance
      await instance.initialize();
      
      // Configure if config provided
      if (config) {
        await instance.configure(config);
        this._instanceConfigs.set(instanceId, config);
      }
      
      // Start instance
      await instance.start();
      
      // Store instance
      this._instances.set(instanceId, instance);
      this._instanceMetadata.set(instanceId, { 
        createdAt: new Date(), 
        reused: false,
        config: config ? 'custom' : 'default'
      });
      
      // Update metrics
      const creationTime = Date.now() - startTime;
      this._instancesCreated++;
      this._totalCreationTime += creationTime;
      
      return instance;
      
    } catch (error) {
      this._errorCount++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown creation error';
      throw new Error(`Failed to create optimization engine: ${errorMessage}`);
    }
  }

  /**
   * Release an optimization engine instance
   */
  public async releaseOptimizationEngine(instance: IOptimizationService): Promise<void> {
    try {
      // Find instance ID
      const instanceId = this._findInstanceId(instance);
      if (!instanceId) {
        throw new Error('Instance not found in factory registry');
      }
      
      // Stop instance
      await instance.stop();
      
      // Pool instance if pooling enabled and pool not full
      if (this._config.enablePooling && this._instancePool.length < this._config.poolSize) {
        // Reset instance to default state
        await this._resetInstanceToDefault(instance);
        this._instancePool.push(instance);
      } else {
        // Destroy instance
        await this._destroyInstance(instance);
        this._instancesDestroyed++;
      }
      
      // Remove from active instances
      this._instances.delete(instanceId);
      this._instanceConfigs.delete(instanceId);
      this._instanceMetadata.delete(instanceId);
      
    } catch (error) {
      this._errorCount++;
      const errorMessage = error instanceof Error ? error.message : 'Unknown release error';
      throw new Error(`Failed to release optimization engine: ${errorMessage}`);
    }
  }

  /**
   * Get all active optimization engine instances
   */
  public getActiveInstances(): IOptimizationService[] {
    return Array.from(this._instances.values());
  }

  /**
   * Get factory metrics
   */
  public getMetrics(): TFactoryMetrics {
    return {
      instancesCreated: this._instancesCreated,
      instancesDestroyed: this._instancesDestroyed,
      activeInstances: this._instances.size,
      pooledInstances: this._instancePool.length,
      factoryUptime: Date.now() - this._createdAt.getTime(),
      averageCreationTime: this._totalCreationTime / Math.max(this._instancesCreated, 1),
      errorCount: this._errorCount
    };
  }

  /**
   * Configure the factory
   */
  public async configure(config: Partial<TOptimizationEngineFactoryConfig>): Promise<void> {
    const oldConfig = { ...this._config };
    const newConfig = { ...this._config, ...config };
    this._validateConfiguration(newConfig);
    
    // Check if pooling configuration changed
    const poolingChanged = (
      newConfig.enablePooling !== oldConfig.enablePooling ||
      newConfig.poolSize !== oldConfig.poolSize
    );
    
    this._config = newConfig;
    
    // Handle pool reconfiguration
    if (poolingChanged) {
      await this._reconfigurePool(oldConfig, newConfig);
    }
    
    // ✅ TIMER COORDINATION: Timer restart logic now handled automatically by TimerCoordinationService
    // Configuration changes will be applied to new timer instances automatically
  }

  /**
   * Reconfigure instance pool when pooling settings change
   */
  private async _reconfigurePool(
    oldConfig: TOptimizationEngineFactoryConfig, 
    newConfig: TOptimizationEngineFactoryConfig
  ): Promise<void> {
    // If pooling was disabled and now enabled, initialize pool
    if (!oldConfig.enablePooling && newConfig.enablePooling) {
      await this._initializeInstancePool();
      return;
    }
    
    // If pooling was enabled and now disabled, clear pool
    if (oldConfig.enablePooling && !newConfig.enablePooling) {
      await this._clearInstancePool();
      return;
    }
    
    // If pool size changed and pooling is enabled
    if (newConfig.enablePooling && oldConfig.poolSize !== newConfig.poolSize) {
      if (newConfig.poolSize > oldConfig.poolSize) {
        // Increase pool size - add more instances
        const instancesToAdd = newConfig.poolSize - this._instancePool.length;
        for (let i = 0; i < instancesToAdd; i++) {
          await this._createPooledInstance();
        }
      } else if (newConfig.poolSize < this._instancePool.length) {
        // Decrease pool size - remove excess instances
        const instancesToRemove = this._instancePool.length - newConfig.poolSize;
        for (let i = 0; i < instancesToRemove; i++) {
          const instance = this._instancePool.pop();
          if (instance) {
            await this._destroyInstance(instance);
          }
        }
      }
    }
  }

  /**
   * Clear all instances from the pool
   */
  private async _clearInstancePool(): Promise<void> {
    const clearPromises = this._instancePool.map(instance => 
      this._destroyInstance(instance).catch(() => {}) // Ignore errors during cleanup
    );
    await Promise.allSettled(clearPromises);
    this._instancePool.length = 0;
  }

  /**
   * Get factory configuration
   */
  public getConfiguration(): TOptimizationEngineFactoryConfig {
    return { ...this._config };
  }

  /**
   * Shutdown the factory and cleanup all instances
   */
  public async shutdown(): Promise<void> {
    try {
      // Stop timers
      this._stopCleanupTimer();
      this._stopMonitoring();
      
      // Stop and cleanup all active instances
      const shutdownPromises = Array.from(this._instances.values()).map(instance => 
        instance.stop().catch(() => {}) // Ignore errors during shutdown
      );
      await Promise.allSettled(shutdownPromises);
      
      // Cleanup pooled instances
      const poolCleanupPromises = this._instancePool.map(instance => 
        this._destroyInstance(instance).catch(() => {}) // Ignore errors during cleanup
      );
      await Promise.allSettled(poolCleanupPromises);
      
      // Clear all maps and arrays
      this._instances.clear();
      this._instanceConfigs.clear();
      this._instanceMetadata.clear();
      this._instancePool.length = 0;
      
      this._isInitialized = false;
      
    } catch (error) {
      this._errorCount++;
      throw new Error(`Factory shutdown failed: ${error}`);
    }
  }

  // Private helper methods
  private _validateConfiguration(config?: TOptimizationEngineFactoryConfig): void {
    const configToValidate = config || this._config;
    
    if (configToValidate.maxInstances < 1) {
      throw new Error('maxInstances must be >= 1');
    }
    
    if (configToValidate.poolSize < 0) {
      throw new Error('poolSize must be >= 0');
    }
    
    if (configToValidate.poolSize > configToValidate.maxInstances) {
      throw new Error('poolSize cannot exceed maxInstances');
    }
    
    if (configToValidate.instanceTimeout < 0) {
      throw new Error('instanceTimeout must be >= 0');
    }
    
    if (configToValidate.cleanupInterval < 1000) {
      throw new Error('cleanupInterval must be >= 1000ms');
    }
  }

  private async _initializeInstancePool(): Promise<void> {
    const poolInitPromises: Promise<void>[] = [];
    
    for (let i = 0; i < this._config.poolSize; i++) {
      poolInitPromises.push(this._createPooledInstance());
    }
    
    await Promise.all(poolInitPromises);
  }

  private async _createPooledInstance(): Promise<void> {
    try {
      const instance = new GovernanceRuleOptimizationEngine();
      await (instance as GovernanceRuleOptimizationEngine).initialize();
      this._instancePool.push(instance);
    } catch (error) {
      // Log error but don't fail entire pool initialization
      this._errorCount++;
    }
  }

  private _generateInstanceId(): string {
    return `opt-engine-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private _findInstanceId(instance: IOptimizationService): string | null {
    // ES5 compatible Map iteration
    let foundId: string | null = null;
    this._instances.forEach((storedInstance, id) => {
      if (storedInstance === instance && foundId === null) {
        foundId = id;
      }
    });
    return foundId;
  }

  private async _resetInstanceToDefault(instance: IOptimizationService): Promise<void> {
    try {
      // Stop and reinitialize instance to reset state
      await instance.stop();
      await (instance as GovernanceRuleOptimizationEngine).initialize();
    } catch (error) {
      // If reset fails, don't pool the instance
      throw error;
    }
  }

  private async _destroyInstance(instance: IOptimizationService): Promise<void> {
    try {
      await instance.stop();
    } catch (error) {
      // Ignore errors during destruction
    }
  }

  private _startCleanupTimer(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performCleanup().catch(() => {
          this._errorCount++;
        });
      },
      this._config.cleanupInterval,
      'GovernanceRuleOptimizationEngineFactory',
      'cleanup'
    );
  }

  private _stopCleanupTimer(): void {
    // ✅ TIMER COORDINATION: Cleanup now handled automatically by TimerCoordinationService
  }

  private async _performCleanup(): Promise<void> {
    const now = Date.now();
    const expiredInstances: string[] = [];
    
    // Find expired instances (ES5 compatible)
    this._instanceMetadata.forEach((metadata, instanceId) => {
      if (now - metadata.createdAt.getTime() > this._config.instanceTimeout) {
        expiredInstances.push(instanceId);
      }
    });
    
    // Cleanup expired instances
    for (const instanceId of expiredInstances) {
      const instance = this._instances.get(instanceId);
      if (instance) {
        try {
          await this.releaseOptimizationEngine(instance);
        } catch (error) {
          this._errorCount++;
        }
      }
    }
  }

  private _startMonitoring(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performMonitoring();
      },
      60000, // Monitor every minute
      'GovernanceRuleOptimizationEngineFactory',
      'monitoring'
    );
  }

  private _stopMonitoring(): void {
    // ✅ TIMER COORDINATION: Cleanup now handled automatically by TimerCoordinationService
  }

  private _performMonitoring(): void {
    // Basic health monitoring
    // In a real implementation, this would include more sophisticated monitoring
    const metrics = this.getMetrics();
    
    // Log metrics or send to monitoring system
    if (metrics.errorCount > 10) {
      // High error rate detected
    }
    
    if (metrics.activeInstances === 0 && this._instancesCreated > 0) {
      // No active instances but factory has been used
    }
  }

  /**
   * Static method to create a single optimization engine instance
   * Convenience method for simple use cases
   */
  public static async createOptimizationEngine(config?: TOptimizationEngineConfig): Promise<IOptimizationService> {
    const factory = await GovernanceRuleOptimizationEngineFactory.getInstance();
    return factory.createOptimizationEngine(config);
  }

  /**
   * Static method to release an optimization engine instance
   * Convenience method for simple use cases
   */
  public static async releaseOptimizationEngine(instance: IOptimizationService): Promise<void> {
    const factory = await GovernanceRuleOptimizationEngineFactory.getInstance();
    return factory.releaseOptimizationEngine(instance);
  }
} 