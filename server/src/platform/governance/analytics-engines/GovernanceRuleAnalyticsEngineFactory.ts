/**
 * @file Governance Rule Analytics Engine Factory
 * @filepath server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngineFactory.ts
 * @task-id G-TSK-06.SUB-06.1.IMP-02
 * @component governance-rule-analytics-engine-factory
 * @reference foundation-context.ANALYTICS.002
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Analytics
 * @created 2025-07-01 05:12:30 +03
 * @modified 2025-07-01 05:12:30 +03
 * 
 * @description
 * Factory for creating and managing Governance Rule Analytics Engines
 * providing centralized instantiation and lifecycle management.
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level component-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-tracking-architecture
 * @governance-dcr DCR-foundation-001-tracking-development
 * @governance-status active
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/analytics-engines/GovernanceRuleAnalyticsEngine
 * @depends-on shared/src/types/platform/governance/automation-processing-types
 * @enables server/src/platform/governance/automation-processing
 * @related-contexts foundation-context, governance-context
 * @governance-impact analytics-tracking, governance-analytics
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type analytics-factory
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/analytics/analytics-engine-factory.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { GovernanceRuleAnalyticsEngine } from './GovernanceRuleAnalyticsEngine';
import {
  IAnalyticsEngine,
  IAnalyticsService,
  TAnalyticsEngineData
} from './GovernanceRuleAnalyticsEngine';

import {
  IGovernanceService
} from '../../../../../shared/src/types/platform/governance/automation-processing-types';

import {
  TValidationResult,
  TAuthorityData,
  TMetrics,
  TPerformanceMetrics,
  TUsageMetrics,
  TErrorMetrics
} from '../../../../../shared/src/types/platform/tracking/tracking-types';

import { getTimerCoordinator } from '../../../../../shared/src/base/TimerCoordinationService';

/**
 * Analytics Engine Factory Configuration
 */
export type TAnalyticsEngineFactoryConfig = {
  /** Maximum number of engine instances */
  maxInstances: number;
  
  /** Instance cleanup interval */
  cleanupInterval: number;
  
  /** Enable instance pooling */
  enablePooling: boolean;
  
  /** Default engine configuration */
  defaultConfig: Record<string, any>;
  
  /** Factory monitoring enabled */
  monitoringEnabled: boolean;
};

/**
 * Analytics Engine Instance Data
 */
export type TAnalyticsEngineInstance = {
  /** Instance identifier */
  instanceId: string;
  
  /** Engine instance */
  engine: GovernanceRuleAnalyticsEngine;
  
  /** Creation timestamp */
  createdAt: Date;
  
  /** Last accessed timestamp */
  lastAccessed: Date;
  
  /** Instance status */
  status: 'active' | 'idle' | 'disposed';
  
  /** Instance metadata */
  metadata: Record<string, any>;
};

/**
 * Factory Statistics
 */
export type TFactoryStatistics = {
  /** Total instances created */
  totalInstancesCreated: number;
  
  /** Active instances count */
  activeInstances: number;
  
  /** Disposed instances count */
  disposedInstances: number;
  
  /** Factory uptime */
  uptime: number;
  
  /** Last cleanup timestamp */
  lastCleanup: Date;
};

/**
 * Governance Rule Analytics Engine Factory
 * 
 * Provides centralized creation and management of analytics engines with:
 * - Instance pooling and lifecycle management
 * - Performance monitoring and optimization
 * - Resource management and cleanup
 * - Configuration management
 * - Health monitoring and diagnostics
 * 
 * @implements {IGovernanceService}
 * @authority President & CEO, E.Z. Consultancy
 * @compliance OA Framework Development Standards v21
 */
export class GovernanceRuleAnalyticsEngineFactory implements IGovernanceService {
  // ============================================================================
  // CORE PROPERTIES AND IDENTIFICATION
  // ============================================================================

  /** Component identification */
  private readonly _componentId: string = 'governance-rule-analytics-engine-factory';
  private readonly _version: string = '1.0.0';
  private readonly _componentType: string = 'analytics-engine-factory';

  /** Factory data management */
  private _instances: Map<string, TAnalyticsEngineInstance> = new Map();
  private _instancePool: GovernanceRuleAnalyticsEngine[] = [];
  private _factoryStatistics: TFactoryStatistics = {
    totalInstancesCreated: 0,
    activeInstances: 0,
    disposedInstances: 0,
    uptime: Date.now(),
    lastCleanup: new Date()
  };

  /** Configuration and settings */
  private _config: TAnalyticsEngineFactoryConfig = this._getDefaultConfig();
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService
  private _isInitialized: boolean = false;

  // ============================================================================
  // GOVERNANCE SERVICE INTERFACE
  // ============================================================================

  /** Service identification */
  public readonly id: string = this._componentId;

  /** Authority validation */
  public readonly authority: string = 'President & CEO, E.Z. Consultancy';

  /**
   * Initialize analytics engine factory
   */
  async initialize(): Promise<void> {
    if (this._isInitialized) {
      throw new Error('Analytics Engine Factory already initialized');
    }

    // Initialize factory configuration
    this._config = this._getDefaultConfig();
    
    // Initialize factory statistics
    this._factoryStatistics = {
      totalInstancesCreated: 0,
      activeInstances: 0,
      disposedInstances: 0,
      uptime: Date.now(),
      lastCleanup: new Date()
    };

    // Start cleanup timer if enabled
    if (this._config.enablePooling) {
      this._startCleanupTimer();
    }

    this._isInitialized = true;
  }

  /**
   * Validate factory state and configuration
   */
  async validate(): Promise<TValidationResult> {
    const validationId = this._generateId();
    const timestamp = new Date();
    const checks: any[] = [];
    const warnings: string[] = [];
    const errors: string[] = [];

    // Validate factory initialization
    if (!this._isInitialized) {
      errors.push('Analytics Engine Factory not initialized');
    }

    // Validate instance pool
    if (this._config.enablePooling && this._instancePool.length === 0) {
      warnings.push('Instance pool is empty - may impact performance');
    }

    // Validate active instances
    const instanceArray = Array.from(this._instances.values());
    const activeInstances = instanceArray.filter(instance => instance.status === 'active');
    
    if (activeInstances.length > this._config.maxInstances) {
      errors.push(`Too many active instances: ${activeInstances.length}/${this._config.maxInstances}`);
    }

    // Add validation checks
    checks.push({
      checkId: 'factory-initialization',
      name: 'Factory Initialization',
      type: 'initialization',
      status: this._isInitialized ? 'passed' : 'failed',
      score: this._isInitialized ? 100 : 0,
      details: `Factory initialized: ${this._isInitialized}`,
      timestamp
    });

    checks.push({
      checkId: 'instance-management',
      name: 'Instance Management',
      type: 'performance',
      status: activeInstances.length <= this._config.maxInstances ? 'passed' : 'failed',
      score: activeInstances.length <= this._config.maxInstances ? 100 : 50,
      details: `Active instances: ${activeInstances.length}/${this._config.maxInstances}`,
      timestamp
    });

    const overallScore = checks.reduce((sum, check) => sum + check.score, 0) / checks.length;

    return {
      validationId,
      componentId: this._componentId,
      timestamp,
      executionTime: 0,
      status: errors.length === 0 ? 'valid' : 'invalid',
      overallScore,
      checks,
      references: {
        componentId: this._componentId,
        internalReferences: [],
        externalReferences: [],
        circularReferences: [],
        missingReferences: [],
        redundantReferences: [],
        metadata: {
          totalReferences: 0,
          buildTimestamp: timestamp,
          analysisDepth: 1
        }
      },
      recommendations: this._generateRecommendations(),
      warnings,
      errors,
      metadata: {
        validationMethod: 'analytics-engine-factory-validation',
        rulesApplied: checks.length,
        dependencyDepth: 1,
        cyclicDependencies: [],
        orphanReferences: []
      }
    };
  }

  /**
   * Get factory metrics
   */
  async getMetrics(): Promise<TMetrics> {
    const factoryStats = this.getFactoryStatistics();
    
    return {
      timestamp: new Date().toISOString(),
      service: this._componentId,
      performance: {
        queryExecutionTimes: [],
        cacheOperationTimes: [],
        memoryUtilization: [],
        throughputMetrics: [factoryStats.totalInstancesCreated],
        errorRates: []
      },
      usage: {
        totalOperations: factoryStats.totalInstancesCreated,
        successfulOperations: factoryStats.totalInstancesCreated - factoryStats.disposedInstances,
        failedOperations: 0,
        activeUsers: factoryStats.activeInstances,
        peakConcurrentUsers: this._config.maxInstances
      },
      errors: {
        totalErrors: 0,
        errorRate: 0,
        errorsByType: {},
        recentErrors: []
      },
      custom: {
        totalInstancesCreated: factoryStats.totalInstancesCreated,
        activeInstances: factoryStats.activeInstances,
        disposedInstances: factoryStats.disposedInstances,
        factoryUptime: factoryStats.uptime,
        poolingEnabled: this._config.enablePooling ? 1 : 0,
        maxInstances: this._config.maxInstances
      }
    };
  }

  /**
   * Check if factory is ready
   */
  isReady(): boolean {
    return this._isInitialized && 
           this._instances.size < this._config.maxInstances;
  }

  /**
   * Shutdown factory and cleanup resources
   */
  async shutdown(): Promise<void> {
    // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

    // Dispose all active instances
    const instanceArray = Array.from(this._instances.values());
    for (const instance of instanceArray) {
      if (instance.status === 'active') {
        await instance.engine.shutdown();
        instance.status = 'disposed';
      }
    }

    // Clear instance pool
    for (const engine of this._instancePool) {
      await engine.shutdown();
    }
    this._instancePool.length = 0;

    // Clear instances
    this._instances.clear();

    this._isInitialized = false;
  }

  // ============================================================================
  // ANALYTICS ENGINE FACTORY METHODS
  // ============================================================================

  /**
   * Create new analytics engine instance
   */
  async createAnalyticsEngine(): Promise<GovernanceRuleAnalyticsEngine> {
    if (!this._isInitialized) {
      throw new Error('Factory not initialized');
    }

    // Check instance limits
    if (this._instances.size >= this._config.maxInstances) {
      throw new Error(`Maximum instances reached: ${this._config.maxInstances}`);
    }

    let engine: GovernanceRuleAnalyticsEngine;

    // Try to get instance from pool
    if (this._config.enablePooling && this._instancePool.length > 0) {
      engine = this._instancePool.pop()!;
    } else {
      // Create new instance
      engine = new GovernanceRuleAnalyticsEngine();
      await engine.initialize();
    }

    // Create instance record
    const instanceId = this._generateId();
    const instance: TAnalyticsEngineInstance = {
      instanceId,
      engine,
      createdAt: new Date(),
      lastAccessed: new Date(),
      status: 'active',
      metadata: {
        pooled: this._config.enablePooling,
        version: this._version
      }
    };

    this._instances.set(instanceId, instance);
    this._factoryStatistics.totalInstancesCreated++;
    this._factoryStatistics.activeInstances++;

    return engine;
  }

  /**
   * Get analytics service interface
   */
  async getAnalyticsService(): Promise<IAnalyticsService> {
    const engine = await this.createAnalyticsEngine();
    return engine as IAnalyticsService;
  }

  /**
   * Get analytics engine interface
   */
  async getAnalyticsEngine(): Promise<IAnalyticsEngine> {
    const engine = await this.createAnalyticsEngine();
    return engine as IAnalyticsEngine;
  }

  /**
   * Release analytics engine instance
   */
  async releaseAnalyticsEngine(engine: GovernanceRuleAnalyticsEngine): Promise<void> {
    // Find instance
    const instanceArray = Array.from(this._instances.values());
    const instance = instanceArray.find(inst => inst.engine === engine);

    if (!instance) {
      throw new Error('Analytics engine instance not found');
    }

    // Update instance status
    instance.status = 'idle';
    instance.lastAccessed = new Date();

    // Return to pool if pooling enabled
    if (this._config.enablePooling && this._instancePool.length < this._config.maxInstances) {
      this._instancePool.push(engine);
    } else {
      // Dispose instance
      await engine.shutdown();
      instance.status = 'disposed';
      this._factoryStatistics.disposedInstances++;
    }

    this._factoryStatistics.activeInstances--;
    this._instances.delete(instance.instanceId);
  }

  /**
   * Get factory statistics
   */
  getFactoryStatistics(): TFactoryStatistics {
    return {
      ...this._factoryStatistics,
      uptime: Date.now() - this._factoryStatistics.uptime
    };
  }

  /**
   * Get factory configuration
   */
  getFactoryConfiguration(): TAnalyticsEngineFactoryConfig {
    return { ...this._config };
  }

  /**
   * Update factory configuration
   */
  async updateFactoryConfiguration(config: Partial<TAnalyticsEngineFactoryConfig>): Promise<void> {
    this._config = { ...this._config, ...config };

    // ✅ TIMER COORDINATION: Timer restart logic now handled automatically by TimerCoordinationService
    // Configuration changes will be applied to new timer instances automatically
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Get default factory configuration
   */
  private _getDefaultConfig(): TAnalyticsEngineFactoryConfig {
    return {
      maxInstances: 10,
      cleanupInterval: 300000, // 5 minutes
      enablePooling: true,
      defaultConfig: {},
      monitoringEnabled: true
    };
  }

  /**
   * Start cleanup timer
   */
  private _startCleanupTimer(): void {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      () => {
        this._performCleanup();
      },
      this._config.cleanupInterval,
      'GovernanceRuleAnalyticsEngineFactory',
      'cleanup'
    );
  }

  /**
   * Perform instance cleanup
   */
  private _performCleanup(): void {
    const now = new Date();
    const cleanupThreshold = now.getTime() - (this._config.cleanupInterval * 2);

    // Cleanup idle instances
    const instanceEntries = Array.from(this._instances.entries());
    for (const [instanceId, instance] of instanceEntries) {
      if (instance.status === 'idle' && 
          instance.lastAccessed.getTime() < cleanupThreshold) {
        
        // Dispose old idle instance
        instance.engine.shutdown().catch(console.error);
        instance.status = 'disposed';
        this._factoryStatistics.disposedInstances++;
        this._instances.delete(instanceId);
      }
    }

    // Cleanup pool instances
    const poolCleanupThreshold = now.getTime() - (this._config.cleanupInterval * 4);
    while (this._instancePool.length > 0) {
      const engine = this._instancePool[0];
      // Note: In a real implementation, you'd track creation time
      // For now, we'll just limit pool size
      if (this._instancePool.length > Math.floor(this._config.maxInstances / 2)) {
        this._instancePool.shift();
        engine.shutdown().catch(console.error);
      } else {
        break;
      }
    }

    this._factoryStatistics.lastCleanup = now;
  }

  /**
   * Generate unique identifier
   */
  private _generateId(): string {
    return `analytics_factory_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Generate validation recommendations
   */
  private _generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this._instances.size === 0) {
      recommendations.push('Consider pre-creating analytics engine instances for better performance');
    }

    if (!this._config.enablePooling) {
      recommendations.push('Enable instance pooling to improve resource utilization');
    }

    if (this._config.maxInstances < 5) {
      recommendations.push('Consider increasing maximum instances for better scalability');
    }

    return recommendations;
  }
}

/**
 * Export singleton factory instance
 */
export const analyticsEngineFactory = new GovernanceRuleAnalyticsEngineFactory();