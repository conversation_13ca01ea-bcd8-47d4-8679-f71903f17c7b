/**
 * @file GovernanceRuleTemplateSecurity
 * @filepath server/src/platform/governance/management-configuration/GovernanceRuleTemplateSecurity.ts
 * @reference G-TSK-07.SUB-07.2.IMP-01
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-07-04
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-template-security-architecture
 * @governance-dcr DCR-foundation-008-template-security-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/tracking/core-data/base/BaseTrackingService
 * @depends-on shared/src/types/platform/governance/security-types
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine
 * @related-contexts foundation-context, governance-context, security-context
 * @governance-impact framework-foundation, template-security
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-template-security
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/governance/GovernanceRuleTemplateSecurity.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { 
  ITemplateSecurity,
  TSecurityValidationResult,
  TSecurityConfig,
  TSecurityMetrics,
  TSecurityAuditLogEntry,
  TTemplateSecurityContext,
  TInjectionThreat,
  TSecurityPolicyDefinition,
  TSanitizationRule,
  TSecurityViolation
} from '../../../../../shared/src/types/platform/governance/security-types';
import { TValidationResult } from '../../../../../shared/src/types/platform/tracking/tracking-types';
import * as crypto from 'crypto';

/**
 * Template Security Validator
 * Implements comprehensive security validation for governance rule templates
 */
export class GovernanceRuleTemplateSecurity 
  extends BaseTrackingService 
  implements ITemplateSecurity {

  private readonly _componentId = 'governance-rule-template-security';
  private readonly _securityConfig: TSecurityConfig;
  private readonly _auditLog: TSecurityAuditLogEntry[] = [];
  private readonly _threatPatterns: Map<string, RegExp> = new Map();
  private readonly _sanitizationRules: TSanitizationRule[] = [];
  private _templateSecurityMetrics: TSecurityMetrics;

  constructor() {
    super();
    
    this._securityConfig = this._initializeSecurityConfig();
    this._templateSecurityMetrics = this._initializeTemplateSecurityMetrics();
    this._initializeThreatPatterns();
    this._initializeSanitizationRules();
    
    this.logOperation('security-initialization', 'Template security validator initialized');
  }

  // ============================================================================
  // BASE TRACKING SERVICE IMPLEMENTATION
  // ============================================================================

  protected getServiceName(): string {
    return 'governance-rule-template-security';
  }

  protected getServiceVersion(): string {
    return '1.0.0';
  }

  protected async doInitialize(): Promise<void> {
    // Initialize template security validator
    this.logOperation('template-security-do-initialize', 'Template security validator initialized');
  }

  protected async doTrack(data: any): Promise<void> {
    // Track template security events
    this.logOperation('template-security-track', 'Template security tracking data', data);
  }

  protected async doValidate(): Promise<any> {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      metadata: {
        componentId: this._componentId,
        timestamp: new Date(),
        validationType: 'template-security-validation'
      }
    };
  }

  protected async doShutdown(): Promise<void> {
    // Cleanup template security validator
    this._auditLog.length = 0;
    this._threatPatterns.clear();
    this._sanitizationRules.length = 0;
    this.logOperation('template-security-shutdown', 'Template security validator shutdown');
  }

  // ============================================================================
  // SECURITY SERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Get current security configuration
   */
  public getSecurityConfig(): TSecurityConfig {
    return { ...this._securityConfig };
  }

  /**
   * Update security configuration
   */
  public async updateSecurityConfig(config: Partial<TSecurityConfig>): Promise<void> {
    try {
      Object.assign(this._securityConfig, config);
      
      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'configuration',
        action: 'security_config_updated',
        resource: this._componentId,
        result: 'success',
        details: { updatedFields: Object.keys(config) },
        riskLevel: 'low'
      });
      
      this.logOperation('security-config-update', 'Security configuration updated', config);
    } catch (error) {
      this.logOperation('security-config-update-error', 'Failed to update security configuration', error);
      throw error;
    }
  }

  /**
   * Validate security compliance
   */
  public async validateSecurityCompliance(): Promise<boolean> {
    try {
      const checks = [
        this._securityConfig.xssProtection,
        this._securityConfig.csrfProtection,
        this._securityConfig.contentSecurityPolicy,
        this._securityConfig.inputSanitization,
        this._securityConfig.threatDetection,
        this._securityConfig.auditLogging
      ];

      const complianceScore = checks.filter(Boolean).length / checks.length;
      const isCompliant = complianceScore >= 0.8; // 80% compliance threshold

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'configuration',
        action: 'compliance_check',
        resource: this._componentId,
        result: isCompliant ? 'success' : 'failure',
        details: { complianceScore, checks },
        riskLevel: isCompliant ? 'low' : 'high'
      });

      return isCompliant;
    } catch (error) {
      this.logOperation('compliance-check-error', 'Security compliance check failed', error);
      return false;
    }
  }

  /**
   * Generate security report
   */
  public async generateSecurityReport(): Promise<TSecurityMetrics> {
    try {
      const currentTime = new Date();
      const report: TSecurityMetrics = {
        ...this._templateSecurityMetrics,
        timestamp: currentTime,
        period: 'hour',
        securityScore: await this._calculateSecurityScore()
      };

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: currentTime,
        eventType: 'access',
        action: 'security_report_generated',
        resource: this._componentId,
        result: 'success',
        details: { reportPeriod: report.period },
        riskLevel: 'low'
      });

      return report;
    } catch (error) {
      this.logOperation('security-report-error', 'Failed to generate security report', error);
      throw error;
    }
  }

  /**
   * Audit security event
   */
  public async auditSecurityEvent(event: TSecurityAuditLogEntry): Promise<void> {
    try {
      this._auditLog.push(event);
      
      // Keep only last 1000 entries to prevent memory issues
      if (this._auditLog.length > 1000) {
        this._auditLog.splice(0, this._auditLog.length - 1000);
      }

      this.logOperation('security-audit', 'Security event audited', {
        eventType: event.eventType,
        action: event.action,
        result: event.result,
        riskLevel: event.riskLevel
      });
    } catch (error) {
      this.logOperation('security-audit-error', 'Failed to audit security event', error);
      throw error;
    }
  }

  // ============================================================================
  // TEMPLATE SECURITY INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Validate template security
   */
  public async validateTemplateSecurity(source: string): Promise<TSecurityValidationResult> {
    try {
      // Handle null/undefined input gracefully
      if (source === null || source === undefined) {
        return {
          isSecure: false,
          threats: ['Input cannot be null or undefined'],
          warnings: [],
          recommendedActions: ['Provide valid template source'],
          riskLevel: 'medium',
          scanTimestamp: new Date(),
          validationContext: 'template_security_validation',
          threatCategories: ['invalid_input'],
          securityScore: 0,
          complianceStatus: 'non-compliant'
        };
      }

      // Convert to string if needed
      const sourceStr = String(source);
      
      const threats: string[] = [];
      const warnings: string[] = [];
      const recommendedActions: string[] = [];
      const threatCategories: string[] = [];
      let securityScore = 100;

      // Check for dangerous patterns
      this._threatPatterns.forEach((pattern, category) => {
        if (pattern.test(sourceStr)) {
          threats.push(`Potential ${category} threat detected`);
          threatCategories.push(category);
          securityScore -= 20;
        }
      });

      // Check for template injection patterns
      const templateThreats = await this._detectTemplateInjection(sourceStr);
      if (templateThreats.length > 0) {
        threats.push(...templateThreats.map(t => t.description));
        threatCategories.push('template_injection');
        securityScore -= templateThreats.length * 15;
      }

      // Check for XSS patterns
      const xssThreats = await this._detectXSSPatterns(sourceStr);
      if (xssThreats.length > 0) {
        threats.push(...xssThreats.map(t => t.description));
        threatCategories.push('xss');
        securityScore -= xssThreats.length * 25;
      }

      // Generate recommendations
      if (threats.length > 0) {
        recommendedActions.push('Sanitize template input');
        recommendedActions.push('Review template source for security issues');
        recommendedActions.push('Apply input validation rules');
      }

      const result: TSecurityValidationResult = {
        isSecure: threats.length === 0,
        threats,
        warnings,
        recommendedActions,
        riskLevel: this._calculateRiskLevel(securityScore),
        scanTimestamp: new Date(),
        validationContext: 'template_security_validation',
        threatCategories: Array.from(new Set(threatCategories)),
        securityScore: Math.max(0, securityScore),
        complianceStatus: threats.length === 0 ? 'compliant' : 'non-compliant'
      };

      // Update metrics
      this._templateSecurityMetrics.threatsDetected += threats.length;
      this._templateSecurityMetrics.totalRequests++;

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'template_security_validation',
        resource: 'template',
        result: result.isSecure ? 'success' : 'warning',
        details: { threatsFound: threats.length, securityScore: result.securityScore },
        riskLevel: result.riskLevel
      });

      return result;
    } catch (error) {
      this.logOperation('template-security-validation-error', 'Template security validation failed', error);
      throw error;
    }
  }

  /**
   * Sanitize template output
   */
  public async sanitizeTemplateOutput(output: string): Promise<string> {
    try {
      if (!this._securityConfig.inputSanitization) {
        return output;
      }

      let sanitizedOutput = output;

      // Apply sanitization rules
      for (const rule of this._sanitizationRules) {
        if (rule.enabled) {
          const regex = new RegExp(rule.pattern, rule.flags);
          sanitizedOutput = sanitizedOutput.replace(regex, rule.replacement);
        }
      }

      // Basic XSS protection
      sanitizedOutput = sanitizedOutput
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;')
        .replace(/`/g, '&#x60;')
        .replace(/=/g, '&#x3D;');

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'template_output_sanitized',
        resource: 'template_output',
        result: 'success',
        details: { originalLength: output.length, sanitizedLength: sanitizedOutput.length },
        riskLevel: 'low'
      });

      return sanitizedOutput;
    } catch (error) {
      this.logOperation('template-sanitization-error', 'Template output sanitization failed', error);
      throw error;
    }
  }

  /**
   * Generate CSP headers
   */
  public async generateCSPHeaders(context: TTemplateSecurityContext): Promise<Record<string, string>> {
    try {
      const headers: Record<string, string> = {
        'Content-Security-Policy': this._buildCSPDirective(context),
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
      };

      // Add custom security headers from config
      Object.assign(headers, this._securityConfig.securityHeaders);

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'configuration',
        action: 'csp_headers_generated',
        resource: context.templateId,
        result: 'success',
        details: { securityLevel: context.securityLevel },
        riskLevel: 'low'
      });

      return headers;
    } catch (error) {
      this.logOperation('csp-generation-error', 'CSP header generation failed', error);
      throw error;
    }
  }

  /**
   * Detect threats in input
   */
  public async detectThreats(input: string): Promise<TInjectionThreat[]> {
    try {
      const threats: TInjectionThreat[] = [];

      // Check for various injection types
      const injectionChecks = [
        { type: 'sql', patterns: [/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER)\b)/gi] },
        { type: 'xss', patterns: [/<script[^>]*>.*?<\/script>/gi, /javascript:/gi, /on\w+\s*=/gi] },
        { type: 'template', patterns: [/{{.*constructor.*}}/gi, /{{.*process.*}}/gi, /{{.*require.*}}/gi] },
        { type: 'command', patterns: [/(\||;|&|`|\$\(|\${)/g] },
        { type: 'code', patterns: [/eval\s*\(/gi, /Function\s*\(/gi, /setTimeout\s*\(/gi] }
      ];

      for (const check of injectionChecks) {
        for (const pattern of check.patterns) {
          const matches = input.match(pattern);
          if (matches) {
            for (const match of matches) {
              threats.push({
                threatId: crypto.randomUUID(),
                type: check.type as any,
                pattern: pattern.source,
                severity: this._calculateThreatSeverity(check.type, match),
                description: `Potential ${check.type} injection detected: ${match}`,
                detectedAt: new Date(),
                input: match,
                blocked: true,
                confidence: this._calculateConfidence(check.type, match)
              });
            }
          }
        }
      }

      // Update metrics
      this._templateSecurityMetrics.threatsDetected += threats.length;
      threats.forEach(threat => {
        this._templateSecurityMetrics.threatsByType[threat.type] = 
          (this._templateSecurityMetrics.threatsByType[threat.type] || 0) + 1;
      });

      return threats;
    } catch (error) {
      this.logOperation('threat-detection-error', 'Threat detection failed', error);
      throw error;
    }
  }

  /**
   * Apply security policy
   */
  public async applySecurityPolicy(templateId: string, policy: TSecurityPolicyDefinition): Promise<void> {
    try {
      // Validate policy
      if (!policy.enabled) {
        this.logOperation('security-policy-disabled', 'Security policy is disabled', { policyId: policy.policyId });
        return;
      }

      // Apply policy rules
      for (const rule of policy.rules) {
        if (rule.enabled) {
          await this._applySecurityRule(templateId, rule);
        }
      }

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'configuration',
        action: 'security_policy_applied',
        resource: templateId,
        result: 'success',
        details: { policyId: policy.policyId, rulesApplied: policy.rules.filter(r => r.enabled).length },
        riskLevel: 'low'
      });

      this.logOperation('security-policy-applied', 'Security policy applied successfully', {
        templateId,
        policyId: policy.policyId
      });
    } catch (error) {
      this.logOperation('security-policy-error', 'Failed to apply security policy', error);
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Initialize security configuration
   */
  private _initializeSecurityConfig(): TSecurityConfig {
    return {
      xssProtection: true,
      csrfProtection: true,
      contentSecurityPolicy: true,
      inputSanitization: true,
      threatDetection: true,
      auditLogging: true,
      maxTokenAge: 3600000, // 1 hour
      tokenCleanupInterval: 300000, // 5 minutes
      maxFailedAttempts: 5,
      lockoutDuration: 900000, // 15 minutes
      securityHeaders: {},
      allowedOrigins: ['self'],
      blockedPatterns: [
        '<script',
        'javascript:',
        'on\\w+\\s*=',
        'eval\\s*\\(',
        'Function\\s*\\(',
        'constructor\\.constructor'
      ]
    };
  }

  /**
   * Initialize threat detection patterns
   */
  private _initializeThreatPatterns(): void {
    this._threatPatterns.set('script_injection', /<script[^>]*>.*?<\/script>/gi);
    this._threatPatterns.set('javascript_protocol', /javascript:/gi);
    this._threatPatterns.set('event_handler', /on\w+\s*=/gi);
    this._threatPatterns.set('eval_function', /eval\s*\(/gi);
    this._threatPatterns.set('function_constructor', /Function\s*\(/gi);
    this._threatPatterns.set('constructor_access', /constructor\.constructor/gi);
    this._threatPatterns.set('document_cookie', /document\.cookie/gi);
    this._threatPatterns.set('window_location', /window\.location/gi);
    this._threatPatterns.set('inner_html', /innerHTML/gi);
    this._threatPatterns.set('outer_html', /outerHTML/gi);
  }

  /**
   * Initialize sanitization rules
   */
  private _initializeSanitizationRules(): void {
    this._sanitizationRules.push(
      {
        ruleId: 'remove-script-tags',
        name: 'Remove Script Tags',
        type: 'html',
        pattern: '<script[^>]*>.*?</script>',
        replacement: '',
        flags: 'gis',
        enabled: true,
        priority: 1,
        description: 'Remove all script tags'
      },
      {
        ruleId: 'block-javascript-protocol',
        name: 'Block JavaScript Protocol',
        type: 'javascript',
        pattern: 'javascript:',
        replacement: 'javascript-blocked:',
        flags: 'gi',
        enabled: true,
        priority: 2,
        description: 'Block javascript: protocol'
      },
      {
        ruleId: 'remove-event-handlers',
        name: 'Remove Event Handlers',
        type: 'html',
        pattern: 'on\\w+\\s*=',
        replacement: 'on-event-blocked=',
        flags: 'gi',
        enabled: true,
        priority: 3,
        description: 'Remove HTML event handlers'
      }
    );
  }

  /**
   * Initialize template security metrics
   */
  private _initializeTemplateSecurityMetrics(): TSecurityMetrics {
    return {
      timestamp: new Date(),
      period: 'hour',
      totalRequests: 0,
      blockedRequests: 0,
      threatsDetected: 0,
      violationsLogged: 0,
      securityScore: 100,
      threatsByType: {},
      violationsByType: {},
      topThreats: [],
      riskTrends: []
    };
  }

  /**
   * Calculate security score
   */
  private async _calculateSecurityScore(): Promise<number> {
    const baseScore = 100;
    const threatPenalty = this._templateSecurityMetrics.threatsDetected * 2;
    const violationPenalty = this._templateSecurityMetrics.violationsLogged * 5;
    const complianceBonus = (await this.validateSecurityCompliance()) ? 10 : 0;

    return Math.max(0, Math.min(100, baseScore - threatPenalty - violationPenalty + complianceBonus));
  }

  /**
   * Calculate risk level based on security score
   */
  private _calculateRiskLevel(securityScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (securityScore >= 80) return 'low';
    if (securityScore >= 60) return 'medium';
    if (securityScore >= 40) return 'high';
    return 'critical';
  }

  /**
   * Detect template injection patterns
   */
  private async _detectTemplateInjection(source: string): Promise<TInjectionThreat[]> {
    // Handle null/undefined input gracefully
    if (source === null || source === undefined) {
      return [];
    }

    const sourceStr = String(source);
    const threats: TInjectionThreat[] = [];
    const patterns = [
      /{{.*constructor.*}}/gi,
      /{{.*process.*}}/gi,
      /{{.*require.*}}/gi,
      /{{.*global.*}}/gi,
      /{{.*__proto__.*}}/gi,
      /{{.*prototype.*}}/gi
    ];

    for (const pattern of patterns) {
      const matches = sourceStr.match(pattern);
      if (matches) {
        for (const match of matches) {
          threats.push({
            threatId: crypto.randomUUID(),
            type: 'template',
            pattern: pattern.source,
            severity: 'high',
            description: `Template injection detected: ${match}`,
            detectedAt: new Date(),
            input: match,
            blocked: true,
            confidence: 90
          });
        }
      }
    }

    return threats;
  }

  /**
   * Detect XSS patterns
   */
  private async _detectXSSPatterns(source: string): Promise<TInjectionThreat[]> {
    // Handle null/undefined input gracefully
    if (source === null || source === undefined) {
      return [];
    }

    const sourceStr = String(source);
    const threats: TInjectionThreat[] = [];
    const patterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /eval\s*\(/gi,
      /innerHTML/gi,
      /document\.write/gi
    ];

    for (const pattern of patterns) {
      const matches = sourceStr.match(pattern);
      if (matches) {
        for (const match of matches) {
          threats.push({
            threatId: crypto.randomUUID(),
            type: 'xss',
            pattern: pattern.source,
            severity: 'high',
            description: `XSS pattern detected: ${match}`,
            detectedAt: new Date(),
            input: match,
            blocked: true,
            confidence: 85
          });
        }
      }
    }

    return threats;
  }

  /**
   * Calculate threat severity
   */
  private _calculateThreatSeverity(type: string, match: string): 'low' | 'medium' | 'high' | 'critical' {
    const highRiskPatterns = ['script', 'eval', 'Function', 'constructor'];
    const mediumRiskPatterns = ['javascript:', 'on\\w+', 'innerHTML'];
    
    if (highRiskPatterns.some(pattern => match.toLowerCase().includes(pattern.toLowerCase()))) {
      return 'high';
    }
    if (mediumRiskPatterns.some(pattern => new RegExp(pattern, 'i').test(match))) {
      return 'medium';
    }
    return 'low';
  }

  /**
   * Calculate confidence score
   */
  private _calculateConfidence(type: string, match: string): number {
    const exactMatches = ['<script', 'javascript:', 'eval(', 'Function('];
    if (exactMatches.some(exact => match.toLowerCase().includes(exact.toLowerCase()))) {
      return 95;
    }
    return 75;
  }

  /**
   * Build CSP directive
   */
  private _buildCSPDirective(context: TTemplateSecurityContext): string {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "connect-src 'self'",
      "font-src 'self' https:",
      "object-src 'none'",
      "media-src 'self'",
      "frame-src 'none'"
    ];

    // Add trusted sources
    if (context.trustedSources.length > 0) {
      directives.push(`trusted-types ${context.trustedSources.join(' ')}`);
    }

    // Add custom CSP directives
    Object.entries(context.cspDirectives).forEach(([key, value]) => {
      directives.push(`${key} ${value}`);
    });

    return directives.join('; ') + ';';
  }

  /**
   * Apply security rule
   */
  private async _applySecurityRule(templateId: string, rule: any): Promise<void> {
    // Implementation would depend on specific rule types
    this.logOperation('security-rule-applied', 'Security rule applied', {
      templateId,
      ruleId: rule.ruleId,
      action: rule.action
    });
  }

  /**
   * Log operation with context
   */
  protected logOperation(operation: string, message: string, data?: any): void {
    const logData = {
      operation,
      message,
      componentId: this._componentId,
      timestamp: new Date(),
      data,
    };
    
    // In real implementation, would use proper logging framework
    console.log(`[${this._componentId}] ${message}`, logData);
  }
} 