/**
 * @file GovernanceRuleInputValidator
 * @filepath server/src/platform/governance/management-configuration/GovernanceRuleInputValidator.ts
 * @reference G-TSK-07.SUB-07.2.IMP-04
 * @template templates/contexts/foundation-context/components/component-header-standard.template
 * @tier T2
 * @context foundation-context
 * @category Foundation
 * @created 2025-01-27
 * @modified 2025-07-04
 * 
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-009-template-security-architecture
 * @governance-dcr DCR-foundation-008-template-security-standards
 * @governance-status approved
 * @governance-compliance authority-validated
 * 
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on server/src/platform/governance/tracking/core-data/base/BaseTrackingService
 * @depends-on shared/src/types/platform/governance/security-types
 * @enables server/src/platform/governance/management-configuration/GovernanceRuleTemplateEngine
 * @related-contexts foundation-context, governance-context, security-context
 * @governance-impact framework-foundation, input-validation
 * 
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type governance-input-validator
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/contexts/foundation-context/governance/GovernanceRuleInputValidator.md
 * 
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 */

import { BaseTrackingService } from '../../tracking/core-data/base/BaseTrackingService';
import { 
  IInputValidator,
  TSecurityValidationResult,
  TSecurityConfig,
  TSecurityMetrics,
  TSecurityAuditLogEntry,
  TInjectionThreat,
  TDataSchema,
  TSchemaField,
  TValidationRule,
  TSanitizationRule
} from '../../../../../shared/src/types/platform/governance/security-types';
import * as crypto from 'crypto';

/**
 * Input Validation Manager
 * Implements comprehensive input validation and sanitization
 */
export class GovernanceRuleInputValidator 
  extends BaseTrackingService 
  implements IInputValidator {

  private readonly _componentId = 'governance-rule-input-validator';
  private readonly _securityConfig: TSecurityConfig;
  private readonly _auditLog: TSecurityAuditLogEntry[] = [];
  private readonly _validationRules: Map<string, TValidationRule> = new Map();
  private readonly _sanitizationRules: Map<string, TSanitizationRule> = new Map();
  private readonly _schemas: Map<string, TDataSchema> = new Map();
  private readonly _injectionPatterns: Map<string, RegExp> = new Map();
  private _inputSecurityMetrics: TSecurityMetrics;

  constructor() {
    super();
    
    this._securityConfig = this._initializeSecurityConfig();
    this._inputSecurityMetrics = this._initializeInputSecurityMetrics();
    this._initializeValidationRules();
    this._initializeSanitizationRules();
    this._initializeInjectionPatterns();
    this._initializeDefaultSchemas();
    
    this.logOperation('input-validator-initialization', 'Input validator initialized');
  }

  // ============================================================================
  // BASE TRACKING SERVICE IMPLEMENTATION
  // ============================================================================

  protected getServiceName(): string {
    return 'governance-rule-input-validator';
  }

  protected getServiceVersion(): string {
    return '1.0.0';
  }

  protected async doInitialize(): Promise<void> {
    // Initialize input validator
    this.logOperation('input-validator-do-initialize', 'Input validator initialized');
  }

  protected async doTrack(data: any): Promise<void> {
    // Track input validation events
    this.logOperation('input-validator-track', 'Input validation tracking data', data);
  }

  protected async doValidate(): Promise<any> {
    return {
      isValid: true,
      errors: [],
      warnings: [],
      metadata: {
        componentId: this._componentId,
        timestamp: new Date(),
        validationType: 'input-validation'
      }
    };
  }

  protected async doShutdown(): Promise<void> {
    // Cleanup input validator
    this._validationRules.clear();
    this._sanitizationRules.clear();
    this._schemas.clear();
    this._injectionPatterns.clear();
    this.logOperation('input-validator-shutdown', 'Input validator shutdown');
  }

  // ============================================================================
  // SECURITY SERVICE INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Get current security configuration
   */
  public getSecurityConfig(): TSecurityConfig {
    return { ...this._securityConfig };
  }

  /**
   * Update security configuration
   */
  public async updateSecurityConfig(config: Partial<TSecurityConfig>): Promise<void> {
    try {
      Object.assign(this._securityConfig, config);
      
      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'configuration',
        action: 'input_validator_config_updated',
        resource: this._componentId,
        result: 'success',
        details: { updatedFields: Object.keys(config) },
        riskLevel: 'low'
      });
      
      this.logOperation('input-validator-config-update', 'Input validator configuration updated', config);
    } catch (error) {
      this.logOperation('input-validator-config-update-error', 'Failed to update input validator configuration', error);
      throw error;
    }
  }

  /**
   * Validate security compliance
   */
  public async validateSecurityCompliance(): Promise<boolean> {
    try {
      const checks = [
        this._securityConfig.inputSanitization,
        this._securityConfig.threatDetection,
        this._securityConfig.auditLogging,
        this._validationRules.size > 0,
        this._sanitizationRules.size > 0,
        this._injectionPatterns.size > 0
      ];

      const complianceScore = checks.filter(Boolean).length / checks.length;
      const isCompliant = complianceScore >= 0.8; // 80% compliance threshold

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'configuration',
        action: 'input_validator_compliance_check',
        resource: this._componentId,
        result: isCompliant ? 'success' : 'failure',
        details: { 
          complianceScore, 
          checks,
          validationRules: this._validationRules.size,
          sanitizationRules: this._sanitizationRules.size,
          injectionPatterns: this._injectionPatterns.size
        },
        riskLevel: isCompliant ? 'low' : 'high'
      });

      return isCompliant;
    } catch (error) {
      this.logOperation('input-validator-compliance-check-error', 'Input validator compliance check failed', error);
      return false;
    }
  }

  /**
   * Generate security report
   */
  public async generateSecurityReport(): Promise<TSecurityMetrics> {
    try {
      const currentTime = new Date();
      const report: TSecurityMetrics = {
        ...this._inputSecurityMetrics,
        timestamp: currentTime,
        period: 'hour',
        securityScore: await this._calculateSecurityScore()
      };

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: currentTime,
        eventType: 'access',
        action: 'input_validator_security_report_generated',
        resource: this._componentId,
        result: 'success',
        details: { 
          validationRules: this._validationRules.size,
          sanitizationRules: this._sanitizationRules.size,
          schemas: this._schemas.size,
          reportPeriod: report.period 
        },
        riskLevel: 'low'
      });

      return report;
    } catch (error) {
      this.logOperation('input-validator-security-report-error', 'Failed to generate input validator security report', error);
      throw error;
    }
  }

  /**
   * Audit security event
   */
  public async auditSecurityEvent(event: TSecurityAuditLogEntry): Promise<void> {
    try {
      this._auditLog.push(event);
      
      // Keep only last 1000 entries to prevent memory issues
      if (this._auditLog.length > 1000) {
        this._auditLog.splice(0, this._auditLog.length - 1000);
      }

      this.logOperation('input-validator-audit', 'Input validator security event audited', {
        eventType: event.eventType,
        action: event.action,
        result: event.result,
        riskLevel: event.riskLevel
      });
    } catch (error) {
      this.logOperation('input-validator-audit-error', 'Failed to audit input validator security event', error);
      throw error;
    }
  }

  // ============================================================================
  // INPUT VALIDATOR INTERFACE IMPLEMENTATION
  // ============================================================================

  /**
   * Validate template input
   */
  public async validateTemplateInput(input: string, schema?: TDataSchema): Promise<TSecurityValidationResult> {
    try {
      // Handle null/undefined input gracefully
      if (input === null || input === undefined) {
        return {
          isSecure: false,
          threats: ['Input cannot be null or undefined'],
          warnings: [],
          recommendedActions: ['Provide valid input'],
          riskLevel: 'medium',
          scanTimestamp: new Date(),
          validationContext: 'template_input_validation',
          threatCategories: ['invalid_input'],
          securityScore: 0,
          complianceStatus: 'non-compliant'
        };
      }

      // Convert to string if needed
      const inputStr = String(input);
      
      const threats: string[] = [];
      const warnings: string[] = [];
      const recommendedActions: string[] = [];
      const threatCategories: string[] = [];
      let securityScore = 100;

      // Detect injection threats
      const injectionThreats = await this.detectInjectionAttempts(inputStr);
      if (injectionThreats.length > 0) {
        threats.push(...injectionThreats.map(t => t.description));
        threatCategories.push(...injectionThreats.map(t => t.type));
        securityScore -= injectionThreats.length * 20;
      }

      // Validate against schema if provided (for template input, we validate the template structure, not the data)
      if (schema) {
        // For template input validation, we check if the template references valid fields
        // Extract template variables from the input
        const templateVars = this._extractTemplateVariables(inputStr);
        const schemaFieldNames = new Set(schema.fields.map(f => f.name));
        
        // Check if all template variables are defined in the schema (ES5 compatible)
        templateVars.forEach((varName) => {
          if (!schemaFieldNames.has(varName)) {
            warnings.push(`Template variable '${varName}' is not defined in schema`);
            securityScore -= 5;
          }
        });
        
        // Check for required fields that are not referenced in the template
        for (const requiredField of schema.required) {
          if (!templateVars.has(requiredField)) {
            warnings.push(`Required field '${requiredField}' is not referenced in template`);
            securityScore -= 3;
          }
        }
      }

      // Check for blocked patterns
      for (const pattern of this._securityConfig.blockedPatterns) {
        const regex = new RegExp(pattern, 'gi');
        if (regex.test(inputStr)) {
          threats.push(`Blocked pattern detected: ${pattern}`);
          threatCategories.push('blocked_pattern');
          securityScore -= 10;
        }
      }

      // Length validation
      if (inputStr.length > 10000) { // 10KB limit
        warnings.push('Input exceeds recommended length limit');
        securityScore -= 5;
      }

      // Generate recommendations
      if (threats.length > 0) {
        recommendedActions.push('Sanitize input before processing');
        recommendedActions.push('Apply input validation rules');
        recommendedActions.push('Consider rejecting malicious input');
      }

      const result: TSecurityValidationResult = {
        isSecure: threats.length === 0,
        threats,
        warnings,
        recommendedActions,
        riskLevel: this._calculateRiskLevel(securityScore),
        scanTimestamp: new Date(),
        validationContext: 'template_input_validation',
        threatCategories: Array.from(new Set(threatCategories)),
        securityScore: Math.max(0, securityScore),
        complianceStatus: threats.length === 0 ? 'compliant' : 'non-compliant'
      };

      // Update metrics
      this._inputSecurityMetrics.threatsDetected += injectionThreats.length;
      this._inputSecurityMetrics.totalRequests++;

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'template_input_validated',
        resource: 'template_input',
        result: result.isSecure ? 'success' : 'warning',
        details: { 
          inputLength: inputStr.length,
          threatsFound: threats.length,
          securityScore: result.securityScore,
          schemaUsed: !!schema
        },
        riskLevel: result.riskLevel
      });

      return result;
    } catch (error) {
      this.logOperation('template-input-validation-error', 'Template input validation failed', error);
      throw error;
    }
  }

  /**
   * Sanitize user input
   */
  public async sanitizeUserInput(input: any, rules?: TSanitizationRule[]): Promise<any> {
    try {
      if (!this._securityConfig.inputSanitization) {
        return input;
      }

      const sanitizationRules = rules || Array.from(this._sanitizationRules.values());
      let sanitizedInput = input;

      // Handle different input types
      if (typeof input === 'string') {
        sanitizedInput = await this._sanitizeString(input, sanitizationRules);
      } else if (typeof input === 'object' && input !== null) {
        sanitizedInput = await this._sanitizeObject(input, sanitizationRules);
      } else if (Array.isArray(input)) {
        sanitizedInput = await this._sanitizeArray(input, sanitizationRules);
      }

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'user_input_sanitized',
        resource: 'user_input',
        result: 'success',
        details: { 
          inputType: typeof input,
          rulesApplied: sanitizationRules.filter(r => r.enabled).length,
          inputChanged: JSON.stringify(input) !== JSON.stringify(sanitizedInput)
        },
        riskLevel: 'low'
      });

      this.logOperation('user-input-sanitized', 'User input sanitized successfully', {
        inputType: typeof input,
        rulesApplied: sanitizationRules.filter(r => r.enabled).length
      });

      return sanitizedInput;
    } catch (error) {
      this.logOperation('user-input-sanitization-error', 'User input sanitization failed', error);
      throw error;
    }
  }

  /**
   * Validate data schema
   */
  public async validateDataSchema(data: any, schema: TDataSchema): Promise<TSecurityValidationResult> {
    try {
      const threats: string[] = [];
      const warnings: string[] = [];
      const recommendedActions: string[] = [];
      let securityScore = 100;
      let validationsPassed = 0;
      let validationsFailed = 0;

      // Validate required fields
      for (const requiredField of schema.required) {
        if (!(requiredField in data)) {
          threats.push(`Required field missing: ${requiredField}`);
          validationsFailed++;
          securityScore -= 10;
        } else {
          validationsPassed++;
        }
      }

      // Validate field constraints
      for (const field of schema.fields) {
        if (field.name in data) {
          const fieldValidation = await this._validateField(data[field.name], field);
          if (!fieldValidation.isValid) {
            // For schema validation, put format/type errors in warnings and critical errors in threats
            const criticalErrors = fieldValidation.errors.filter(err => 
              err.includes('required') || err.includes('missing')
            );
            const formatErrors = fieldValidation.errors.filter(err => 
              !err.includes('required') && !err.includes('missing')
            );
            
            threats.push(...criticalErrors);
            warnings.push(...formatErrors);
            warnings.push(...fieldValidation.warnings);
            validationsFailed++;
            securityScore -= 5;
          } else {
            validationsPassed++;
          }
        }
      }

      // Apply schema validation rules
      for (const rule of schema.validationRules) {
        if (rule.enabled) {
          const ruleValidation = await this._applyValidationRule(data, rule);
          if (!ruleValidation.isValid) {
            if (rule.severity === 'error') {
              threats.push(rule.errorMessage);
              validationsFailed++;
              securityScore -= 8;
            } else {
              warnings.push(rule.errorMessage);
              securityScore -= 3;
            }
          } else {
            validationsPassed++;
          }
        }
      }

      // Check for additional properties
      if (!schema.additionalProperties) {
        const allowedFields = new Set(schema.fields.map(f => f.name));
        const extraFields = Object.keys(data).filter(key => !allowedFields.has(key));
        if (extraFields.length > 0) {
          warnings.push(`Additional properties not allowed: ${extraFields.join(', ')}`);
          securityScore -= extraFields.length * 2;
        }
      }

      // Check property count limits
      const propertyCount = Object.keys(data).length;
      if (schema.maxProperties && propertyCount > schema.maxProperties) {
        threats.push(`Too many properties: ${propertyCount} > ${schema.maxProperties}`);
        validationsFailed++;
        securityScore -= 15;
      }
      if (schema.minProperties && propertyCount < schema.minProperties) {
        threats.push(`Too few properties: ${propertyCount} < ${schema.minProperties}`);
        validationsFailed++;
        securityScore -= 10;
      }

      // Generate recommendations
      if (threats.length > 0) {
        recommendedActions.push('Fix validation errors before processing');
        recommendedActions.push('Review data schema compliance');
      }
      if (warnings.length > 0) {
        recommendedActions.push('Address validation warnings');
      }

      const result: TSecurityValidationResult = {
        isSecure: threats.length === 0 && validationsFailed === 0,
        threats,
        warnings,
        recommendedActions,
        riskLevel: this._calculateRiskLevel(securityScore),
        scanTimestamp: new Date(),
        validationContext: 'data_schema_validation',
        threatCategories: threats.length > 0 || validationsFailed > 0 ? ['schema_violation'] : [],
        securityScore: Math.max(0, securityScore),
        complianceStatus: threats.length === 0 && validationsFailed === 0 ? 'compliant' : 'non-compliant'
      };

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'data_schema_validated',
        resource: 'data_schema',
        result: result.isSecure ? 'success' : 'warning',
        details: { 
          schemaId: schema.schemaId,
          validationsPassed,
          validationsFailed,
          securityScore: result.securityScore
        },
        riskLevel: result.riskLevel
      });

      return result;
    } catch (error) {
      this.logOperation('data-schema-validation-error', 'Data schema validation failed', error);
      throw error;
    }
  }

  /**
   * Detect injection attempts
   */
  public async detectInjectionAttempts(input: string): Promise<TInjectionThreat[]> {
    try {
      // Handle null/undefined input gracefully
      if (input === null || input === undefined) {
        return [];
      }

      const inputStr = String(input);
      const threats: TInjectionThreat[] = [];

      // Check each injection pattern
      this._injectionPatterns.forEach((pattern, type) => {
        const matches = inputStr.match(pattern);
        if (matches) {
          for (const match of matches) {
            threats.push({
              threatId: crypto.randomUUID(),
              type: type as any,
              pattern: pattern.source,
              severity: this._calculateThreatSeverity(type, match),
              description: `Potential ${type} injection detected: ${match}`,
              detectedAt: new Date(),
              input: match,
              blocked: true,
              confidence: this._calculateConfidence(type, match)
            });
          }
        }
      });

      // Update metrics
      this._inputSecurityMetrics.threatsDetected += threats.length;
      threats.forEach(threat => {
        this._inputSecurityMetrics.threatsByType[threat.type] = 
          (this._inputSecurityMetrics.threatsByType[threat.type] || 0) + 1;
      });

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'injection_detection_performed',
        resource: 'input_validation',
        result: threats.length > 0 ? 'warning' : 'success',
        details: { 
          inputLength: inputStr.length,
          threatsDetected: threats.length,
          threatTypes: Array.from(new Set(threats.map(t => t.type)))
        },
        riskLevel: threats.length > 0 ? 'medium' : 'low'
      });

      return threats;
    } catch (error) {
      this.logOperation('injection-detection-error', 'Injection detection failed', error);
      throw error;
    }
  }

  /**
   * Apply input sanitization
   */
  public async applyInputSanitization(input: string, rules: TSanitizationRule[]): Promise<string> {
    try {
      let sanitizedInput = input;

      // Apply sanitization rules in priority order
      const sortedRules = rules
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);

      for (const rule of sortedRules) {
        const regex = new RegExp(rule.pattern, rule.flags);
        sanitizedInput = sanitizedInput.replace(regex, rule.replacement);
      }

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'input_sanitization_applied',
        resource: 'input_sanitization',
        result: 'success',
        details: { 
          originalLength: input.length,
          sanitizedLength: sanitizedInput.length,
          rulesApplied: sortedRules.length,
          inputChanged: input !== sanitizedInput
        },
        riskLevel: 'low'
      });

      this.logOperation('input-sanitization-applied', 'Input sanitization applied successfully', {
        rulesApplied: sortedRules.length,
        inputChanged: input !== sanitizedInput
      });

      return sanitizedInput;
    } catch (error) {
      this.logOperation('input-sanitization-error', 'Input sanitization failed', error);
      throw error;
    }
  }

  /**
   * Validate input compliance
   */
  public async validateInputCompliance(input: any, rules: TValidationRule[]): Promise<TSecurityValidationResult> {
    try {
      const threats: string[] = [];
      const warnings: string[] = [];
      const recommendedActions: string[] = [];
      let securityScore = 100;
      let validationsPassed = 0;
      let validationsFailed = 0;

      // Apply validation rules
      for (const rule of rules) {
        if (rule.enabled) {
          const ruleValidation = await this._applyValidationRule(input, rule);
          if (ruleValidation.isValid) {
            validationsPassed++;
          } else {
            validationsFailed++;
            if (rule.severity === 'error') {
              threats.push(rule.errorMessage);
              securityScore -= 15;
            } else if (rule.severity === 'warning') {
              warnings.push(rule.errorMessage);
              securityScore -= 5;
            }
          }
        }
      }

      // Generate recommendations
      if (threats.length > 0) {
        recommendedActions.push('Fix compliance violations');
        recommendedActions.push('Review input validation rules');
      }

      const result: TSecurityValidationResult = {
        isSecure: threats.length === 0,
        threats,
        warnings,
        recommendedActions,
        riskLevel: this._calculateRiskLevel(securityScore),
        scanTimestamp: new Date(),
        validationContext: 'input_compliance_validation',
        threatCategories: threats.length > 0 ? ['compliance_violation'] : [],
        securityScore: Math.max(0, securityScore),
        complianceStatus: threats.length === 0 ? 'compliant' : 'non-compliant'
      };

      await this.auditSecurityEvent({
        logId: crypto.randomUUID(),
        timestamp: new Date(),
        eventType: 'data_access',
        action: 'input_compliance_validated',
        resource: 'input_compliance',
        result: result.isSecure ? 'success' : 'warning',
        details: { 
          rulesApplied: rules.filter(r => r.enabled).length,
          validationsPassed,
          validationsFailed,
          securityScore: result.securityScore
        },
        riskLevel: result.riskLevel
      });

      return result;
    } catch (error) {
      this.logOperation('input-compliance-validation-error', 'Input compliance validation failed', error);
      throw error;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /**
   * Initialize security configuration
   */
  private _initializeSecurityConfig(): TSecurityConfig {
    return {
      xssProtection: false, // Not applicable for input validator
      csrfProtection: false, // Not applicable for input validator
      contentSecurityPolicy: false, // Not applicable for input validator
      inputSanitization: true,
      threatDetection: true,
      auditLogging: true,
      maxTokenAge: 0, // Not applicable
      tokenCleanupInterval: 0, // Not applicable
      maxFailedAttempts: 0, // Not applicable
      lockoutDuration: 0, // Not applicable
      securityHeaders: {},
      allowedOrigins: [],
      blockedPatterns: [
        '<script',
        'javascript:',
        'on\\w+\\s*=',
        'eval\\s*\\(',
        'Function\\s*\\(',
        'constructor\\.constructor',
        'document\\.cookie',
        'window\\.location',
        'innerHTML',
        'outerHTML'
      ]
    };
  }

  /**
   * Initialize security metrics
   */
  private _initializeInputSecurityMetrics(): TSecurityMetrics {
    return {
      timestamp: new Date(),
      period: 'hour',
      totalRequests: 0,
      blockedRequests: 0,
      threatsDetected: 0,
      violationsLogged: 0,
      securityScore: 100,
      threatsByType: {},
      violationsByType: {},
      topThreats: [],
      riskTrends: []
    };
  }

  /**
   * Initialize validation rules
   */
  private _initializeValidationRules(): void {
    const rules: TValidationRule[] = [
      {
        ruleId: 'max-length',
        name: 'Maximum Length Check',
        type: 'length',
        parameters: { maxLength: 10000 },
        errorMessage: 'Input exceeds maximum allowed length',
        severity: 'error',
        enabled: true
      },
      {
        ruleId: 'no-script-tags',
        name: 'No Script Tags',
        type: 'pattern',
        parameters: { pattern: '<script[^>]*>', flags: 'gi' },
        errorMessage: 'Script tags are not allowed',
        severity: 'error',
        enabled: true
      },
      {
        ruleId: 'no-javascript-protocol',
        name: 'No JavaScript Protocol',
        type: 'pattern',
        parameters: { pattern: 'javascript:', flags: 'gi' },
        errorMessage: 'JavaScript protocol is not allowed',
        severity: 'error',
        enabled: true
      }
    ];

    rules.forEach(rule => this._validationRules.set(rule.ruleId, rule));
  }

  /**
   * Initialize sanitization rules
   */
  private _initializeSanitizationRules(): void {
    const rules: TSanitizationRule[] = [
      {
        ruleId: 'remove-script-tags',
        name: 'Remove Script Tags',
        type: 'html',
        pattern: '<script[^>]*>.*?</script>',
        replacement: '',
        flags: 'gis',
        enabled: true,
        priority: 1,
        description: 'Remove all script tags'
      },
      {
        ruleId: 'neutralize-javascript-protocol',
        name: 'Neutralize JavaScript Protocol',
        type: 'javascript',
        pattern: 'javascript:',
        replacement: 'javascript-blocked:',
        flags: 'gi',
        enabled: true,
        priority: 2,
        description: 'Neutralize javascript: protocol'
      },
      {
        ruleId: 'remove-event-handlers',
        name: 'Remove Event Handlers',
        type: 'html',
        pattern: 'on\\w+\\s*=',
        replacement: 'on-event-blocked=',
        flags: 'gi',
        enabled: true,
        priority: 3,
        description: 'Remove HTML event handlers'
      },
      {
        ruleId: 'neutralize-eval',
        name: 'Neutralize Eval Function',
        type: 'javascript',
        pattern: 'eval\\s*\\(',
        replacement: 'eval-blocked(',
        flags: 'gi',
        enabled: true,
        priority: 4,
        description: 'Neutralize eval function calls'
      }
    ];

    rules.forEach(rule => this._sanitizationRules.set(rule.ruleId, rule));
  }

  /**
   * Initialize injection patterns
   */
  private _initializeInjectionPatterns(): void {
    this._injectionPatterns.set('sql', /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|UNION|ALTER|CREATE|EXEC)\b)/gi);
    this._injectionPatterns.set('xss', /<script[^>]*>.*?<\/script>|javascript:|on\w+\s*=|eval\s*\(|innerHTML|outerHTML/gi);
    this._injectionPatterns.set('template', /{{.*\b(constructor|process|require|global|__proto__|prototype)\b.*}}/gi);
    this._injectionPatterns.set('command', /(;\s*(rm|del|format|shutdown|reboot|wget|curl|cat|type|echo|ls|dir|ps|kill|killall|chmod|chown|su|sudo|passwd|net\s+user|net\s+localgroup)\b)/gi);
    this._injectionPatterns.set('code', /eval\s*\(|Function\s*\(|setTimeout\s*\(|setInterval\s*\(/gi);
    this._injectionPatterns.set('ldap', /(\(\s*\|\s*\(|\)\s*\)\s*\(|\*\s*\)\s*\()/g);
    this._injectionPatterns.set('xpath', /(\/\/\s*\[|or\s+1\s*=\s*1|and\s+1\s*=\s*1)/gi);
  }

  /**
   * Initialize default schemas
   */
  private _initializeDefaultSchemas(): void {
    const templateSchema: TDataSchema = {
      schemaId: 'template-input-schema',
      version: '1.0.0',
      fields: [
        {
          name: 'templateId',
          type: 'string',
          required: true,
          minLength: 1,
          maxLength: 100,
          pattern: '^[a-zA-Z0-9_-]+$',
          description: 'Template identifier'
        },
        {
          name: 'data',
          type: 'object',
          required: true,
          description: 'Template data context'
        }
      ],
      required: ['templateId', 'data'],
      additionalProperties: false,
      maxProperties: 10,
      validationRules: Array.from(this._validationRules.values())
    };

    this._schemas.set(templateSchema.schemaId, templateSchema);
  }

  /**
   * Sanitization helpers
   */
  private async _sanitizeString(input: string, rules: TSanitizationRule[]): Promise<string> {
    let sanitized = input;
    
    const enabledRules = rules
      .filter(rule => rule.enabled)
      .sort((a, b) => a.priority - b.priority);

    for (const rule of enabledRules) {
      const regex = new RegExp(rule.pattern, rule.flags);
      sanitized = sanitized.replace(regex, rule.replacement);
    }

    return sanitized;
  }

  private async _sanitizeObject(input: any, rules: TSanitizationRule[]): Promise<any> {
    const sanitized: any = {};
    
    for (const [key, value] of Object.entries(input)) {
      if (typeof value === 'string') {
        sanitized[key] = await this._sanitizeString(value, rules);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = await this._sanitizeObject(value, rules);
      } else if (Array.isArray(value)) {
        sanitized[key] = await this._sanitizeArray(value, rules);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  private async _sanitizeArray(input: any[], rules: TSanitizationRule[]): Promise<any[]> {
    const sanitizedArray: any[] = [];
    
    for (const item of input) {
      if (typeof item === 'string') {
        sanitizedArray.push(await this._sanitizeString(item, rules));
      } else if (typeof item === 'object' && item !== null) {
        sanitizedArray.push(await this._sanitizeObject(item, rules));
      } else if (Array.isArray(item)) {
        sanitizedArray.push(await this._sanitizeArray(item, rules));
      } else {
        sanitizedArray.push(item);
      }
    }
    return sanitizedArray;
  }

  /**
   * Validation helpers
   */
  private async _validateField(value: any, field: TSchemaField): Promise<{isValid: boolean, errors: string[], warnings: string[]}> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Type validation
    if (!this._validateFieldType(value, field.type)) {
      errors.push(`Field ${field.name} must be of type ${field.type}`);
    }

    // String-specific validations
    if (field.type === 'string' && typeof value === 'string') {
      if (field.minLength && value.length < field.minLength) {
        errors.push(`Field ${field.name} must be at least ${field.minLength} characters`);
      }
      if (field.maxLength && value.length > field.maxLength) {
        errors.push(`Field ${field.name} must not exceed ${field.maxLength} characters`);
      }
      if (field.pattern && !new RegExp(field.pattern).test(value)) {
        errors.push(`Field ${field.name} does not match required pattern`);
      }
    }

    // Enum validation
    if (field.enum && !field.enum.includes(value)) {
      errors.push(`Field ${field.name} must be one of: ${field.enum.join(', ')}`);
    }

    // Apply field-specific validation rules
    if (field.validation) {
      for (const rule of field.validation) {
        if (rule.enabled) {
          const ruleValidation = await this._applyValidationRule(value, rule);
          if (!ruleValidation.isValid) {
            if (rule.severity === 'error') {
              errors.push(rule.errorMessage);
            } else {
              warnings.push(rule.errorMessage);
            }
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  private _validateFieldType(value: any, type: string): boolean {
    switch (type) {
      case 'string': return typeof value === 'string';
      case 'number': return typeof value === 'number' && !isNaN(value);
      case 'boolean': return typeof value === 'boolean';
      case 'array': return Array.isArray(value);
      case 'object': return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'date': return value instanceof Date || !isNaN(Date.parse(value));
      case 'email': return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'url': return typeof value === 'string' && /^https?:\/\/.+/.test(value);
      default: return true;
    }
  }

  private async _applyValidationRule(data: any, rule: TValidationRule): Promise<{isValid: boolean}> {
    try {
      switch (rule.type) {
        case 'length':
          return this._validateLength(data, rule.parameters);
        case 'pattern':
          return this._validatePattern(data, rule.parameters);
        case 'range':
          return this._validateRange(data, rule.parameters);
        case 'format':
          return this._validateFormat(data, rule.parameters);
        case 'custom':
          return this._validateCustom(data, rule.parameters);
        default:
          return { isValid: true };
      }
    } catch (error) {
      this.logOperation('validation-rule-error', 'Validation rule application failed', { ruleId: rule.ruleId, error });
      return { isValid: false };
    }
  }

  private _validateLength(data: any, params: any): {isValid: boolean} {
    const str = String(data);
    if (params.minLength && str.length < params.minLength) return { isValid: false };
    if (params.maxLength && str.length > params.maxLength) return { isValid: false };
    return { isValid: true };
  }

  private _validatePattern(data: any, params: any): {isValid: boolean} {
    const str = String(data);
    const regex = new RegExp(params.pattern, params.flags || '');
    return { isValid: regex.test(str) };
  }

  private _validateRange(data: any, params: any): {isValid: boolean} {
    const num = Number(data);
    if (isNaN(num)) return { isValid: false };
    if (params.min !== undefined && num < params.min) return { isValid: false };
    if (params.max !== undefined && num > params.max) return { isValid: false };
    return { isValid: true };
  }

  private _validateFormat(data: any, params: any): {isValid: boolean} {
    const str = String(data);
    switch (params.format) {
      case 'email':
        return { isValid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(str) };
      case 'url':
        return { isValid: /^https?:\/\/.+/.test(str) };
      case 'date':
        return { isValid: !isNaN(Date.parse(str)) };
      default:
        return { isValid: true };
    }
  }

  private _validateCustom(data: any, params: any): {isValid: boolean} {
    // Custom validation logic would be implemented here
    return { isValid: true };
  }

  /**
   * Utility methods
   */
  private _calculateRiskLevel(securityScore: number): 'low' | 'medium' | 'high' | 'critical' {
    if (securityScore >= 80) return 'low';
    if (securityScore >= 60) return 'medium';
    if (securityScore >= 40) return 'high';
    return 'critical';
  }

  private _calculateThreatSeverity(type: string, match: string): 'low' | 'medium' | 'high' | 'critical' {
    const highRiskTypes = ['sql', 'xss', 'code'];
    const mediumRiskTypes = ['template', 'command'];
    
    if (highRiskTypes.includes(type)) return 'high';
    if (mediumRiskTypes.includes(type)) return 'medium';
    return 'low';
  }

  private _calculateConfidence(type: string, match: string): number {
    const exactMatches = ['SELECT', 'INSERT', '<script', 'eval('];
    if (exactMatches.some(exact => match.toUpperCase().includes(exact.toUpperCase()))) {
      return 95;
    }
    return 75;
  }

  private async _calculateSecurityScore(): Promise<number> {
    const baseScore = 100;
    const threatPenalty = this._inputSecurityMetrics.threatsDetected * 2;
    const violationPenalty = this._inputSecurityMetrics.violationsLogged * 3;
    const complianceBonus = (await this.validateSecurityCompliance()) ? 10 : 0;
    const rulesBonus = Math.min(10, (this._validationRules.size + this._sanitizationRules.size) * 0.5);

    return Math.max(0, Math.min(100, baseScore - threatPenalty - violationPenalty + complianceBonus + rulesBonus));
  }

  /**
   * Extract template variables from template string
   */
  private _extractTemplateVariables(template: string): Set<string> {
    const variables = new Set<string>();
    const regex = /\{\{\s*([^}]+)\s*\}\}/g;
    let match;
    
    while ((match = regex.exec(template)) !== null) {
      const varName = match[1].trim();
      // Handle nested properties like user.name, user.email
      variables.add(varName);
    }
    
    return variables;
  }

  /**
   * Log operation with context
   */
  protected logOperation(operation: string, message: string, data?: any): void {
    const logData = {
      operation,
      message,
      componentId: this._componentId,
      timestamp: new Date(),
      data,
    };
    
    // In real implementation, would use proper logging framework
    console.log(`[${this._componentId}] ${message}`, logData);
  }
} 